const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const WorkerForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  // تحديد ما إذا كان النموذج للتعديل أو للإضافة
  const isEditMode = !!id;
  
  // حالة النموذج
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    phone: '',
    address: '',
    fee_per_order: '',
    monthly_salary: '',
    notes: ''
  });
  
  const [roles, setRoles] = useState(['نجار', 'مصمم', 'دهان', 'منجد', 'فني تركيب', 'عامل']);
  const [loading, setLoading] = useState(isEditMode);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  // تحميل بيانات العامل في حالة التعديل
  useEffect(() => {
    const fetchWorker = async () => {
      if (!isEditMode) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const workerData = await window.api.workers.getById(id);
        if (!workerData) {
          throw new Error('لم يتم العثور على العامل');
        }
        
        setFormData({
          name: workerData.name || '',
          role: workerData.role || '',
          phone: workerData.phone || '',
          address: workerData.address || '',
          fee_per_order: workerData.fee_per_order ? workerData.fee_per_order.toString() : '',
          monthly_salary: workerData.monthly_salary ? workerData.monthly_salary.toString() : '',
          notes: workerData.notes || ''
        });
        
        // إضافة الدور إلى القائمة إذا لم يكن موجودًا
        if (workerData.role && !roles.includes(workerData.role)) {
          setRoles(prev => [...prev, workerData.role]);
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات العامل:', error);
        setError('حدث خطأ أثناء تحميل بيانات العامل. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchWorker();
  }, [id, isEditMode]);
  
  // التحقق من الصلاحيات
  useEffect(() => {
    if (isEditMode && !hasPermission('workers_edit')) {
      alert('ليس لديك صلاحية لتعديل العمال');
      navigate('/workers');
    } else if (!isEditMode && !hasPermission('workers_create')) {
      alert('ليس لديك صلاحية لإضافة عمال جدد');
      navigate('/workers');
    }
  }, [isEditMode, hasPermission, navigate]);
  
  // معالجة تغيير قيم الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // معالجة تغيير القيم الرقمية (للتأكد من أنها أرقام)
  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    if (value === '' || /^\d+(\.\d{0,2})?$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // إضافة دور جديد
  const handleAddRole = () => {
    const newRole = prompt('أدخل الدور الجديد:');
    if (newRole && newRole.trim() && !roles.includes(newRole.trim())) {
      setRoles([...roles, newRole.trim()]);
      setFormData(prev => ({
        ...prev,
        role: newRole.trim()
      }));
    }
  };
  
  // التحقق من صحة البيانات
  const validateForm = () => {
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم العامل');
      return false;
    }
    
    // التحقق من صحة رقم الهاتف (اختياري)
    if (formData.phone && !/^[0-9+\s-]{8,15}$/.test(formData.phone)) {
      alert('يرجى إدخال رقم هاتف صحيح');
      return false;
    }
    
    return true;
  };
  
  // حفظ البيانات
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setSubmitting(true);
      setError(null);
      
      // تحويل القيم الرقمية
      const workerData = {
        ...formData,
        fee_per_order: formData.fee_per_order ? parseFloat(formData.fee_per_order) : null,
        monthly_salary: formData.monthly_salary ? parseFloat(formData.monthly_salary) : null
      };
      
      let result;
      
      if (isEditMode) {
        // تعديل العامل
        result = await window.api.workers.update(id, workerData);
        if (result.success) {
          alert('تم تعديل العامل بنجاح');
          navigate(`/workers/${id}`);
        } else {
          throw new Error('فشل في تعديل العامل');
        }
      } else {
        // إضافة عامل جديد
        result = await window.api.workers.create(workerData);
        if (result.success) {
          alert('تم إضافة العامل بنجاح');
          navigate(`/workers/${result.id}`);
        } else {
          throw new Error('فشل في إضافة العامل');
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ بيانات العامل:', error);
      setError('حدث خطأ أثناء حفظ بيانات العامل. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  return (
    <div className="worker-form-page">
      <div className="page-header">
        <h2>{isEditMode ? 'تعديل بيانات العامل' : 'إضافة عامل جديد'}</h2>
        <div className="page-actions">
          <Link to={isEditMode ? `/workers/${id}` : '/workers'} className="btn btn-secondary">
            <i className="fas fa-times"></i> إلغاء
          </Link>
        </div>
      </div>
      
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="card">
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">اسم العامل <span className="text-danger">*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">الدور</label>
                  <div className="input-group">
                    <select
                      className="form-control"
                      name="role"
                      value={formData.role}
                      onChange={handleChange}
                    >
                      <option value="">اختر الدور</option>
                      {roles.map((role, index) => (
                        <option key={index} value={role}>{role}</option>
                      ))}
                    </select>
                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={handleAddRole}
                    >
                      <i className="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">رقم الهاتف</label>
                  <input
                    type="text"
                    className="form-control"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="05xxxxxxxx"
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">العنوان</label>
                  <input
                    type="text"
                    className="form-control"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">أجر الطلب (ر.س)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="fee_per_order"
                    value={formData.fee_per_order}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">الراتب الشهري (ر.س)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="monthly_salary"
                    value={formData.monthly_salary}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                </div>
              </div>
            </div>
            
            <div className="form-group mb-3">
              <label className="form-label">ملاحظات</label>
              <textarea
                className="form-control"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows="3"
              ></textarea>
            </div>
            
            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span className="ms-1">جاري الحفظ...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    <span className="ms-1">{isEditMode ? 'حفظ التعديلات' : 'إضافة العامل'}</span>
                  </>
                )}
              </button>
              
              <Link to={isEditMode ? `/workers/${id}` : '/workers'} className="btn btn-secondary">
                <i className="fas fa-times"></i> إلغاء
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = WorkerForm;
