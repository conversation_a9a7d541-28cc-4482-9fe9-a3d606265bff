const React = require('react');
const { useState, useEffect } = React;

const MaterialsReport = () => {
  const [materials, setMaterials] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMaterialsData();
  }, []);

  const loadMaterialsData = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات
      const mockMaterials = [
        {
          id: 1,
          name: 'خشب زان',
          currentStock: 50,
          usedQuantity: 200,
          totalCost: 15000
        },
        {
          id: 2,
          name: 'مسامير',
          currentStock: 1000,
          usedQuantity: 5000,
          totalCost: 2500
        }
      ];
      setMaterials(mockMaterials);
    } catch (error) {
      console.error('خطأ في تحميل تقرير المواد:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل التقرير...');
  }

  return React.createElement('div', { className: 'materials-report' },
    React.createElement('h1', null, 'تقرير استهلاك المواد'),
    React.createElement('table', { className: 'table' },
      React.createElement('thead', null,
        React.createElement('tr', null,
          React.createElement('th', null, 'اسم المادة'),
          React.createElement('th', null, 'المخزون الحالي'),
          React.createElement('th', null, 'الكمية المستهلكة'),
          React.createElement('th', null, 'إجمالي التكلفة')
        )
      ),
      React.createElement('tbody', null,
        materials.map(material =>
          React.createElement('tr', { key: material.id },
            React.createElement('td', null, material.name),
            React.createElement('td', null, material.currentStock),
            React.createElement('td', null, material.usedQuantity),
            React.createElement('td', null, `${material.totalCost.toLocaleString()} ر.س`)
          )
        )
      )
    )
  );
};

module.exports = MaterialsReport;
