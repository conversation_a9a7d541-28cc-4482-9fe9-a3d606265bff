/* إصلاح سريع لتخطيط التطبيق */

/* إعادة تعيين أساسية */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f8f9fa;
  color: #2c3e50;
  line-height: 1.6;
  direction: rtl;
  min-height: 100vh;
}

/* إخفاء الشريط الجانبي القديم */
.sidebar {
  display: none !important;
}

/* تخطيط التطبيق الرئيسي */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* الشريط العلوي */
header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 0.75rem 0;
  box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  text-decoration: none;
  font-size: 1.25rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  color: #ff6b35;
  text-decoration: none;
}

.navbar-brand i {
  font-size: 1.5rem;
  color: #ff6b35;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* قائمة الإشعارات */
.notifications-menu {
  position: relative;
}

.notifications-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.notifications-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notifications-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff6b35;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  margin-top: 0.5rem;
  overflow: hidden;
  z-index: 1001;
  color: #2c3e50;
}

.notifications-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-header h5 {
  margin: 0;
  font-weight: 600;
}

.mark-all-read {
  background: none;
  border: none;
  color: #1e3c72;
  cursor: pointer;
  font-size: 0.8rem;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: background 0.2s ease;
}

.notification-item:hover {
  background: #f8f9fa;
}

.notification-item.unread {
  background: rgba(30, 60, 114, 0.05);
  border-left: 3px solid #1e3c72;
}

.no-notifications {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

/* قائمة المستخدم */
.user-menu {
  position: relative;
}

.user-menu-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.user-menu-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  margin-top: 0.5rem;
  overflow: hidden;
  z-index: 1001;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #2c3e50;
  text-decoration: none;
  transition: all 0.15s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: right;
  cursor: pointer;
  font-size: 0.875rem;
}

.user-menu-item:hover {
  background: #f8f9fa;
  color: #1e3c72;
  text-decoration: none;
}

.logout-button {
  border-top: 1px solid #e9ecef;
  color: #e74c3c;
}

.logout-button:hover {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

/* شريط التنقل الرئيسي */
.main-navigation {
  background: white;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow-x: auto;
  padding: 0.5rem 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-menu::-webkit-scrollbar {
  display: none;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #6c757d;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  position: relative;
  min-width: fit-content;
}

.nav-item:hover {
  background: rgba(30, 60, 114, 0.1);
  color: #1e3c72;
  text-decoration: none;
}

.nav-item.active {
  background: #1e3c72;
  color: white;
  box-shadow: 0 2px 8px rgba(30, 60, 114, 0.3);
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: #ff6b35;
  border-radius: 50%;
}

.nav-item i {
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

/* منطقة المحتوى */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  min-height: calc(100vh - 140px);
}

/* إخفاء أي عناصر جانبية قديمة */
.sidebar,
.side-menu,
.left-sidebar,
.right-sidebar {
  display: none !important;
}

/* التأكد من أن المحتوى يأخذ العرض الكامل */
.container-fluid,
.main-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 0.75rem;
  }
  
  .navbar-brand {
    font-size: 1.125rem;
  }
  
  .nav-container {
    padding: 0 0.75rem;
  }
  
  .nav-menu {
    gap: 0.25rem;
  }
  
  .nav-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .navbar-menu {
    gap: 0.5rem;
  }
  
  .user-menu-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
}
