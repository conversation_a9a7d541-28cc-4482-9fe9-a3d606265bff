{"version": 3, "file": "react-router-dom.development.js", "sources": ["../dom.ts", "../index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_logV6DeprecationWarnings as logV6DeprecationWarnings,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [future, router.future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n"], "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "REACT_ROUTER_VERSION", "window", "__reactRouterVersion", "createBrowserRouter", "routes", "opts", "createRouter", "future", "v7_prependBasename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "mapRouteProperties", "dataStrategy", "patchRoutesOnNavigation", "initialize", "createHashRouter", "createHashHistory", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "React", "createContext", "isTransitioning", "displayName", "FetchersContext", "Map", "START_TRANSITION", "startTransitionImpl", "FLUSH_SYNC", "flushSyncImpl", "ReactDOM", "USE_ID", "useIdImpl", "startTransitionSafe", "cb", "flushSyncSafe", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "deletedFetchers", "flushSync", "viewTransitionOpts", "fetchers", "fetcher", "current", "set", "delete", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "location", "v7_partialHydration", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "logV6DeprecationWarnings", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "MemoizedDataRoutes", "DataRoutes", "useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "historyRef", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HistoryRouter", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "onClick", "relative", "reloadDocument", "viewTransition", "rest", "ref", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "assign", "NavLink", "NavLinkWithRef", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "fetcher<PERSON>ey", "onSubmit", "props", "forwardedRef", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "invariant", "useDataRouterState", "replaceProp", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "fetcherId", "getUniqueFetcherId", "String", "UseSubmit", "currentRouteId", "useRouteId", "options", "fetch", "formEncType", "fromRouteId", "routeContext", "RouteContext", "match", "matches", "slice", "params", "indexValues", "hasNakedIndexParam", "some", "qs", "toString", "route", "index", "joinPaths", "useFetcher", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "get", "IDLE_FETCHER", "fetcherWithComponents", "useFetchers", "UseFetchers", "from", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "when", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "currentPath", "nextPath", "matchPath"], "mappings": ";;;;;;;;;;;;;;;;;AAOO,MAAMA,aAA6B,GAAG,KAAK,CAAA;AAClD,MAAMC,cAA2B,GAAG,mCAAmC,CAAA;AAEhE,SAASC,aAAaA,CAACC,MAAW,EAAyB;EAChE,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,CAAA;AAC7D,CAAA;AAEO,SAASC,eAAeA,CAACF,MAAW,EAA+B;AACxE,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,QAAQ,CAAA;AAC3E,CAAA;AAEO,SAASC,aAAaA,CAACJ,MAAW,EAA6B;AACpE,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,MAAM,CAAA;AACzE,CAAA;AAEO,SAASE,cAAcA,CAACL,MAAW,EAA8B;AACtE,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,OAAO,CAAA;AAC1E,CAAA;AAOA,SAASG,eAAeA,CAACC,KAAwB,EAAE;AACjD,EAAA,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,QAAQ,CAAC,CAAA;AAC7E,CAAA;AAEO,SAASC,sBAAsBA,CACpCL,KAAwB,EACxBM,MAAe,EACf;AACA,EAAA,OACEN,KAAK,CAACO,MAAM,KAAK,CAAC;AAAI;AACrB,EAAA,CAACD,MAAM,IAAIA,MAAM,KAAK,OAAO,CAAC;AAAI;AACnC,EAAA,CAACP,eAAe,CAACC,KAAK,CAAC;AAAC,GAAA;AAE5B,CAAA;;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASQ,kBAAkBA,CAChCC,IAAyB,GAAG,EAAE,EACb;AACjB,EAAA,OAAO,IAAIC,eAAe,CACxB,OAAOD,IAAI,KAAK,QAAQ,IACxBE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IACnBA,IAAI,YAAYC,eAAe,GAC3BD,IAAI,GACJI,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;AACtC,IAAA,IAAIC,KAAK,GAAGT,IAAI,CAACQ,GAAG,CAAC,CAAA;AACrB,IAAA,OAAOD,IAAI,CAACG,MAAM,CAChBR,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,CAAEC,CAAC,IAAK,CAACJ,GAAG,EAAEI,CAAC,CAAC,CAAC,GAAG,CAAC,CAACJ,GAAG,EAAEC,KAAK,CAAC,CACnE,CAAC,CAAA;GACF,EAAE,EAAyB,CAClC,CAAC,CAAA;AACH,CAAA;AAEO,SAASI,0BAA0BA,CACxCC,cAAsB,EACtBC,mBAA2C,EAC3C;AACA,EAAA,IAAIC,YAAY,GAAGjB,kBAAkB,CAACe,cAAc,CAAC,CAAA;AAErD,EAAA,IAAIC,mBAAmB,EAAE;AACvB;AACA;AACA;AACA;AACA;AACAA,IAAAA,mBAAmB,CAACE,OAAO,CAAC,CAACC,CAAC,EAAEV,GAAG,KAAK;AACtC,MAAA,IAAI,CAACQ,YAAY,CAACG,GAAG,CAACX,GAAG,CAAC,EAAE;QAC1BO,mBAAmB,CAACK,MAAM,CAACZ,GAAG,CAAC,CAACS,OAAO,CAAER,KAAK,IAAK;AACjDO,UAAAA,YAAY,CAACK,MAAM,CAACb,GAAG,EAAEC,KAAK,CAAC,CAAA;AACjC,SAAC,CAAC,CAAA;AACJ,OAAA;AACF,KAAC,CAAC,CAAA;AACJ,GAAA;AAEA,EAAA,OAAOO,YAAY,CAAA;AACrB,CAAA;;AAEA;;AAiBA;AACA,IAAIM,0BAA0C,GAAG,IAAI,CAAA;AAErD,SAASC,4BAA4BA,GAAG;EACtC,IAAID,0BAA0B,KAAK,IAAI,EAAE;IACvC,IAAI;AACF,MAAA,IAAIE,QAAQ,CACVC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;AAC9B;AACA,MAAA,CACF,CAAC,CAAA;AACDJ,MAAAA,0BAA0B,GAAG,KAAK,CAAA;KACnC,CAAC,OAAOK,CAAC,EAAE;AACVL,MAAAA,0BAA0B,GAAG,IAAI,CAAA;AACnC,KAAA;AACF,GAAA;AACA,EAAA,OAAOA,0BAA0B,CAAA;AACnC,CAAA;;AAEA;AACA;AACA;;AAuCA;AACA;AACA;;AAGA;AACA;AACA;;AA8BA,MAAMM,qBAAuC,GAAG,IAAIC,GAAG,CAAC,CACtD,mCAAmC,EACnC,qBAAqB,EACrB,YAAY,CACb,CAAC,CAAA;AAEF,SAASC,cAAcA,CAACC,OAAsB,EAAE;EAC9C,IAAIA,OAAO,IAAI,IAAI,IAAI,CAACH,qBAAqB,CAACT,GAAG,CAACY,OAAsB,CAAC,EAAE;AACzEC,IAAAC,cAAO,CACL,KAAK,EACJ,CAAA,CAAA,EAAGF,OAAQ,CAAgE,+DAAA,CAAA,GACzE,CAAuBjD,qBAAAA,EAAAA,cAAe,GAC3C,CAAC,CAAA,CAAA;AAED,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACA,EAAA,OAAOiD,OAAO,CAAA;AAChB,CAAA;AAEO,SAASG,qBAAqBA,CACnCrC,MAAoB,EACpBsC,QAAgB,EAOhB;AACA,EAAA,IAAIC,MAAc,CAAA;AAClB,EAAA,IAAIC,MAAqB,CAAA;AACzB,EAAA,IAAIN,OAAe,CAAA;AACnB,EAAA,IAAIO,QAA8B,CAAA;AAClC,EAAA,IAAIC,IAAS,CAAA;AAEb,EAAA,IAAInD,aAAa,CAACS,MAAM,CAAC,EAAE;AACzB;AACA;AACA;AACA,IAAA,IAAI2C,IAAI,GAAG3C,MAAM,CAAC4C,YAAY,CAAC,QAAQ,CAAC,CAAA;IACxCJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI,CAAA;IACpDC,MAAM,GAAGvC,MAAM,CAAC4C,YAAY,CAAC,QAAQ,CAAC,IAAI5D,aAAa,CAAA;IACvDkD,OAAO,GAAGD,cAAc,CAACjC,MAAM,CAAC4C,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI3D,cAAc,CAAA;AAE1EwD,IAAAA,QAAQ,GAAG,IAAId,QAAQ,CAAC3B,MAAM,CAAC,CAAA;GAChC,MAAM,IACLX,eAAe,CAACW,MAAM,CAAC,IACtBR,cAAc,CAACQ,MAAM,CAAC,KACpBA,MAAM,CAAC8C,IAAI,KAAK,QAAQ,IAAI9C,MAAM,CAAC8C,IAAI,KAAK,OAAO,CAAE,EACxD;AACA,IAAA,IAAIC,IAAI,GAAG/C,MAAM,CAAC+C,IAAI,CAAA;IAEtB,IAAIA,IAAI,IAAI,IAAI,EAAE;AAChB,MAAA,MAAM,IAAIC,KAAK,CACZ,CAAA,kEAAA,CACH,CAAC,CAAA;AACH,KAAA;;AAEA;;AAEA;AACA;AACA;AACA,IAAA,IAAIL,IAAI,GAAG3C,MAAM,CAAC4C,YAAY,CAAC,YAAY,CAAC,IAAIG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,CAAA;IAC3EJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI,CAAA;AAEpDC,IAAAA,MAAM,GACJvC,MAAM,CAAC4C,YAAY,CAAC,YAAY,CAAC,IACjCG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,IAC3B5D,aAAa,CAAA;IACfkD,OAAO,GACLD,cAAc,CAACjC,MAAM,CAAC4C,YAAY,CAAC,aAAa,CAAC,CAAC,IAClDX,cAAc,CAACc,IAAI,CAACH,YAAY,CAAC,SAAS,CAAC,CAAC,IAC5C3D,cAAc,CAAA;;AAEhB;AACAwD,IAAAA,QAAQ,GAAG,IAAId,QAAQ,CAACoB,IAAI,EAAE/C,MAAM,CAAC,CAAA;;AAErC;AACA;AACA;AACA;AACA,IAAA,IAAI,CAAC0B,4BAA4B,EAAE,EAAE;MACnC,IAAI;QAAEuB,IAAI;QAAEH,IAAI;AAAElC,QAAAA,KAAAA;AAAM,OAAC,GAAGZ,MAAM,CAAA;MAClC,IAAI8C,IAAI,KAAK,OAAO,EAAE;QACpB,IAAII,MAAM,GAAGD,IAAI,GAAI,GAAEA,IAAK,CAAA,CAAA,CAAE,GAAG,EAAE,CAAA;QACnCR,QAAQ,CAACjB,MAAM,CAAE,CAAA,EAAE0B,MAAO,CAAE,CAAA,CAAA,EAAE,GAAG,CAAC,CAAA;QAClCT,QAAQ,CAACjB,MAAM,CAAE,CAAA,EAAE0B,MAAO,CAAE,CAAA,CAAA,EAAE,GAAG,CAAC,CAAA;OACnC,MAAM,IAAID,IAAI,EAAE;AACfR,QAAAA,QAAQ,CAACjB,MAAM,CAACyB,IAAI,EAAErC,KAAK,CAAC,CAAA;AAC9B,OAAA;AACF,KAAA;AACF,GAAC,MAAM,IAAI1B,aAAa,CAACc,MAAM,CAAC,EAAE;AAChC,IAAA,MAAM,IAAIgD,KAAK,CACZ,CAAwD,uDAAA,CAAA,GACtD,6BACL,CAAC,CAAA;AACH,GAAC,MAAM;AACLT,IAAAA,MAAM,GAAGvD,aAAa,CAAA;AACtBwD,IAAAA,MAAM,GAAG,IAAI,CAAA;AACbN,IAAAA,OAAO,GAAGjD,cAAc,CAAA;AACxByD,IAAAA,IAAI,GAAG1C,MAAM,CAAA;AACf,GAAA;;AAEA;AACA,EAAA,IAAIyC,QAAQ,IAAIP,OAAO,KAAK,YAAY,EAAE;AACxCQ,IAAAA,IAAI,GAAGD,QAAQ,CAAA;AACfA,IAAAA,QAAQ,GAAGU,SAAS,CAAA;AACtB,GAAA;EAEA,OAAO;IAAEX,MAAM;AAAED,IAAAA,MAAM,EAAEA,MAAM,CAACjD,WAAW,EAAE;IAAE4C,OAAO;IAAEO,QAAQ;AAAEC,IAAAA,IAAAA;GAAM,CAAA;AAC1E;;ACrVA;AACA;AACA;AACA;AAiOA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,MAAAU,oBAAA,GAAA,GAAA,CAAA;AAEA,IAAI;EACFC,MAAM,CAACC,oBAAoB,GAAGF,oBAAoB,CAAA;AACpD,CAAC,CAAC,OAAOtB,CAAC,EAAE;AACV;AAAA,CAAA;;AAGF;AACA;AACA;AAWO,SAASyB,mBAAmBA,CACjCC,MAAqB,EACrBC,IAAoB,EACP;AACb,EAAA,OAAOC,YAAY,CAAC;IAClBpB,QAAQ,EAAEmB,IAAI,EAAEnB,QAAQ;AACxBqB,IAAAA,MAAM,EAAE;MACN,GAAGF,IAAI,EAAEE,MAAM;AACfC,MAAAA,kBAAkB,EAAE,IAAA;KACrB;IACDC,OAAO,EAAEC,oBAAoB,CAAC;MAAET,MAAM,EAAEI,IAAI,EAAEJ,MAAAA;AAAO,KAAC,CAAC;AACvDU,IAAAA,aAAa,EAAEN,IAAI,EAAEM,aAAa,IAAIC,kBAAkB,EAAE;IAC1DR,MAAM;wBACNS,yBAAkB;IAClBC,YAAY,EAAET,IAAI,EAAES,YAAY;IAChCC,uBAAuB,EAAEV,IAAI,EAAEU,uBAAuB;IACtDd,MAAM,EAAEI,IAAI,EAAEJ,MAAAA;AAChB,GAAC,CAAC,CAACe,UAAU,EAAE,CAAA;AACjB,CAAA;AAEO,SAASC,gBAAgBA,CAC9Bb,MAAqB,EACrBC,IAAoB,EACP;AACb,EAAA,OAAOC,YAAY,CAAC;IAClBpB,QAAQ,EAAEmB,IAAI,EAAEnB,QAAQ;AACxBqB,IAAAA,MAAM,EAAE;MACN,GAAGF,IAAI,EAAEE,MAAM;AACfC,MAAAA,kBAAkB,EAAE,IAAA;KACrB;IACDC,OAAO,EAAES,iBAAiB,CAAC;MAAEjB,MAAM,EAAEI,IAAI,EAAEJ,MAAAA;AAAO,KAAC,CAAC;AACpDU,IAAAA,aAAa,EAAEN,IAAI,EAAEM,aAAa,IAAIC,kBAAkB,EAAE;IAC1DR,MAAM;wBACNS,yBAAkB;IAClBC,YAAY,EAAET,IAAI,EAAES,YAAY;IAChCC,uBAAuB,EAAEV,IAAI,EAAEU,uBAAuB;IACtDd,MAAM,EAAEI,IAAI,EAAEJ,MAAAA;AAChB,GAAC,CAAC,CAACe,UAAU,EAAE,CAAA;AACjB,CAAA;AAEA,SAASJ,kBAAkBA,GAA+B;AACxD,EAAA,IAAIO,KAAK,GAAGlB,MAAM,EAAEmB,2BAA2B,CAAA;AAC/C,EAAA,IAAID,KAAK,IAAIA,KAAK,CAACE,MAAM,EAAE;AACzBF,IAAAA,KAAK,GAAG;AACN,MAAA,GAAGA,KAAK;AACRE,MAAAA,MAAM,EAAEC,iBAAiB,CAACH,KAAK,CAACE,MAAM,CAAA;KACvC,CAAA;AACH,GAAA;AACA,EAAA,OAAOF,KAAK,CAAA;AACd,CAAA;AAEA,SAASG,iBAAiBA,CACxBD,MAAsC,EACN;AAChC,EAAA,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI,CAAA;AACxB,EAAA,IAAIE,OAAO,GAAGpE,MAAM,CAACoE,OAAO,CAACF,MAAM,CAAC,CAAA;EACpC,IAAIG,UAA0C,GAAG,EAAE,CAAA;EACnD,KAAK,IAAI,CAACjE,GAAG,EAAEkE,GAAG,CAAC,IAAIF,OAAO,EAAE;AAC9B;AACA;AACA,IAAA,IAAIE,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAACjE,GAAG,CAAC,GAAG,IAAIoE,wBAAiB,CACrCF,GAAG,CAACG,MAAM,EACVH,GAAG,CAACI,UAAU,EACdJ,GAAG,CAACK,IAAI,EACRL,GAAG,CAACM,QAAQ,KAAK,IACnB,CAAC,CAAA;KACF,MAAM,IAAIN,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;AACxC;MACA,IAAID,GAAG,CAACO,SAAS,EAAE;AACjB,QAAA,IAAIC,gBAAgB,GAAGhC,MAAM,CAACwB,GAAG,CAACO,SAAS,CAAC,CAAA;AAC5C,QAAA,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;AACF;YACA,IAAIC,KAAK,GAAG,IAAID,gBAAgB,CAACR,GAAG,CAACU,OAAO,CAAC,CAAA;AAC7C;AACA;YACAD,KAAK,CAACE,KAAK,GAAG,EAAE,CAAA;AAChBZ,YAAAA,UAAU,CAACjE,GAAG,CAAC,GAAG2E,KAAK,CAAA;WACxB,CAAC,OAAOxD,CAAC,EAAE;AACV;AAAA,WAAA;AAEJ,SAAA;AACF,OAAA;AAEA,MAAA,IAAI8C,UAAU,CAACjE,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAI2E,KAAK,GAAG,IAAItC,KAAK,CAAC6B,GAAG,CAACU,OAAO,CAAC,CAAA;AAClC;AACA;QACAD,KAAK,CAACE,KAAK,GAAG,EAAE,CAAA;AAChBZ,QAAAA,UAAU,CAACjE,GAAG,CAAC,GAAG2E,KAAK,CAAA;AACzB,OAAA;AACF,KAAC,MAAM;AACLV,MAAAA,UAAU,CAACjE,GAAG,CAAC,GAAGkE,GAAG,CAAA;AACvB,KAAA;AACF,GAAA;AACA,EAAA,OAAOD,UAAU,CAAA;AACnB,CAAA;;AAEA;;AAEA;AACA;AACA;AAaA,MAAMa,qBAAqB,gBAAGC,KAAK,CAACC,aAAa,CAA8B;AAC7EC,EAAAA,eAAe,EAAE,KAAA;AACnB,CAAC,EAAC;AACW;EACXH,qBAAqB,CAACI,WAAW,GAAG,gBAAgB,CAAA;AACtD,CAAA;;AAIA;;AAGMC,MAAAA,eAAe,gBAAGJ,KAAK,CAACC,aAAa,CAAwB,IAAII,GAAG,EAAE,EAAC;AAChE;EACXD,eAAe,CAACD,WAAW,GAAG,UAAU,CAAA;AAC1C,CAAA;;AAIA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAG,iBAAiB,CAAA;AAC1C,MAAMC,mBAAmB,GAAGP,KAAK,CAACM,gBAAgB,CAAC,CAAA;AACnD,MAAME,UAAU,GAAG,WAAW,CAAA;AAC9B,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,CAAC,CAAA;AAC1C,MAAMG,MAAM,GAAG,OAAO,CAAA;AACtB,MAAMC,SAAS,GAAGZ,KAAK,CAACW,MAAM,CAAC,CAAA;AAE/B,SAASE,mBAAmBA,CAACC,EAAc,EAAE;AAC3C,EAAA,IAAIP,mBAAmB,EAAE;IACvBA,mBAAmB,CAACO,EAAE,CAAC,CAAA;AACzB,GAAC,MAAM;AACLA,IAAAA,EAAE,EAAE,CAAA;AACN,GAAA;AACF,CAAA;AAEA,SAASC,aAAaA,CAACD,EAAc,EAAE;AACrC,EAAA,IAAIL,aAAa,EAAE;IACjBA,aAAa,CAACK,EAAE,CAAC,CAAA;AACnB,GAAC,MAAM;AACLA,IAAAA,EAAE,EAAE,CAAA;AACN,GAAA;AACF,CAAA;AASA,MAAME,QAAQ,CAAI;AAChB1B,EAAAA,MAAM,GAAwC,SAAS,CAAA;;AAEvD;;AAEA;;AAEA2B,EAAAA,WAAWA,GAAG;IACZ,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;AAC9C,MAAA,IAAI,CAACD,OAAO,GAAIlG,KAAK,IAAK;AACxB,QAAA,IAAI,IAAI,CAACoE,MAAM,KAAK,SAAS,EAAE;UAC7B,IAAI,CAACA,MAAM,GAAG,UAAU,CAAA;UACxB8B,OAAO,CAAClG,KAAK,CAAC,CAAA;AAChB,SAAA;OACD,CAAA;AACD,MAAA,IAAI,CAACmG,MAAM,GAAIC,MAAM,IAAK;AACxB,QAAA,IAAI,IAAI,CAAChC,MAAM,KAAK,SAAS,EAAE;UAC7B,IAAI,CAACA,MAAM,GAAG,UAAU,CAAA;UACxB+B,MAAM,CAACC,MAAM,CAAC,CAAA;AAChB,SAAA;OACD,CAAA;AACH,KAAC,CAAC,CAAA;AACJ,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACO,SAASC,cAAcA,CAAC;EAC7BC,eAAe;EACfC,MAAM;AACNxD,EAAAA,MAAAA;AACmB,CAAC,EAAsB;AAC1C,EAAA,IAAI,CAACY,KAAK,EAAE6C,YAAY,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAACF,MAAM,CAAC5C,KAAK,CAAC,CAAA;EACxD,IAAI,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAG7B,KAAK,CAAC2B,QAAQ,EAAe,CAAA;EACnE,IAAI,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG/B,KAAK,CAAC2B,QAAQ,CAA8B;AAC1EzB,IAAAA,eAAe,EAAE,KAAA;AACnB,GAAC,CAAC,CAAA;EACF,IAAI,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGjC,KAAK,CAAC2B,QAAQ,EAAkB,CAAA;EAChE,IAAI,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGnC,KAAK,CAAC2B,QAAQ,EAAkB,CAAA;EAClE,IAAI,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGrC,KAAK,CAAC2B,QAAQ,EAIhD,CAAA;EACJ,IAAIW,WAAW,GAAGtC,KAAK,CAACuC,MAAM,CAAmB,IAAIlC,GAAG,EAAE,CAAC,CAAA;EAC3D,IAAI;AAAEmC,IAAAA,kBAAAA;AAAmB,GAAC,GAAGvE,MAAM,IAAI,EAAE,CAAA;AAEzC,EAAA,IAAIwE,oBAAoB,GAAGzC,KAAK,CAAC0C,WAAW,CACzC5B,EAAc,IAAK;AAClB,IAAA,IAAI0B,kBAAkB,EAAE;MACtB3B,mBAAmB,CAACC,EAAE,CAAC,CAAA;AACzB,KAAC,MAAM;AACLA,MAAAA,EAAE,EAAE,CAAA;AACN,KAAA;AACF,GAAC,EACD,CAAC0B,kBAAkB,CACrB,CAAC,CAAA;EAED,IAAIG,QAAQ,GAAG3C,KAAK,CAAC0C,WAAW,CAC9B,CACEE,QAAqB,EACrB;IACEC,eAAe;AACfC,IAAAA,SAAS,EAAEA,SAAS;AACpBC,IAAAA,kBAAkB,EAAEA,kBAAAA;AACtB,GAAC,KACE;IACHH,QAAQ,CAACI,QAAQ,CAACtH,OAAO,CAAC,CAACuH,OAAO,EAAEhI,GAAG,KAAK;AAC1C,MAAA,IAAIgI,OAAO,CAACzD,IAAI,KAAK/B,SAAS,EAAE;QAC9B6E,WAAW,CAACY,OAAO,CAACC,GAAG,CAAClI,GAAG,EAAEgI,OAAO,CAACzD,IAAI,CAAC,CAAA;AAC5C,OAAA;AACF,KAAC,CAAC,CAAA;AACFqD,IAAAA,eAAe,CAACnH,OAAO,CAAET,GAAG,IAAKqH,WAAW,CAACY,OAAO,CAACE,MAAM,CAACnI,GAAG,CAAC,CAAC,CAAA;IAEjE,IAAIoI,2BAA2B,GAC7B5B,MAAM,CAAC9D,MAAM,IAAI,IAAI,IACrB8D,MAAM,CAAC9D,MAAM,CAACzB,QAAQ,IAAI,IAAI,IAC9B,OAAOuF,MAAM,CAAC9D,MAAM,CAACzB,QAAQ,CAACoH,mBAAmB,KAAK,UAAU,CAAA;;AAElE;AACA;AACA,IAAA,IAAI,CAACP,kBAAkB,IAAIM,2BAA2B,EAAE;AACtD,MAAA,IAAIP,SAAS,EAAE;AACb/B,QAAAA,aAAa,CAAC,MAAMW,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AAC7C,OAAC,MAAM;AACLH,QAAAA,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AACpD,OAAA;AACA,MAAA,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAIE,SAAS,EAAE;AACb;AACA/B,MAAAA,aAAa,CAAC,MAAM;AAClB;AACA,QAAA,IAAImB,UAAU,EAAE;AACdF,UAAAA,SAAS,IAAIA,SAAS,CAACZ,OAAO,EAAE,CAAA;UAChCc,UAAU,CAACqB,cAAc,EAAE,CAAA;AAC7B,SAAA;AACAxB,QAAAA,YAAY,CAAC;AACX7B,UAAAA,eAAe,EAAE,IAAI;AACrB4C,UAAAA,SAAS,EAAE,IAAI;UACfU,eAAe,EAAET,kBAAkB,CAACS,eAAe;UACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;AACnC,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;;AAEF;MACA,IAAIC,CAAC,GAAGjC,MAAM,CAAC9D,MAAM,CAAEzB,QAAQ,CAACoH,mBAAmB,CAAC,MAAM;AACxDvC,QAAAA,aAAa,CAAC,MAAMW,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AAC7C,OAAC,CAAC,CAAA;;AAEF;AACAc,MAAAA,CAAC,CAACC,QAAQ,CAACC,OAAO,CAAC,MAAM;AACvB7C,QAAAA,aAAa,CAAC,MAAM;UAClBkB,YAAY,CAACxE,SAAS,CAAC,CAAA;UACvB0E,aAAa,CAAC1E,SAAS,CAAC,CAAA;UACxBoE,eAAe,CAACpE,SAAS,CAAC,CAAA;AAC1BsE,UAAAA,YAAY,CAAC;AAAE7B,YAAAA,eAAe,EAAE,KAAA;AAAM,WAAC,CAAC,CAAA;AAC1C,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AAEFa,MAAAA,aAAa,CAAC,MAAMoB,aAAa,CAACuB,CAAC,CAAC,CAAC,CAAA;AACrC,MAAA,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAIxB,UAAU,EAAE;AACd;AACA;AACAF,MAAAA,SAAS,IAAIA,SAAS,CAACZ,OAAO,EAAE,CAAA;MAChCc,UAAU,CAACqB,cAAc,EAAE,CAAA;AAC3BlB,MAAAA,eAAe,CAAC;AACdxD,QAAAA,KAAK,EAAE+D,QAAQ;QACfY,eAAe,EAAET,kBAAkB,CAACS,eAAe;QACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;AACnC,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL;MACA5B,eAAe,CAACe,QAAQ,CAAC,CAAA;AACzBb,MAAAA,YAAY,CAAC;AACX7B,QAAAA,eAAe,EAAE,IAAI;AACrB4C,QAAAA,SAAS,EAAE,KAAK;QAChBU,eAAe,EAAET,kBAAkB,CAACS,eAAe;QACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;AACnC,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAC,EACD,CAAChC,MAAM,CAAC9D,MAAM,EAAEuE,UAAU,EAAEF,SAAS,EAAEM,WAAW,EAAEG,oBAAoB,CAC1E,CAAC,CAAA;;AAED;AACA;AACAzC,EAAAA,KAAK,CAAC6D,eAAe,CAAC,MAAMpC,MAAM,CAACqC,SAAS,CAACnB,QAAQ,CAAC,EAAE,CAAClB,MAAM,EAAEkB,QAAQ,CAAC,CAAC,CAAA;;AAE3E;AACA;EACA3C,KAAK,CAAC+D,SAAS,CAAC,MAAM;IACpB,IAAIjC,SAAS,CAAC5B,eAAe,IAAI,CAAC4B,SAAS,CAACgB,SAAS,EAAE;AACrDb,MAAAA,YAAY,CAAC,IAAIjB,QAAQ,EAAQ,CAAC,CAAA;AACpC,KAAA;AACF,GAAC,EAAE,CAACc,SAAS,CAAC,CAAC,CAAA;;AAEf;AACA;AACA;EACA9B,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpB,IAAA,IAAI/B,SAAS,IAAIJ,YAAY,IAAIH,MAAM,CAAC9D,MAAM,EAAE;MAC9C,IAAIiF,QAAQ,GAAGhB,YAAY,CAAA;AAC3B,MAAA,IAAIoC,aAAa,GAAGhC,SAAS,CAACd,OAAO,CAAA;MACrC,IAAIgB,UAAU,GAAGT,MAAM,CAAC9D,MAAM,CAACzB,QAAQ,CAACoH,mBAAmB,CAAC,YAAY;AACtEb,QAAAA,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AAClD,QAAA,MAAMoB,aAAa,CAAA;AACrB,OAAC,CAAC,CAAA;AACF9B,MAAAA,UAAU,CAACyB,QAAQ,CAACC,OAAO,CAAC,MAAM;QAChC3B,YAAY,CAACxE,SAAS,CAAC,CAAA;QACvB0E,aAAa,CAAC1E,SAAS,CAAC,CAAA;QACxBoE,eAAe,CAACpE,SAAS,CAAC,CAAA;AAC1BsE,QAAAA,YAAY,CAAC;AAAE7B,UAAAA,eAAe,EAAE,KAAA;AAAM,SAAC,CAAC,CAAA;AAC1C,OAAC,CAAC,CAAA;MACFiC,aAAa,CAACD,UAAU,CAAC,CAAA;AAC3B,KAAA;AACF,GAAC,EAAE,CAACO,oBAAoB,EAAEb,YAAY,EAAEI,SAAS,EAAEP,MAAM,CAAC9D,MAAM,CAAC,CAAC,CAAA;;AAElE;AACA;EACAqC,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpB,IAAA,IACE/B,SAAS,IACTJ,YAAY,IACZ/C,KAAK,CAACoF,QAAQ,CAAChJ,GAAG,KAAK2G,YAAY,CAACqC,QAAQ,CAAChJ,GAAG,EAChD;MACA+G,SAAS,CAACZ,OAAO,EAAE,CAAA;AACrB,KAAA;AACF,GAAC,EAAE,CAACY,SAAS,EAAEE,UAAU,EAAErD,KAAK,CAACoF,QAAQ,EAAErC,YAAY,CAAC,CAAC,CAAA;;AAEzD;AACA;EACA5B,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpB,IAAA,IAAI,CAACjC,SAAS,CAAC5B,eAAe,IAAIkC,YAAY,EAAE;AAC9CP,MAAAA,eAAe,CAACO,YAAY,CAACvD,KAAK,CAAC,CAAA;AACnCkD,MAAAA,YAAY,CAAC;AACX7B,QAAAA,eAAe,EAAE,IAAI;AACrB4C,QAAAA,SAAS,EAAE,KAAK;QAChBU,eAAe,EAAEpB,YAAY,CAACoB,eAAe;QAC7CC,YAAY,EAAErB,YAAY,CAACqB,YAAAA;AAC7B,OAAC,CAAC,CAAA;MACFpB,eAAe,CAAC5E,SAAS,CAAC,CAAA;AAC5B,KAAA;GACD,EAAE,CAACqE,SAAS,CAAC5B,eAAe,EAAEkC,YAAY,CAAC,CAAC,CAAA;EAE7CpC,KAAK,CAAC+D,SAAS,CAAC,MAAM;IACpBrH,cAAO,CACL8E,eAAe,IAAI,IAAI,IAAI,CAACC,MAAM,CAACxD,MAAM,CAACiG,mBAAmB,EAC7D,8DAA8D,GAC5D,kEACJ,CAAC,CAAA,CAAA;AACD;AACA;GACD,EAAE,EAAE,CAAC,CAAA;AAEN,EAAA,IAAIC,SAAS,GAAGnE,KAAK,CAACoE,OAAO,CAAC,MAAiB;IAC7C,OAAO;MACLC,UAAU,EAAE5C,MAAM,CAAC4C,UAAU;MAC7BC,cAAc,EAAE7C,MAAM,CAAC6C,cAAc;MACrCC,EAAE,EAAGC,CAAC,IAAK/C,MAAM,CAACgD,QAAQ,CAACD,CAAC,CAAC;AAC7BE,MAAAA,IAAI,EAAEA,CAACC,EAAE,EAAE9F,KAAK,EAAEd,IAAI,KACpB0D,MAAM,CAACgD,QAAQ,CAACE,EAAE,EAAE;QAClB9F,KAAK;QACL+F,kBAAkB,EAAE7G,IAAI,EAAE6G,kBAAAA;AAC5B,OAAC,CAAC;AACJC,MAAAA,OAAO,EAAEA,CAACF,EAAE,EAAE9F,KAAK,EAAEd,IAAI,KACvB0D,MAAM,CAACgD,QAAQ,CAACE,EAAE,EAAE;AAClBE,QAAAA,OAAO,EAAE,IAAI;QACbhG,KAAK;QACL+F,kBAAkB,EAAE7G,IAAI,EAAE6G,kBAAAA;OAC3B,CAAA;KACJ,CAAA;AACH,GAAC,EAAE,CAACnD,MAAM,CAAC,CAAC,CAAA;AAEZ,EAAA,IAAI7E,QAAQ,GAAG6E,MAAM,CAAC7E,QAAQ,IAAI,GAAG,CAAA;AAErC,EAAA,IAAIkI,iBAAiB,GAAG9E,KAAK,CAACoE,OAAO,CACnC,OAAO;IACL3C,MAAM;IACN0C,SAAS;AACTY,IAAAA,MAAM,EAAE,KAAK;AACbnI,IAAAA,QAAAA;GACD,CAAC,EACF,CAAC6E,MAAM,EAAE0C,SAAS,EAAEvH,QAAQ,CAC9B,CAAC,CAAA;AAED,EAAA,IAAIoI,YAAY,GAAGhF,KAAK,CAACoE,OAAO,CAC9B,OAAO;AACLa,IAAAA,oBAAoB,EAAExD,MAAM,CAACxD,MAAM,CAACgH,oBAAAA;GACrC,CAAC,EACF,CAACxD,MAAM,CAACxD,MAAM,CAACgH,oBAAoB,CACrC,CAAC,CAAA;EAEDjF,KAAK,CAAC+D,SAAS,CACb,MAAMmB,+BAAwB,CAACjH,MAAM,EAAEwD,MAAM,CAACxD,MAAM,CAAC,EACrD,CAACA,MAAM,EAAEwD,MAAM,CAACxD,MAAM,CACxB,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,oBACE+B,KAAA,CAAA7D,aAAA,CAAA6D,KAAA,CAAAmF,QAAA,EACEnF,IAAAA,eAAAA,KAAA,CAAA7D,aAAA,CAACiJ,wBAAiB,CAACC,QAAQ,EAAA;AAACnK,IAAAA,KAAK,EAAE4J,iBAAAA;AAAkB,GAAA,eACnD9E,KAAA,CAAA7D,aAAA,CAACmJ,6BAAsB,CAACD,QAAQ,EAAA;AAACnK,IAAAA,KAAK,EAAE2D,KAAAA;AAAM,GAAA,eAC5CmB,KAAA,CAAA7D,aAAA,CAACiE,eAAe,CAACiF,QAAQ,EAAA;IAACnK,KAAK,EAAEoH,WAAW,CAACY,OAAAA;AAAQ,GAAA,eACnDlD,KAAA,CAAA7D,aAAA,CAAC4D,qBAAqB,CAACsF,QAAQ,EAAA;AAACnK,IAAAA,KAAK,EAAE4G,SAAAA;AAAU,GAAA,eAC/C9B,KAAA,CAAA7D,aAAA,CAACoJ,MAAM,EAAA;AACL3I,IAAAA,QAAQ,EAAEA,QAAS;IACnBqH,QAAQ,EAAEpF,KAAK,CAACoF,QAAS;IACzBuB,cAAc,EAAE3G,KAAK,CAAC4G,aAAc;AACpCtB,IAAAA,SAAS,EAAEA,SAAU;AACrBlG,IAAAA,MAAM,EAAE+G,YAAAA;AAAa,GAAA,EAEpBnG,KAAK,CAAC6G,WAAW,IAAIjE,MAAM,CAACxD,MAAM,CAACiG,mBAAmB,gBACrDlE,KAAA,CAAA7D,aAAA,CAACwJ,kBAAkB,EAAA;IACjB7H,MAAM,EAAE2D,MAAM,CAAC3D,MAAO;IACtBG,MAAM,EAAEwD,MAAM,CAACxD,MAAO;AACtBY,IAAAA,KAAK,EAAEA,KAAAA;GACR,CAAC,GAEF2C,eAEI,CACsB,CACR,CACK,CACP,CAAC,EAC5B,IACD,CAAC,CAAA;AAEP,CAAA;;AAEA;AACA,MAAMmE,kBAAkB,gBAAG3F,KAAK,CAAChF,IAAI,CAAC4K,UAAU,CAAC,CAAA;AAEjD,SAASA,UAAUA,CAAC;EAClB9H,MAAM;EACNG,MAAM;AACNY,EAAAA,KAAAA;AAKF,CAAC,EAA6B;EAC5B,OAAOgH,oBAAa,CAAC/H,MAAM,EAAEL,SAAS,EAAEoB,KAAK,EAAEZ,MAAM,CAAC,CAAA;AACxD,CAAA;AASA;AACA;AACA;AACO,SAAS6H,aAAaA,CAAC;EAC5BlJ,QAAQ;EACRmJ,QAAQ;EACR9H,MAAM;AACNN,EAAAA,MAAAA;AACkB,CAAC,EAAE;AACrB,EAAA,IAAIqI,UAAU,GAAGhG,KAAK,CAACuC,MAAM,EAAkB,CAAA;AAC/C,EAAA,IAAIyD,UAAU,CAAC9C,OAAO,IAAI,IAAI,EAAE;AAC9B8C,IAAAA,UAAU,CAAC9C,OAAO,GAAG9E,oBAAoB,CAAC;MAAET,MAAM;AAAEsI,MAAAA,QAAQ,EAAE,IAAA;AAAK,KAAC,CAAC,CAAA;AACvE,GAAA;AAEA,EAAA,IAAI9H,OAAO,GAAG6H,UAAU,CAAC9C,OAAO,CAAA;EAChC,IAAI,CAACrE,KAAK,EAAE6C,YAAY,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC;IACzC7E,MAAM,EAAEqB,OAAO,CAACrB,MAAM;IACtBmH,QAAQ,EAAE9F,OAAO,CAAC8F,QAAAA;AACpB,GAAC,CAAC,CAAA;EACF,IAAI;AAAEzB,IAAAA,kBAAAA;AAAmB,GAAC,GAAGvE,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAI0E,QAAQ,GAAG3C,KAAK,CAAC0C,WAAW,CAC7BE,QAAwD,IAAK;AAC5DJ,IAAAA,kBAAkB,IAAIjC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMmB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CACnC,CAAC,CAAA;AAEDxC,EAAAA,KAAK,CAAC6D,eAAe,CAAC,MAAM1F,OAAO,CAAC+H,MAAM,CAACvD,QAAQ,CAAC,EAAE,CAACxE,OAAO,EAAEwE,QAAQ,CAAC,CAAC,CAAA;AAE1E3C,EAAAA,KAAK,CAAC+D,SAAS,CAAC,MAAMmB,+BAAwB,CAACjH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;AAEjE,EAAA,oBACE+B,KAAA,CAAA7D,aAAA,CAACoJ,MAAM,EAAA;AACL3I,IAAAA,QAAQ,EAAEA,QAAS;AACnBmJ,IAAAA,QAAQ,EAAEA,QAAS;IACnB9B,QAAQ,EAAEpF,KAAK,CAACoF,QAAS;IACzBuB,cAAc,EAAE3G,KAAK,CAAC/B,MAAO;AAC7BqH,IAAAA,SAAS,EAAEhG,OAAQ;AACnBF,IAAAA,MAAM,EAAEA,MAAAA;AAAO,GAChB,CAAC,CAAA;AAEN,CAAA;AASA;AACA;AACA;AACA;AACO,SAASkI,UAAUA,CAAC;EACzBvJ,QAAQ;EACRmJ,QAAQ;EACR9H,MAAM;AACNN,EAAAA,MAAAA;AACe,CAAC,EAAE;AAClB,EAAA,IAAIqI,UAAU,GAAGhG,KAAK,CAACuC,MAAM,EAAe,CAAA;AAC5C,EAAA,IAAIyD,UAAU,CAAC9C,OAAO,IAAI,IAAI,EAAE;AAC9B8C,IAAAA,UAAU,CAAC9C,OAAO,GAAGtE,iBAAiB,CAAC;MAAEjB,MAAM;AAAEsI,MAAAA,QAAQ,EAAE,IAAA;AAAK,KAAC,CAAC,CAAA;AACpE,GAAA;AAEA,EAAA,IAAI9H,OAAO,GAAG6H,UAAU,CAAC9C,OAAO,CAAA;EAChC,IAAI,CAACrE,KAAK,EAAE6C,YAAY,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC;IACzC7E,MAAM,EAAEqB,OAAO,CAACrB,MAAM;IACtBmH,QAAQ,EAAE9F,OAAO,CAAC8F,QAAAA;AACpB,GAAC,CAAC,CAAA;EACF,IAAI;AAAEzB,IAAAA,kBAAAA;AAAmB,GAAC,GAAGvE,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAI0E,QAAQ,GAAG3C,KAAK,CAAC0C,WAAW,CAC7BE,QAAwD,IAAK;AAC5DJ,IAAAA,kBAAkB,IAAIjC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMmB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CACnC,CAAC,CAAA;AAEDxC,EAAAA,KAAK,CAAC6D,eAAe,CAAC,MAAM1F,OAAO,CAAC+H,MAAM,CAACvD,QAAQ,CAAC,EAAE,CAACxE,OAAO,EAAEwE,QAAQ,CAAC,CAAC,CAAA;AAE1E3C,EAAAA,KAAK,CAAC+D,SAAS,CAAC,MAAMmB,+BAAwB,CAACjH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;AAEjE,EAAA,oBACE+B,KAAA,CAAA7D,aAAA,CAACoJ,MAAM,EAAA;AACL3I,IAAAA,QAAQ,EAAEA,QAAS;AACnBmJ,IAAAA,QAAQ,EAAEA,QAAS;IACnB9B,QAAQ,EAAEpF,KAAK,CAACoF,QAAS;IACzBuB,cAAc,EAAE3G,KAAK,CAAC/B,MAAO;AAC7BqH,IAAAA,SAAS,EAAEhG,OAAQ;AACnBF,IAAAA,MAAM,EAAEA,MAAAA;AAAO,GAChB,CAAC,CAAA;AAEN,CAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmI,aAAaA,CAAC;EACrBxJ,QAAQ;EACRmJ,QAAQ;EACR9H,MAAM;AACNE,EAAAA,OAAAA;AACkB,CAAC,EAAE;EACrB,IAAI,CAACU,KAAK,EAAE6C,YAAY,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC;IACzC7E,MAAM,EAAEqB,OAAO,CAACrB,MAAM;IACtBmH,QAAQ,EAAE9F,OAAO,CAAC8F,QAAAA;AACpB,GAAC,CAAC,CAAA;EACF,IAAI;AAAEzB,IAAAA,kBAAAA;AAAmB,GAAC,GAAGvE,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAI0E,QAAQ,GAAG3C,KAAK,CAAC0C,WAAW,CAC7BE,QAAwD,IAAK;AAC5DJ,IAAAA,kBAAkB,IAAIjC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMmB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CACnC,CAAC,CAAA;AAEDxC,EAAAA,KAAK,CAAC6D,eAAe,CAAC,MAAM1F,OAAO,CAAC+H,MAAM,CAACvD,QAAQ,CAAC,EAAE,CAACxE,OAAO,EAAEwE,QAAQ,CAAC,CAAC,CAAA;AAE1E3C,EAAAA,KAAK,CAAC+D,SAAS,CAAC,MAAMmB,+BAAwB,CAACjH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;AAEjE,EAAA,oBACE+B,KAAA,CAAA7D,aAAA,CAACoJ,MAAM,EAAA;AACL3I,IAAAA,QAAQ,EAAEA,QAAS;AACnBmJ,IAAAA,QAAQ,EAAEA,QAAS;IACnB9B,QAAQ,EAAEpF,KAAK,CAACoF,QAAS;IACzBuB,cAAc,EAAE3G,KAAK,CAAC/B,MAAO;AAC7BqH,IAAAA,SAAS,EAAEhG,OAAQ;AACnBF,IAAAA,MAAM,EAAEA,MAAAA;AAAO,GAChB,CAAC,CAAA;AAEN,CAAA;AAEa;EACXmI,aAAa,CAACjG,WAAW,GAAG,wBAAwB,CAAA;AACtD,CAAA;AAeA,MAAMkG,SAAS,GACb,OAAO1I,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACzB,QAAQ,KAAK,WAAW,IACtC,OAAOyB,MAAM,CAACzB,QAAQ,CAACC,aAAa,KAAK,WAAW,CAAA;AAEtD,MAAMmK,kBAAkB,GAAG,+BAA+B,CAAA;;AAE1D;AACA;AACA;AACO,MAAMC,IAAI,gBAAGvG,KAAK,CAACwG,UAAU,CAClC,SAASC,WAAWA,CAClB;EACEC,OAAO;EACPC,QAAQ;EACRC,cAAc;EACd/B,OAAO;EACPhG,KAAK;EACLvE,MAAM;EACNqK,EAAE;EACFC,kBAAkB;EAClBiC,cAAc;EACd,GAAGC,IAAAA;AACL,CAAC,EACDC,GAAG,EACH;EACA,IAAI;AAAEnK,IAAAA,QAAAA;AAAS,GAAC,GAAGoD,KAAK,CAACgH,UAAU,CAACC,wBAAiB,CAAC,CAAA;;AAEtD;AACA,EAAA,IAAIC,YAAY,CAAA;EAChB,IAAIC,UAAU,GAAG,KAAK,CAAA;EAEtB,IAAI,OAAOxC,EAAE,KAAK,QAAQ,IAAI2B,kBAAkB,CAACc,IAAI,CAACzC,EAAE,CAAC,EAAE;AACzD;AACAuC,IAAAA,YAAY,GAAGvC,EAAE,CAAA;;AAEjB;AACA,IAAA,IAAI0B,SAAS,EAAE;MACb,IAAI;QACF,IAAIgB,UAAU,GAAG,IAAIC,GAAG,CAAC3J,MAAM,CAACsG,QAAQ,CAACsD,IAAI,CAAC,CAAA;QAC9C,IAAIC,SAAS,GAAG7C,EAAE,CAAC8C,UAAU,CAAC,IAAI,CAAC,GAC/B,IAAIH,GAAG,CAACD,UAAU,CAACK,QAAQ,GAAG/C,EAAE,CAAC,GACjC,IAAI2C,GAAG,CAAC3C,EAAE,CAAC,CAAA;QACf,IAAIgD,IAAI,GAAGxK,aAAa,CAACqK,SAAS,CAACI,QAAQ,EAAEhL,QAAQ,CAAC,CAAA;QAEtD,IAAI4K,SAAS,CAACK,MAAM,KAAKR,UAAU,CAACQ,MAAM,IAAIF,IAAI,IAAI,IAAI,EAAE;AAC1D;UACAhD,EAAE,GAAGgD,IAAI,GAAGH,SAAS,CAACM,MAAM,GAAGN,SAAS,CAACO,IAAI,CAAA;AAC/C,SAAC,MAAM;AACLZ,UAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,SAAA;OACD,CAAC,OAAO/K,CAAC,EAAE;AACV;AACAK,QAAAC,cAAO,CACL,KAAK,EACJ,CAAYiI,UAAAA,EAAAA,EAAG,CAAsD,qDAAA,CAAA,GACnE,mDACL,CAAC,CAAA,CAAA;AACH,OAAA;AACF,KAAA;AACF,GAAA;;AAEA;AACA,EAAA,IAAI4C,IAAI,GAAGS,OAAO,CAACrD,EAAE,EAAE;AAAEgC,IAAAA,QAAAA;AAAS,GAAC,CAAC,CAAA;AAEpC,EAAA,IAAIsB,eAAe,GAAGC,mBAAmB,CAACvD,EAAE,EAAE;IAC5CE,OAAO;IACPhG,KAAK;IACLvE,MAAM;IACNsK,kBAAkB;IAClB+B,QAAQ;AACRE,IAAAA,cAAAA;AACF,GAAC,CAAC,CAAA;EACF,SAASsB,WAAWA,CAClBnO,KAAsD,EACtD;AACA,IAAA,IAAI0M,OAAO,EAAEA,OAAO,CAAC1M,KAAK,CAAC,CAAA;AAC3B,IAAA,IAAI,CAACA,KAAK,CAACoO,gBAAgB,EAAE;MAC3BH,eAAe,CAACjO,KAAK,CAAC,CAAA;AACxB,KAAA;AACF,GAAA;AAEA,EAAA;AAAA;AACE;AACAgG,IAAAA,KAAA,CAAA7D,aAAA,CAAA,GAAA,EAAAtB,MAAA,CAAAwN,MAAA,KACMvB,IAAI,EAAA;MAAAS,IAAA,EACFL,YAAY,IAAIK,IAAI;AAAAb,MAAAA,OAAA,EACjBS,UAAU,IAAIP,cAAc,GAAGF,OAAO,GAAGyB,WAAW;AAAApB,MAAAA,GAAA,EACxDA,GAAG;AAAAzM,MAAAA,MAAA,EACAA,MAAAA;KACT,CAAA,CAAA;AAAC,IAAA;AAEN,CACF,EAAC;AAEY;EACXiM,IAAI,CAACpG,WAAW,GAAG,MAAM,CAAA;AAC3B,CAAA;AAmBA;AACA;AACA;AACO,MAAMmI,OAAO,gBAAGtI,KAAK,CAACwG,UAAU,CACrC,SAAS+B,cAAcA,CACrB;EACE,cAAc,EAAEC,eAAe,GAAG,MAAM;AACxCC,EAAAA,aAAa,GAAG,KAAK;EACrBC,SAAS,EAAEC,aAAa,GAAG,EAAE;AAC7BC,EAAAA,GAAG,GAAG,KAAK;AACXC,EAAAA,KAAK,EAAEC,SAAS;EAChBnE,EAAE;EACFkC,cAAc;EACdd,QAAQ;EACR,GAAGe,IAAAA;AACL,CAAC,EACDC,GAAG,EACH;AACA,EAAA,IAAIY,IAAI,GAAGoB,eAAe,CAACpE,EAAE,EAAE;IAAEgC,QAAQ,EAAEG,IAAI,CAACH,QAAAA;AAAS,GAAC,CAAC,CAAA;AAC3D,EAAA,IAAI1C,QAAQ,GAAG+E,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAIC,WAAW,GAAGjJ,KAAK,CAACgH,UAAU,CAAC1B,6BAAsB,CAAC,CAAA;EAC1D,IAAI;IAAEnB,SAAS;AAAEvH,IAAAA,QAAAA;AAAS,GAAC,GAAGoD,KAAK,CAACgH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACjE,EAAA,IAAI/G,eAAe,GACjB+I,WAAW,IAAI,IAAI;AACnB;AACA;AACAC,EAAAA,sBAAsB,CAACvB,IAAI,CAAC,IAC5Bd,cAAc,KAAK,IAAI,CAAA;AAEzB,EAAA,IAAIsC,UAAU,GAAGhF,SAAS,CAACG,cAAc,GACrCH,SAAS,CAACG,cAAc,CAACqD,IAAI,CAAC,CAACC,QAAQ,GACvCD,IAAI,CAACC,QAAQ,CAAA;AACjB,EAAA,IAAIwB,gBAAgB,GAAGnF,QAAQ,CAAC2D,QAAQ,CAAA;EACxC,IAAIyB,oBAAoB,GACtBJ,WAAW,IAAIA,WAAW,CAACK,UAAU,IAAIL,WAAW,CAACK,UAAU,CAACrF,QAAQ,GACpEgF,WAAW,CAACK,UAAU,CAACrF,QAAQ,CAAC2D,QAAQ,GACxC,IAAI,CAAA;EAEV,IAAI,CAACa,aAAa,EAAE;AAClBW,IAAAA,gBAAgB,GAAGA,gBAAgB,CAACxP,WAAW,EAAE,CAAA;IACjDyP,oBAAoB,GAAGA,oBAAoB,GACvCA,oBAAoB,CAACzP,WAAW,EAAE,GAClC,IAAI,CAAA;AACRuP,IAAAA,UAAU,GAAGA,UAAU,CAACvP,WAAW,EAAE,CAAA;AACvC,GAAA;EAEA,IAAIyP,oBAAoB,IAAIzM,QAAQ,EAAE;IACpCyM,oBAAoB,GAClBlM,aAAa,CAACkM,oBAAoB,EAAEzM,QAAQ,CAAC,IAAIyM,oBAAoB,CAAA;AACzE,GAAA;;AAEA;AACA;AACA;AACA;AACA;EACA,MAAME,gBAAgB,GACpBJ,UAAU,KAAK,GAAG,IAAIA,UAAU,CAACK,QAAQ,CAAC,GAAG,CAAC,GAC1CL,UAAU,CAACM,MAAM,GAAG,CAAC,GACrBN,UAAU,CAACM,MAAM,CAAA;EACvB,IAAIC,QAAQ,GACVN,gBAAgB,KAAKD,UAAU,IAC9B,CAACP,GAAG,IACHQ,gBAAgB,CAAC3B,UAAU,CAAC0B,UAAU,CAAC,IACvCC,gBAAgB,CAACO,MAAM,CAACJ,gBAAgB,CAAC,KAAK,GAAI,CAAA;AAEtD,EAAA,IAAIK,SAAS,GACXP,oBAAoB,IAAI,IAAI,KAC3BA,oBAAoB,KAAKF,UAAU,IACjC,CAACP,GAAG,IACHS,oBAAoB,CAAC5B,UAAU,CAAC0B,UAAU,CAAC,IAC3CE,oBAAoB,CAACM,MAAM,CAACR,UAAU,CAACM,MAAM,CAAC,KAAK,GAAI,CAAC,CAAA;AAE9D,EAAA,IAAII,WAAW,GAAG;IAChBH,QAAQ;IACRE,SAAS;AACT1J,IAAAA,eAAAA;GACD,CAAA;AAED,EAAA,IAAI4J,WAAW,GAAGJ,QAAQ,GAAGlB,eAAe,GAAG/K,SAAS,CAAA;AAExD,EAAA,IAAIiL,SAA6B,CAAA;AACjC,EAAA,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;AACvCD,IAAAA,SAAS,GAAGC,aAAa,CAACkB,WAAW,CAAC,CAAA;AACxC,GAAC,MAAM;AACL;AACA;AACA;AACA;AACA;AACAnB,IAAAA,SAAS,GAAG,CACVC,aAAa,EACbe,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAC1BE,SAAS,GAAG,SAAS,GAAG,IAAI,EAC5B1J,eAAe,GAAG,eAAe,GAAG,IAAI,CACzC,CACE6J,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC,CAAA;AACd,GAAA;AAEA,EAAA,IAAIpB,KAAK,GACP,OAAOC,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACe,WAAW,CAAC,GAAGf,SAAS,CAAA;EAEtE,oBACE9I,KAAA,CAAA7D,aAAA,CAACoK,IAAI,EAAA1L,MAAA,CAAAwN,MAAA,CAAA,EAAA,EACCvB,IAAI,EAAA;AAAA,IAAA,cAAA,EACMgD,WAAW;AAAApB,IAAAA,SAAA,EACdA,SAAS;AAAA3B,IAAAA,GAAA,EACfA,GAAG;AAAA8B,IAAAA,KAAA,EACDA,KAAK;AAAAlE,IAAAA,EAAA,EACRA,EAAE;AAAAkC,IAAAA,cAAA,EACUA,cAAAA;GAEf,CAAA,EAAA,OAAOd,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC8D,WAAW,CAAC,GAAG9D,QACtD,CAAC,CAAA;AAEX,CACF,EAAC;AAEY;EACXuC,OAAO,CAACnI,WAAW,GAAG,SAAS,CAAA;AACjC,CAAA;;AAEA;AACA;AACA;;AA0CA;AACA;AACA;;AAGA;AACA;AACA;;AA2CA;AACA;AACA;AACA;AACA;AACA;MACa+J,IAAI,gBAAGlK,KAAK,CAACwG,UAAU,CAClC,CACE;EACE2D,UAAU;EACV1F,QAAQ;EACRmC,cAAc;EACd/B,OAAO;EACPhG,KAAK;EACLhC,MAAM,EAANA,OAAM,GAAGvD,aAAa;EACtBwD,MAAM;EACNsN,QAAQ;EACRzD,QAAQ;EACR/B,kBAAkB;EAClBiC,cAAc;EACd,GAAGwD,KAAAA;AACL,CAAC,EACDC,YAAY,KACT;AACH,EAAA,IAAIC,MAAM,GAAGC,SAAS,EAAE,CAAA;AACxB,EAAA,IAAIC,UAAU,GAAGC,aAAa,CAAC5N,MAAM,EAAE;AAAE6J,IAAAA,QAAAA;AAAS,GAAC,CAAC,CAAA;AACpD,EAAA,IAAIgE,UAA0B,GAC5B9N,OAAM,CAACjD,WAAW,EAAE,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM,CAAA;EAEjD,IAAIgR,aAAsD,GAAI5Q,KAAK,IAAK;AACtEoQ,IAAAA,QAAQ,IAAIA,QAAQ,CAACpQ,KAAK,CAAC,CAAA;IAC3B,IAAIA,KAAK,CAACoO,gBAAgB,EAAE,OAAA;IAC5BpO,KAAK,CAAC6Q,cAAc,EAAE,CAAA;AAEtB,IAAA,IAAIC,SAAS,GAAI9Q,KAAK,CAAgC+Q,WAAW,CAC9DD,SAAqC,CAAA;IAExC,IAAIE,YAAY,GACbF,SAAS,EAAE5N,YAAY,CAAC,YAAY,CAAC,IACtCL,OAAM,CAAA;AAER0N,IAAAA,MAAM,CAACO,SAAS,IAAI9Q,KAAK,CAACiR,aAAa,EAAE;MACvCd,UAAU;AACVtN,MAAAA,MAAM,EAAEmO,YAAY;MACpBvG,QAAQ;MACRI,OAAO;MACPhG,KAAK;MACL8H,QAAQ;MACR/B,kBAAkB;AAClBiC,MAAAA,cAAAA;AACF,KAAC,CAAC,CAAA;GACH,CAAA;AAED,EAAA,oBACE7G,KAAA,CAAA7D,aAAA,CAAAtB,MAAAA,EAAAA,MAAA,CAAAwN,MAAA,CAAA;AAAAtB,IAAAA,GAAA,EACOuD,YAAY;AAAAzN,IAAAA,MAAA,EACT8N,UAAU;AAAA7N,IAAAA,MAAA,EACV2N,UAAU;AAAAL,IAAAA,QAAA,EACRxD,cAAc,GAAGwD,QAAQ,GAAGQ,aAAAA;GAClCP,EAAAA,KAAK,CACV,CAAC,CAAA;AAEN,CACF,EAAC;AAEY;EACXH,IAAI,CAAC/J,WAAW,GAAG,MAAM,CAAA;AAC3B,CAAA;AAOA;AACA;AACA;AACA;AACO,SAAS+K,iBAAiBA,CAAC;EAChCC,MAAM;AACNC,EAAAA,UAAAA;AACsB,CAAC,EAAE;AACzBC,EAAAA,oBAAoB,CAAC;IAAEF,MAAM;AAAEC,IAAAA,UAAAA;AAAW,GAAC,CAAC,CAAA;AAC5C,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEa;EACXF,iBAAiB,CAAC/K,WAAW,GAAG,mBAAmB,CAAA;AACrD,CAAA;AACA;;AAEA;AACA;AACA;AAAA,IAEKmL,cAAc,0BAAdA,cAAc,EAAA;EAAdA,cAAc,CAAA,sBAAA,CAAA,GAAA,sBAAA,CAAA;EAAdA,cAAc,CAAA,WAAA,CAAA,GAAA,WAAA,CAAA;EAAdA,cAAc,CAAA,kBAAA,CAAA,GAAA,kBAAA,CAAA;EAAdA,cAAc,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;EAAdA,cAAc,CAAA,wBAAA,CAAA,GAAA,wBAAA,CAAA;AAAA,EAAA,OAAdA,cAAc,CAAA;AAAA,CAAA,CAAdA,cAAc,IAAA,EAAA,CAAA,CAAA;AAAA,IAQdC,mBAAmB,0BAAnBA,mBAAmB,EAAA;EAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;EAAnBA,mBAAmB,CAAA,aAAA,CAAA,GAAA,aAAA,CAAA;EAAnBA,mBAAmB,CAAA,sBAAA,CAAA,GAAA,sBAAA,CAAA;AAAA,EAAA,OAAnBA,mBAAmB,CAAA;AAAA,CAAA,CAAnBA,mBAAmB,IAMxB,EAAA,CAAA,CAAA;AAEA,SAASC,yBAAyBA,CAChCC,QAA8C,EAC9C;EACA,OAAQ,CAAA,EAAEA,QAAS,CAA8F,6FAAA,CAAA,CAAA;AACnH,CAAA;AAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAE;AACtD,EAAA,IAAIE,GAAG,GAAG3L,KAAK,CAACgH,UAAU,CAAC5B,wBAAiB,CAAC,CAAA;AAC7C,EAAA,CAAUuG,GAAG,GAAbC,gBAAS,QAAMJ,yBAAyB,CAACC,QAAQ,CAAC,EAAzC,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAOE,GAAG,CAAA;AACZ,CAAA;AAEA,SAASE,kBAAkBA,CAACJ,QAA6B,EAAE;AACzD,EAAA,IAAI5M,KAAK,GAAGmB,KAAK,CAACgH,UAAU,CAAC1B,6BAAsB,CAAC,CAAA;AACpD,EAAA,CAAUzG,KAAK,GAAf+M,gBAAS,QAAQJ,yBAAyB,CAACC,QAAQ,CAAC,EAA3C,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAO5M,KAAK,CAAA;AACd,CAAA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASqJ,mBAAmBA,CACjCvD,EAAM,EACN;EACErK,MAAM;AACNuK,EAAAA,OAAO,EAAEiH,WAAW;EACpBjN,KAAK;EACL+F,kBAAkB;EAClB+B,QAAQ;AACRE,EAAAA,cAAAA;AAQF,CAAC,GAAG,EAAE,EAC4C;AAClD,EAAA,IAAIpC,QAAQ,GAAGsH,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAI9H,QAAQ,GAAG+E,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAIrB,IAAI,GAAGoB,eAAe,CAACpE,EAAE,EAAE;AAAEgC,IAAAA,QAAAA;AAAS,GAAC,CAAC,CAAA;AAE5C,EAAA,OAAO3G,KAAK,CAAC0C,WAAW,CACrB1I,KAAsC,IAAK;AAC1C,IAAA,IAAIK,sBAAsB,CAACL,KAAK,EAAEM,MAAM,CAAC,EAAE;MACzCN,KAAK,CAAC6Q,cAAc,EAAE,CAAA;;AAEtB;AACA;AACA,MAAA,IAAIhG,OAAO,GACTiH,WAAW,KAAKrO,SAAS,GACrBqO,WAAW,GACXE,UAAU,CAAC/H,QAAQ,CAAC,KAAK+H,UAAU,CAACrE,IAAI,CAAC,CAAA;MAE/ClD,QAAQ,CAACE,EAAE,EAAE;QACXE,OAAO;QACPhG,KAAK;QACL+F,kBAAkB;QAClB+B,QAAQ;AACRE,QAAAA,cAAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;GACD,EACD,CACE5C,QAAQ,EACRQ,QAAQ,EACRkD,IAAI,EACJmE,WAAW,EACXjN,KAAK,EACLvE,MAAM,EACNqK,EAAE,EACFC,kBAAkB,EAClB+B,QAAQ,EACRE,cAAc,CAElB,CAAC,CAAA;AACH,CAAA;;AAEA;AACA;AACA;AACA;AACO,SAASoF,eAAeA,CAC7BC,WAAiC,EACM;AACvCzP,EAAAC,cAAO,CACL,OAAOhC,eAAe,KAAK,WAAW,EACrC,yEAAwE,GACtE,CAAA,iEAAA,CAAkE,GAClE,CAAuD,sDAAA,CAAA,GACvD,6CACL,CAAC,CAAA,CAAA;EAED,IAAIyR,sBAAsB,GAAGnM,KAAK,CAACuC,MAAM,CAAC/H,kBAAkB,CAAC0R,WAAW,CAAC,CAAC,CAAA;AAC1E,EAAA,IAAIE,qBAAqB,GAAGpM,KAAK,CAACuC,MAAM,CAAC,KAAK,CAAC,CAAA;AAE/C,EAAA,IAAI0B,QAAQ,GAAG+E,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAIvN,YAAY,GAAGuE,KAAK,CAACoE,OAAO,CAC9B;AACE;AACA;AACA;EACA9I,0BAA0B,CACxB2I,QAAQ,CAAC6D,MAAM,EACfsE,qBAAqB,CAAClJ,OAAO,GAAG,IAAI,GAAGiJ,sBAAsB,CAACjJ,OAChE,CAAC,EACH,CAACe,QAAQ,CAAC6D,MAAM,CAClB,CAAC,CAAA;AAED,EAAA,IAAIrD,QAAQ,GAAGsH,WAAW,EAAE,CAAA;EAC5B,IAAIM,eAAe,GAAGrM,KAAK,CAAC0C,WAAW,CACrC,CAAC4J,QAAQ,EAAEC,eAAe,KAAK;AAC7B,IAAA,MAAMC,eAAe,GAAGhS,kBAAkB,CACxC,OAAO8R,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC7Q,YAAY,CAAC,GAAG6Q,QAC5D,CAAC,CAAA;IACDF,qBAAqB,CAAClJ,OAAO,GAAG,IAAI,CAAA;AACpCuB,IAAAA,QAAQ,CAAC,GAAG,GAAG+H,eAAe,EAAED,eAAe,CAAC,CAAA;AAClD,GAAC,EACD,CAAC9H,QAAQ,EAAEhJ,YAAY,CACzB,CAAC,CAAA;AAED,EAAA,OAAO,CAACA,YAAY,EAAE4Q,eAAe,CAAC,CAAA;AACxC,CAAA;;AASA;AACA;AACA;;AAqBA;AACA;AACA;;AASA,SAASI,4BAA4BA,GAAG;AACtC,EAAA,IAAI,OAAOvQ,QAAQ,KAAK,WAAW,EAAE;AACnC,IAAA,MAAM,IAAIoB,KAAK,CACb,mDAAmD,GACjD,8DACJ,CAAC,CAAA;AACH,GAAA;AACF,CAAA;AAEA,IAAIoP,SAAS,GAAG,CAAC,CAAA;AACjB,IAAIC,kBAAkB,GAAGA,MAAO,CAAA,EAAA,EAAIC,MAAM,CAAC,EAAEF,SAAS,CAAE,CAAG,EAAA,CAAA,CAAA;;AAE3D;AACA;AACA;AACA;AACO,SAASlC,SAASA,GAAmB;EAC1C,IAAI;AAAE/I,IAAAA,MAAAA;AAAO,GAAC,GAAGiK,oBAAoB,CAACJ,cAAc,CAACuB,SAAS,CAAC,CAAA;EAC/D,IAAI;AAAEjQ,IAAAA,QAAAA;AAAS,GAAC,GAAGoD,KAAK,CAACgH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACtD,EAAA,IAAI6F,cAAc,GAAGC,iBAAU,EAAE,CAAA;EAEjC,OAAO/M,KAAK,CAAC0C,WAAW,CACtB,CAACpI,MAAM,EAAE0S,OAAO,GAAG,EAAE,KAAK;AACxBP,IAAAA,4BAA4B,EAAE,CAAA;IAE9B,IAAI;MAAE3P,MAAM;MAAED,MAAM;MAAEL,OAAO;MAAEO,QAAQ;AAAEC,MAAAA,IAAAA;AAAK,KAAC,GAAGL,qBAAqB,CACrErC,MAAM,EACNsC,QACF,CAAC,CAAA;AAED,IAAA,IAAIoQ,OAAO,CAACvI,QAAQ,KAAK,KAAK,EAAE;MAC9B,IAAIxJ,GAAG,GAAG+R,OAAO,CAAC7C,UAAU,IAAIwC,kBAAkB,EAAE,CAAA;AACpDlL,MAAAA,MAAM,CAACwL,KAAK,CAAChS,GAAG,EAAE6R,cAAc,EAAEE,OAAO,CAAClQ,MAAM,IAAIA,MAAM,EAAE;QAC1D8H,kBAAkB,EAAEoI,OAAO,CAACpI,kBAAkB;QAC9C7H,QAAQ;QACRC,IAAI;AACJ2N,QAAAA,UAAU,EAAEqC,OAAO,CAACnQ,MAAM,IAAKA,MAAyB;AACxDqQ,QAAAA,WAAW,EAAEF,OAAO,CAACxQ,OAAO,IAAKA,OAAuB;QACxDsG,SAAS,EAAEkK,OAAO,CAAClK,SAAAA;AACrB,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;MACLrB,MAAM,CAACgD,QAAQ,CAACuI,OAAO,CAAClQ,MAAM,IAAIA,MAAM,EAAE;QACxC8H,kBAAkB,EAAEoI,OAAO,CAACpI,kBAAkB;QAC9C7H,QAAQ;QACRC,IAAI;AACJ2N,QAAAA,UAAU,EAAEqC,OAAO,CAACnQ,MAAM,IAAKA,MAAyB;AACxDqQ,QAAAA,WAAW,EAAEF,OAAO,CAACxQ,OAAO,IAAKA,OAAuB;QACxDqI,OAAO,EAAEmI,OAAO,CAACnI,OAAO;QACxBhG,KAAK,EAAEmO,OAAO,CAACnO,KAAK;AACpBsO,QAAAA,WAAW,EAAEL,cAAc;QAC3BhK,SAAS,EAAEkK,OAAO,CAAClK,SAAS;QAC5B+D,cAAc,EAAEmG,OAAO,CAACnG,cAAAA;AAC1B,OAAC,CAAC,CAAA;AACJ,KAAA;GACD,EACD,CAACpF,MAAM,EAAE7E,QAAQ,EAAEkQ,cAAc,CACnC,CAAC,CAAA;AACH,CAAA;;AAEA;AACA;AACO,SAASpC,aAAaA,CAC3B5N,MAAe,EACf;AAAE6J,EAAAA,QAAAA;AAA6C,CAAC,GAAG,EAAE,EAC7C;EACR,IAAI;AAAE/J,IAAAA,QAAAA;AAAS,GAAC,GAAGoD,KAAK,CAACgH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACtD,EAAA,IAAImG,YAAY,GAAGpN,KAAK,CAACgH,UAAU,CAACqG,mBAAY,CAAC,CAAA;AACjD,EAAA,CAAUD,YAAY,GAAtBxB,gBAAS,CAAA,KAAA,EAAe,kDAAkD,CAAA,CAAjE,GAAA,KAAA,CAAA,CAAA;AAET,EAAA,IAAI,CAAC0B,KAAK,CAAC,GAAGF,YAAY,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC5C;AACA;AACA,EAAA,IAAI7F,IAAI,GAAG;AAAE,IAAA,GAAGoB,eAAe,CAACjM,MAAM,GAAGA,MAAM,GAAG,GAAG,EAAE;AAAE6J,MAAAA,QAAAA;KAAU,CAAA;GAAG,CAAA;;AAEtE;AACA;AACA;AACA,EAAA,IAAI1C,QAAQ,GAAG+E,WAAW,EAAE,CAAA;EAC5B,IAAIlM,MAAM,IAAI,IAAI,EAAE;AAClB;AACA;AACA6K,IAAAA,IAAI,CAACG,MAAM,GAAG7D,QAAQ,CAAC6D,MAAM,CAAA;;AAE7B;AACA;AACA;IACA,IAAI2F,MAAM,GAAG,IAAI/S,eAAe,CAACiN,IAAI,CAACG,MAAM,CAAC,CAAA;AAC7C,IAAA,IAAI4F,WAAW,GAAGD,MAAM,CAAC5R,MAAM,CAAC,OAAO,CAAC,CAAA;IACxC,IAAI8R,kBAAkB,GAAGD,WAAW,CAACE,IAAI,CAAEvS,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC,CAAA;AAC1D,IAAA,IAAIsS,kBAAkB,EAAE;AACtBF,MAAAA,MAAM,CAACrK,MAAM,CAAC,OAAO,CAAC,CAAA;MACtBsK,WAAW,CAAC3D,MAAM,CAAE1O,CAAC,IAAKA,CAAC,CAAC,CAACK,OAAO,CAAEL,CAAC,IAAKoS,MAAM,CAAC3R,MAAM,CAAC,OAAO,EAAET,CAAC,CAAC,CAAC,CAAA;AACtE,MAAA,IAAIwS,EAAE,GAAGJ,MAAM,CAACK,QAAQ,EAAE,CAAA;MAC1BnG,IAAI,CAACG,MAAM,GAAG+F,EAAE,GAAI,CAAGA,CAAAA,EAAAA,EAAG,CAAC,CAAA,GAAG,EAAE,CAAA;AAClC,KAAA;AACF,GAAA;AAEA,EAAA,IAAI,CAAC,CAAC/Q,MAAM,IAAIA,MAAM,KAAK,GAAG,KAAKwQ,KAAK,CAACS,KAAK,CAACC,KAAK,EAAE;AACpDrG,IAAAA,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACG,MAAM,GACrBH,IAAI,CAACG,MAAM,CAACjD,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ,CAAA;AACd,GAAA;;AAEA;AACA;AACA;AACA;EACA,IAAIjI,QAAQ,KAAK,GAAG,EAAE;IACpB+K,IAAI,CAACC,QAAQ,GACXD,IAAI,CAACC,QAAQ,KAAK,GAAG,GAAGhL,QAAQ,GAAGqR,SAAS,CAAC,CAACrR,QAAQ,EAAE+K,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAA;AAC3E,GAAA;EAEA,OAAOoE,UAAU,CAACrE,IAAI,CAAC,CAAA;AACzB,CAAA;AAUA;AAEA;AACA;AACA;AACA;AACO,SAASuG,UAAUA,CAAc;AACtCjT,EAAAA,GAAAA;AACgB,CAAC,GAAG,EAAE,EAAgC;EACtD,IAAI;AAAEwG,IAAAA,MAAAA;AAAO,GAAC,GAAGiK,oBAAoB,CAACJ,cAAc,CAAC6C,UAAU,CAAC,CAAA;AAChE,EAAA,IAAItP,KAAK,GAAGgN,kBAAkB,CAACN,mBAAmB,CAAC4C,UAAU,CAAC,CAAA;AAC9D,EAAA,IAAI7L,WAAW,GAAGtC,KAAK,CAACgH,UAAU,CAAC5G,eAAe,CAAC,CAAA;AACnD,EAAA,IAAI2N,KAAK,GAAG/N,KAAK,CAACgH,UAAU,CAACqG,mBAAY,CAAC,CAAA;AAC1C,EAAA,IAAIe,OAAO,GAAGL,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAAC9D,MAAM,GAAG,CAAC,CAAC,EAAEsE,KAAK,CAACM,EAAE,CAAA;AAE/D,EAAA,CAAU/L,WAAW,GAArBsJ,gBAAS,CAAA,KAAA,EAAe,CAAiD,gDAAA,CAAA,CAAA,CAAhE,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,CAAUmC,KAAK,GAAfnC,gBAAS,CAAA,KAAA,EAAS,CAA8C,6CAAA,CAAA,CAAA,CAAvD,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,EACEwC,OAAO,IAAI,IAAI,CAAA,GADjBxC,gBAAS,CAEN,KAAA,EAAA,CAAA,gEAAA,CAAiE,EAF3D,GAAA,KAAA,CAAA,CAAA;;AAKT;AACA;AACA;EACA,IAAI0C,UAAU,GAAG1N,SAAS,GAAGA,SAAS,EAAE,GAAG,EAAE,CAAA;AAC7C,EAAA,IAAI,CAACuJ,UAAU,EAAEoE,aAAa,CAAC,GAAGvO,KAAK,CAAC2B,QAAQ,CAAS1G,GAAG,IAAIqT,UAAU,CAAC,CAAA;AAC3E,EAAA,IAAIrT,GAAG,IAAIA,GAAG,KAAKkP,UAAU,EAAE;IAC7BoE,aAAa,CAACtT,GAAG,CAAC,CAAA;AACpB,GAAC,MAAM,IAAI,CAACkP,UAAU,EAAE;AACtB;AACAoE,IAAAA,aAAa,CAAC5B,kBAAkB,EAAE,CAAC,CAAA;AACrC,GAAA;;AAEA;EACA3M,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpBtC,IAAAA,MAAM,CAAC+M,UAAU,CAACrE,UAAU,CAAC,CAAA;AAC7B,IAAA,OAAO,MAAM;AACX;AACA;AACA;AACA1I,MAAAA,MAAM,CAACgN,aAAa,CAACtE,UAAU,CAAC,CAAA;KACjC,CAAA;AACH,GAAC,EAAE,CAAC1I,MAAM,EAAE0I,UAAU,CAAC,CAAC,CAAA;;AAExB;EACA,IAAIuE,IAAI,GAAG1O,KAAK,CAAC0C,WAAW,CAC1B,CAAC6E,IAAY,EAAExJ,IAA8B,KAAK;AAChD,IAAA,CAAUqQ,OAAO,GAAjBxC,gBAAS,CAAA,KAAA,EAAU,yCAAyC,CAAA,CAAnD,GAAA,KAAA,CAAA,CAAA;IACTnK,MAAM,CAACwL,KAAK,CAAC9C,UAAU,EAAEiE,OAAO,EAAE7G,IAAI,EAAExJ,IAAI,CAAC,CAAA;GAC9C,EACD,CAACoM,UAAU,EAAEiE,OAAO,EAAE3M,MAAM,CAC9B,CAAC,CAAA;AAED,EAAA,IAAIkN,UAAU,GAAGnE,SAAS,EAAE,CAAA;EAC5B,IAAID,MAAM,GAAGvK,KAAK,CAAC0C,WAAW,CAC5B,CAACpI,MAAM,EAAEyD,IAAI,KAAK;IAChB4Q,UAAU,CAACrU,MAAM,EAAE;AACjB,MAAA,GAAGyD,IAAI;AACP0G,MAAAA,QAAQ,EAAE,KAAK;AACf0F,MAAAA,UAAAA;AACF,KAAC,CAAC,CAAA;AACJ,GAAC,EACD,CAACA,UAAU,EAAEwE,UAAU,CACzB,CAAC,CAAA;AAED,EAAA,IAAIC,WAAW,GAAG5O,KAAK,CAACoE,OAAO,CAAC,MAAM;IACpC,IAAIwK,WAAW,gBAAG5O,KAAK,CAACwG,UAAU,CAChC,CAAC6D,KAAK,EAAEtD,GAAG,KAAK;MACd,oBACE/G,KAAA,CAAA7D,aAAA,CAAC+N,IAAI,EAAArP,MAAA,CAAAwN,MAAA,CAAA,EAAA,EAAKgC,KAAK,EAAA;AAAA5F,QAAAA,QAAA,EAAY,KAAK;AAAA0F,QAAAA,UAAA,EAAcA,UAAU;AAAApD,QAAAA,GAAA,EAAOA,GAAAA;AAAG,OAAA,CAAG,CAAC,CAAA;AAE1E,KACF,CAAC,CAAA;AACD,IAAa;MACX6H,WAAW,CAACzO,WAAW,GAAG,cAAc,CAAA;AAC1C,KAAA;AACA,IAAA,OAAOyO,WAAW,CAAA;AACpB,GAAC,EAAE,CAACzE,UAAU,CAAC,CAAC,CAAA;;AAEhB;EACA,IAAIlH,OAAO,GAAGpE,KAAK,CAACmE,QAAQ,CAAC6L,GAAG,CAAC1E,UAAU,CAAC,IAAI2E,YAAY,CAAA;AAC5D,EAAA,IAAItP,IAAI,GAAG8C,WAAW,CAACuM,GAAG,CAAC1E,UAAU,CAAC,CAAA;AACtC,EAAA,IAAI4E,qBAAqB,GAAG/O,KAAK,CAACoE,OAAO,CACvC,OAAO;AACL8F,IAAAA,IAAI,EAAE0E,WAAW;IACjBrE,MAAM;IACNmE,IAAI;AACJ,IAAA,GAAGzL,OAAO;AACVzD,IAAAA,IAAAA;AACF,GAAC,CAAC,EACF,CAACoP,WAAW,EAAErE,MAAM,EAAEmE,IAAI,EAAEzL,OAAO,EAAEzD,IAAI,CAC3C,CAAC,CAAA;AAED,EAAA,OAAOuP,qBAAqB,CAAA;AAC9B,CAAA;;AAEA;AACA;AACA;AACA;AACO,SAASC,WAAWA,GAAkC;AAC3D,EAAA,IAAInQ,KAAK,GAAGgN,kBAAkB,CAACN,mBAAmB,CAAC0D,WAAW,CAAC,CAAA;EAC/D,OAAOtU,KAAK,CAACuU,IAAI,CAACrQ,KAAK,CAACmE,QAAQ,CAAC/D,OAAO,EAAE,CAAC,CAAC7D,GAAG,CAAC,CAAC,CAACH,GAAG,EAAEgI,OAAO,CAAC,MAAM;AACnE,IAAA,GAAGA,OAAO;AACVhI,IAAAA,GAAAA;AACF,GAAC,CAAC,CAAC,CAAA;AACL,CAAA;AAEA,MAAMkU,8BAA8B,GAAG,+BAA+B,CAAA;AACtE,IAAIC,oBAA4C,GAAG,EAAE,CAAA;;AAErD;AACA;AACA;AACA,SAAS/D,oBAAoBA,CAAC;EAC5BF,MAAM;AACNC,EAAAA,UAAAA;AAIF,CAAC,GAAG,EAAE,EAAE;EACN,IAAI;AAAE3J,IAAAA,MAAAA;AAAO,GAAC,GAAGiK,oBAAoB,CAACJ,cAAc,CAAC+D,oBAAoB,CAAC,CAAA;EAC1E,IAAI;IAAEC,qBAAqB;AAAE1K,IAAAA,kBAAAA;AAAmB,GAAC,GAAGiH,kBAAkB,CACpEN,mBAAmB,CAAC8D,oBACtB,CAAC,CAAA;EACD,IAAI;AAAEzS,IAAAA,QAAAA;AAAS,GAAC,GAAGoD,KAAK,CAACgH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACtD,EAAA,IAAIhD,QAAQ,GAAG+E,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAIuE,OAAO,GAAGgC,UAAU,EAAE,CAAA;AAC1B,EAAA,IAAIjG,UAAU,GAAGkG,aAAa,EAAE,CAAA;;AAEhC;EACAxP,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpBpG,IAAAA,MAAM,CAACQ,OAAO,CAACsR,iBAAiB,GAAG,QAAQ,CAAA;AAC3C,IAAA,OAAO,MAAM;AACX9R,MAAAA,MAAM,CAACQ,OAAO,CAACsR,iBAAiB,GAAG,MAAM,CAAA;KAC1C,CAAA;GACF,EAAE,EAAE,CAAC,CAAA;;AAEN;AACAC,EAAAA,WAAW,CACT1P,KAAK,CAAC0C,WAAW,CAAC,MAAM;AACtB,IAAA,IAAI4G,UAAU,CAACzK,KAAK,KAAK,MAAM,EAAE;AAC/B,MAAA,IAAI5D,GAAG,GAAG,CAACkQ,MAAM,GAAGA,MAAM,CAAClH,QAAQ,EAAEsJ,OAAO,CAAC,GAAG,IAAI,KAAKtJ,QAAQ,CAAChJ,GAAG,CAAA;AACrEmU,MAAAA,oBAAoB,CAACnU,GAAG,CAAC,GAAG0C,MAAM,CAACgS,OAAO,CAAA;AAC5C,KAAA;IACA,IAAI;AACFC,MAAAA,cAAc,CAACC,OAAO,CACpBzE,UAAU,IAAI+D,8BAA8B,EAC5CW,IAAI,CAACC,SAAS,CAACX,oBAAoB,CACrC,CAAC,CAAA;KACF,CAAC,OAAOxP,KAAK,EAAE;AACdnD,MAAAC,cAAO,CACL,KAAK,EACJ,CAAA,iGAAA,EAAmGkD,KAAM,CAAA,EAAA,CAC5G,CAAC,CAAA,CAAA;AACH,KAAA;AACAjC,IAAAA,MAAM,CAACQ,OAAO,CAACsR,iBAAiB,GAAG,MAAM,CAAA;AAC3C,GAAC,EAAE,CAACrE,UAAU,EAAED,MAAM,EAAE7B,UAAU,CAACzK,KAAK,EAAEoF,QAAQ,EAAEsJ,OAAO,CAAC,CAC9D,CAAC,CAAA;;AAED;AACA,EAAA,IAAI,OAAOrR,QAAQ,KAAK,WAAW,EAAE;AACnC;IACA8D,KAAK,CAAC6D,eAAe,CAAC,MAAM;MAC1B,IAAI;QACF,IAAImM,gBAAgB,GAAGJ,cAAc,CAACK,OAAO,CAC3C7E,UAAU,IAAI+D,8BAChB,CAAC,CAAA;AACD,QAAA,IAAIa,gBAAgB,EAAE;AACpBZ,UAAAA,oBAAoB,GAAGU,IAAI,CAACI,KAAK,CAACF,gBAAgB,CAAC,CAAA;AACrD,SAAA;OACD,CAAC,OAAO5T,CAAC,EAAE;AACV;AAAA,OAAA;AAEJ,KAAC,EAAE,CAACgP,UAAU,CAAC,CAAC,CAAA;;AAEhB;AACA;IACApL,KAAK,CAAC6D,eAAe,CAAC,MAAM;AAC1B,MAAA,IAAIsM,qBAAkE,GACpEhF,MAAM,IAAIvO,QAAQ,KAAK,GAAG,GACtB,CAACqH,QAAQ,EAAEsJ,OAAO,KAChBpC,MAAM;AACJ;AACA,MAAA;AACE,QAAA,GAAGlH,QAAQ;QACX2D,QAAQ,EACNzK,aAAa,CAAC8G,QAAQ,CAAC2D,QAAQ,EAAEhL,QAAQ,CAAC,IAC1CqH,QAAQ,CAAC2D,QAAAA;AACb,OAAC,EACD2F,OACF,CAAC,GACHpC,MAAM,CAAA;AACZ,MAAA,IAAIiF,wBAAwB,GAAG3O,MAAM,EAAE4O,uBAAuB,CAC5DjB,oBAAoB,EACpB,MAAMzR,MAAM,CAACgS,OAAO,EACpBQ,qBACF,CAAC,CAAA;AACD,MAAA,OAAO,MAAMC,wBAAwB,IAAIA,wBAAwB,EAAE,CAAA;KACpE,EAAE,CAAC3O,MAAM,EAAE7E,QAAQ,EAAEuO,MAAM,CAAC,CAAC,CAAA;;AAE9B;AACA;IACAnL,KAAK,CAAC6D,eAAe,CAAC,MAAM;AAC1B;MACA,IAAIyL,qBAAqB,KAAK,KAAK,EAAE;AACnC,QAAA,OAAA;AACF,OAAA;;AAEA;AACA,MAAA,IAAI,OAAOA,qBAAqB,KAAK,QAAQ,EAAE;AAC7C3R,QAAAA,MAAM,CAAC2S,QAAQ,CAAC,CAAC,EAAEhB,qBAAqB,CAAC,CAAA;AACzC,QAAA,OAAA;AACF,OAAA;;AAEA;MACA,IAAIrL,QAAQ,CAAC8D,IAAI,EAAE;AACjB,QAAA,IAAIwI,EAAE,GAAGrU,QAAQ,CAACsU,cAAc,CAC9BC,kBAAkB,CAACxM,QAAQ,CAAC8D,IAAI,CAACyF,KAAK,CAAC,CAAC,CAAC,CAC3C,CAAC,CAAA;AACD,QAAA,IAAI+C,EAAE,EAAE;UACNA,EAAE,CAACG,cAAc,EAAE,CAAA;AACnB,UAAA,OAAA;AACF,SAAA;AACF,OAAA;;AAEA;MACA,IAAI9L,kBAAkB,KAAK,IAAI,EAAE;AAC/B,QAAA,OAAA;AACF,OAAA;;AAEA;AACAjH,MAAAA,MAAM,CAAC2S,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;KACtB,EAAE,CAACrM,QAAQ,EAAEqL,qBAAqB,EAAE1K,kBAAkB,CAAC,CAAC,CAAA;AAC3D,GAAA;AACF,CAAA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS+L,eAAeA,CAC7BC,QAA2C,EAC3C5D,OAA+B,EACzB;EACN,IAAI;AAAE6D,IAAAA,OAAAA;AAAQ,GAAC,GAAG7D,OAAO,IAAI,EAAE,CAAA;EAC/BhN,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpB,IAAA,IAAIhG,IAAI,GAAG8S,OAAO,IAAI,IAAI,GAAG;AAAEA,MAAAA,OAAAA;AAAQ,KAAC,GAAGpT,SAAS,CAAA;IACpDE,MAAM,CAACmT,gBAAgB,CAAC,cAAc,EAAEF,QAAQ,EAAE7S,IAAI,CAAC,CAAA;AACvD,IAAA,OAAO,MAAM;MACXJ,MAAM,CAACoT,mBAAmB,CAAC,cAAc,EAAEH,QAAQ,EAAE7S,IAAI,CAAC,CAAA;KAC3D,CAAA;AACH,GAAC,EAAE,CAAC6S,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAA;AACzB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnB,WAAWA,CAClBkB,QAA6C,EAC7C5D,OAA+B,EACzB;EACN,IAAI;AAAE6D,IAAAA,OAAAA;AAAQ,GAAC,GAAG7D,OAAO,IAAI,EAAE,CAAA;EAC/BhN,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpB,IAAA,IAAIhG,IAAI,GAAG8S,OAAO,IAAI,IAAI,GAAG;AAAEA,MAAAA,OAAAA;AAAQ,KAAC,GAAGpT,SAAS,CAAA;IACpDE,MAAM,CAACmT,gBAAgB,CAAC,UAAU,EAAEF,QAAQ,EAAE7S,IAAI,CAAC,CAAA;AACnD,IAAA,OAAO,MAAM;MACXJ,MAAM,CAACoT,mBAAmB,CAAC,UAAU,EAAEH,QAAQ,EAAE7S,IAAI,CAAC,CAAA;KACvD,CAAA;AACH,GAAC,EAAE,CAAC6S,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAA;AACzB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,SAASA,CAAC;EACjBC,IAAI;AACJpR,EAAAA,OAAAA;AAIF,CAAC,EAAE;AACD,EAAA,IAAIqR,OAAO,GAAGC,UAAU,CAACF,IAAI,CAAC,CAAA;EAE9BjR,KAAK,CAAC+D,SAAS,CAAC,MAAM;AACpB,IAAA,IAAImN,OAAO,CAACrS,KAAK,KAAK,SAAS,EAAE;AAC/B,MAAA,IAAIuS,OAAO,GAAGzT,MAAM,CAAC0T,OAAO,CAACxR,OAAO,CAAC,CAAA;AACrC,MAAA,IAAIuR,OAAO,EAAE;AACX;AACA;AACA;AACAE,QAAAA,UAAU,CAACJ,OAAO,CAACE,OAAO,EAAE,CAAC,CAAC,CAAA;AAChC,OAAC,MAAM;QACLF,OAAO,CAACK,KAAK,EAAE,CAAA;AACjB,OAAA;AACF,KAAA;AACF,GAAC,EAAE,CAACL,OAAO,EAAErR,OAAO,CAAC,CAAC,CAAA;EAEtBG,KAAK,CAAC+D,SAAS,CAAC,MAAM;IACpB,IAAImN,OAAO,CAACrS,KAAK,KAAK,SAAS,IAAI,CAACoS,IAAI,EAAE;MACxCC,OAAO,CAACK,KAAK,EAAE,CAAA;AACjB,KAAA;AACF,GAAC,EAAE,CAACL,OAAO,EAAED,IAAI,CAAC,CAAC,CAAA;AACrB,CAAA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/H,sBAAsBA,CAC7BvE,EAAM,EACN5G,IAAwC,GAAG,EAAE,EAC7C;AACA,EAAA,IAAI+D,SAAS,GAAG9B,KAAK,CAACgH,UAAU,CAACjH,qBAAqB,CAAC,CAAA;AAEvD,EAAA,EACE+B,SAAS,IAAI,IAAI,CAAArF,GADnBmP,gBAAS,CAEP,KAAA,EAAA,uFAAuF,GACrF,mEAAmE,EAH9D,GAAA,KAAA,CAAA,CAAA;EAMT,IAAI;AAAEhP,IAAAA,QAAAA;AAAS,GAAC,GAAG8O,oBAAoB,CACrCJ,cAAc,CAACpC,sBACjB,CAAC,CAAA;AACD,EAAA,IAAIvB,IAAI,GAAGoB,eAAe,CAACpE,EAAE,EAAE;IAAEgC,QAAQ,EAAE5I,IAAI,CAAC4I,QAAAA;AAAS,GAAC,CAAC,CAAA;AAC3D,EAAA,IAAI,CAAC7E,SAAS,CAAC5B,eAAe,EAAE;AAC9B,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,IAAIsR,WAAW,GACbrU,aAAa,CAAC2E,SAAS,CAAC0B,eAAe,CAACoE,QAAQ,EAAEhL,QAAQ,CAAC,IAC3DkF,SAAS,CAAC0B,eAAe,CAACoE,QAAQ,CAAA;AACpC,EAAA,IAAI6J,QAAQ,GACVtU,aAAa,CAAC2E,SAAS,CAAC2B,YAAY,CAACmE,QAAQ,EAAEhL,QAAQ,CAAC,IACxDkF,SAAS,CAAC2B,YAAY,CAACmE,QAAQ,CAAA;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,OACE8J,SAAS,CAAC/J,IAAI,CAACC,QAAQ,EAAE6J,QAAQ,CAAC,IAAI,IAAI,IAC1CC,SAAS,CAAC/J,IAAI,CAACC,QAAQ,EAAE4J,WAAW,CAAC,IAAI,IAAI,CAAA;AAEjD,CAAA;;AAIA;;;;"}