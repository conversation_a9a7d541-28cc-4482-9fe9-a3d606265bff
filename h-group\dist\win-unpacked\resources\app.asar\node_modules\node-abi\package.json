{"name": "node-abi", "version": "3.75.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "main": "index.js", "files": ["abi_registry.json"], "repository": {"type": "git", "url": "git+https://github.com/electron/node-abi.git"}, "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/electron/node-abi#readme", "devDependencies": {"tape": "^5.3.1"}, "dependencies": {"semver": "^7.3.5"}, "engines": {"node": ">=10"}, "publishConfig": {"provenance": true}}