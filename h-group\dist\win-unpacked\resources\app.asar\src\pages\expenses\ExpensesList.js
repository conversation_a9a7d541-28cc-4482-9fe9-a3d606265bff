const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const ExpensesList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [expenses, setExpenses] = useState([]);
  const [filteredExpenses, setFilteredExpenses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [categories, setCategories] = useState([]);

  // إحصائيات المصروفات
  const [stats, setStats] = useState({
    totalExpenses: 0,
    recurringExpenses: 0,
    oneTimeExpenses: 0,
    thisMonthExpenses: 0
  });

  // تحميل المصروفات
  useEffect(() => {
    const fetchExpenses = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع المصروفات
        const data = await window.api.factoryExpenses.getAll();
        setExpenses(data);
        setFilteredExpenses(data);

        // استخراج الفئات الفريدة
        const uniqueCategories = [...new Set(data.map(expense => expense.category))].filter(Boolean);
        setCategories(uniqueCategories);

        // حساب الإحصائيات
        const recurringExpenses = data.filter(expense => expense.recurring);
        const oneTimeExpenses = data.filter(expense => !expense.recurring);

        // حساب مصروفات الشهر الحالي
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        const thisMonthExpenses = data.filter(expense => {
          const expenseDate = new Date(expense.expense_date);
          return expenseDate >= firstDayOfMonth && expenseDate <= lastDayOfMonth;
        });

        const totalAmount = data.reduce((sum, expense) => sum + expense.amount, 0);
        const thisMonthAmount = thisMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);

        setStats({
          totalExpenses: totalAmount,
          recurringExpenses: recurringExpenses.length,
          oneTimeExpenses: oneTimeExpenses.length,
          thisMonthExpenses: thisMonthAmount
        });

      } catch (error) {
        console.error('خطأ في تحميل المصروفات:', error);
        setError('حدث خطأ أثناء تحميل المصروفات. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchExpenses();
  }, []);

  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...expenses];

    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(expense =>
        expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expense.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // تطبيق فلتر الفئة
    if (categoryFilter !== 'all') {
      result = result.filter(expense => expense.category === categoryFilter);
    }

    // تطبيق فلتر التاريخ
    if (dateRange.startDate && dateRange.endDate) {
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      endDate.setHours(23, 59, 59, 999); // نهاية اليوم

      result = result.filter(expense => {
        const expenseDate = new Date(expense.expense_date);
        return expenseDate >= startDate && expenseDate <= endDate;
      });
    }

    setFilteredExpenses(result);
  }, [expenses, searchTerm, categoryFilter, dateRange]);

  // تغيير فلتر التاريخ
  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
    setCategoryFilter('all');
    setDateRange({
      startDate: '',
      endDate: ''
    });
  };

  // إنشاء مصروف جديد
  const handleCreateExpense = () => {
    if (!hasPermission('expenses_create')) {
      alert('ليس لديك صلاحية لإنشاء مصروف جديد');
      return;
    }

    navigate('/expenses/new');
  };

  // تعديل المصروف
  const handleEditExpense = (id) => {
    if (!hasPermission('expenses_edit')) {
      alert('ليس لديك صلاحية لتعديل المصروف');
      return;
    }

    navigate(`/expenses/edit/${id}`);
  };

  // حذف المصروف
  const handleDeleteExpense = async (id) => {
    if (!hasPermission('expenses_delete')) {
      alert('ليس لديك صلاحية لحذف المصروف');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
      try {
        const result = await window.api.factoryExpenses.delete(id);
        if (result.success) {
          // تحديث القائمة بعد الحذف
          setExpenses(expenses.filter(expense => expense.id !== id));
          alert('تم حذف المصروف بنجاح');
        } else {
          throw new Error('فشل في حذف المصروف');
        }
      } catch (error) {
        console.error('خطأ في حذف المصروف:', error);
        alert('حدث خطأ أثناء حذف المصروف. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  // تصدير المصروفات إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredExpenses.map(expense => ({
        'التاريخ': new Date(expense.expense_date).toLocaleDateString('ar-SA'),
        'الفئة': expense.category,
        'الوصف': expense.description,
        'المبلغ': `${expense.amount.toLocaleString()} ر.س`,
        'متكرر': expense.recurring ? 'نعم' : 'لا',
        'الفترة': expense.recurring ? expense.period : '-'
      }));

      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.api.exportToExcel(dataToExport, 'المصروفات');

      if (result.success) {
        alert(`تم تصدير المصروفات بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير المصروفات');
      }
    } catch (error) {
      console.error('خطأ في تصدير المصروفات:', error);
      alert('حدث خطأ أثناء تصدير المصروفات. يرجى المحاولة مرة أخرى.');
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل المصروفات...');
  }

  if (error) {
    return React.createElement('div', { className: 'alert alert-danger' }, error);
  }

  return React.createElement('div', { className: 'expenses-list-page' },
    React.createElement('div', { className: 'page-header' },
      React.createElement('h2', null, 'مصروفات المصنع'),
      React.createElement('div', { className: 'page-actions' },
        hasPermission('expenses_create') && React.createElement('button', {
          className: 'btn btn-primary',
          onClick: handleCreateExpense
        },
          React.createElement('i', { className: 'fas fa-plus' }),
          ' إضافة مصروف جديد'
        ),
        React.createElement('button', {
          className: 'btn btn-success',
          onClick: handleExportToExcel
        },
          React.createElement('i', { className: 'fas fa-file-excel' }),
          ' تصدير إلى Excel'
        )
      )
    ),
    React.createElement('div', { className: 'card' },
      React.createElement('div', { className: 'card-header' },
        React.createElement('h5', null, 'قائمة المصروفات'),
        React.createElement('span', { className: 'badge bg-primary' }, `${filteredExpenses.length} مصروف`)
      ),
      React.createElement('div', { className: 'card-body' },
        filteredExpenses.length === 0
          ? React.createElement('div', { className: 'alert alert-info' }, 'لا توجد مصروفات حالياً')
          : React.createElement('div', { className: 'table-responsive' },
              React.createElement('table', { className: 'table table-hover' },
                React.createElement('thead', null,
                  React.createElement('tr', null,
                    React.createElement('th', null, 'التاريخ'),
                    React.createElement('th', null, 'الفئة'),
                    React.createElement('th', null, 'الوصف'),
                    React.createElement('th', null, 'المبلغ'),
                    React.createElement('th', null, 'الإجراءات')
                  )
                ),
                React.createElement('tbody', null,
                  filteredExpenses.map(expense =>
                    React.createElement('tr', { key: expense.id },
                      React.createElement('td', null, new Date(expense.expense_date).toLocaleDateString('ar-SA')),
                      React.createElement('td', null, expense.category),
                      React.createElement('td', null, expense.description),
                      React.createElement('td', null, `${expense.amount.toLocaleString()} ر.س`),
                      React.createElement('td', null,
                        React.createElement('div', { className: 'btn-group' },
                          hasPermission('expenses_edit') && React.createElement('button', {
                            className: 'btn btn-sm btn-primary',
                            onClick: () => handleEditExpense(expense.id)
                          },
                            React.createElement('i', { className: 'fas fa-edit' })
                          ),
                          hasPermission('expenses_delete') && React.createElement('button', {
                            className: 'btn btn-sm btn-danger',
                            onClick: () => handleDeleteExpense(expense.id)
                          },
                            React.createElement('i', { className: 'fas fa-trash' })
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
      )
    )
  );
};

module.exports = ExpensesList;
