const React = require('react');
const { useState, useEffect } = React;
const { useNavigate, useParams } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const EditInstallment = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [installment, setInstallment] = useState({
    amount: 0,
    due_date: '',
    status: 'غير مدفوع',
    notes: ''
  });
  
  const [invoice, setInvoice] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // التحقق من الصلاحيات
  useEffect(() => {
    if (!hasPermission('invoices_edit')) {
      alert('ليس لديك صلاحية لتعديل الأقساط');
      navigate(-1);
    }
  }, [hasPermission, navigate]);
  
  // تحميل بيانات القسط
  useEffect(() => {
    const fetchInstallment = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل بيانات القسط
        const data = await window.api.installments.getById(id);
        
        if (!data) {
          throw new Error('القسط غير موجود');
        }
        
        setInstallment(data);
        
        // تحميل بيانات الفاتورة
        const invoiceData = await window.api.invoices.getById(data.invoice_id);
        setInvoice(invoiceData);
      } catch (error) {
        console.error('خطأ في تحميل بيانات القسط:', error);
        setError('حدث خطأ أثناء تحميل بيانات القسط. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      fetchInstallment();
    }
  }, [id]);
  
  // تغيير قيمة في نموذج القسط
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // معالجة خاصة للقيم العددية
    if (name === 'amount') {
      setInstallment(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    } else {
      setInstallment(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // إرسال نموذج تعديل القسط
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // التحقق من صحة المدخلات
      if (installment.amount <= 0) {
        alert('يجب أن يكون المبلغ أكبر من صفر');
        return;
      }
      
      if (!installment.due_date) {
        alert('يجب تحديد تاريخ الاستحقاق');
        return;
      }
      
      // تعديل القسط
      const result = await window.api.installments.update(id, installment);
      
      if (result) {
        alert('تم تعديل القسط بنجاح');
        
        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'update_installment',
          action_details: `تعديل القسط رقم ${installment.installment_number} للفاتورة رقم ${invoice?.invoice_number}`
        });
        
        // العودة إلى الصفحة السابقة
        navigate(-1);
      } else {
        throw new Error('فشل في تعديل القسط');
      }
    } catch (error) {
      console.error('خطأ في تعديل القسط:', error);
      alert('حدث خطأ أثناء تعديل القسط. يرجى المحاولة مرة أخرى.');
    }
  };
  
  // دفع القسط
  const handlePayInstallment = async () => {
    try {
      if (installment.status === 'مدفوع') {
        alert('هذا القسط مدفوع بالفعل');
        return;
      }
      
      const paymentData = {
        payment_date: new Date().toISOString(),
        payment_method: 'نقدي',
        notes: `دفع القسط رقم ${installment.installment_number}`
      };
      
      const result = await window.api.installments.pay(id, paymentData);
      
      if (result.success) {
        alert('تم دفع القسط بنجاح');
        
        // تحديث بيانات القسط
        setInstallment(prev => ({
          ...prev,
          status: 'مدفوع',
          payment_date: paymentData.payment_date
        }));
        
        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'pay_installment',
          action_details: `دفع القسط رقم ${installment.installment_number} للفاتورة رقم ${invoice?.invoice_number}`
        });
      }
    } catch (error) {
      console.error('خطأ في دفع القسط:', error);
      alert('حدث خطأ أثناء دفع القسط. يرجى المحاولة مرة أخرى.');
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  return (
    <div className="edit-installment-page">
      <div className="page-header">
        <h2>تعديل القسط رقم {installment.installment_number}</h2>
        <button className="btn btn-secondary" onClick={() => navigate(-1)}>
          <i className="fas fa-arrow-right"></i> العودة
        </button>
      </div>
      
      {invoice && (
        <div className="alert alert-info">
          <strong>الفاتورة:</strong> {invoice.invoice_number} - 
          <strong>العميل:</strong> {invoice.customer_name} - 
          <strong>المبلغ الإجمالي:</strong> {invoice.total_amount.toLocaleString()} ر.س
        </div>
      )}
      
      <div className="card">
        <div className="card-header">بيانات القسط</div>
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">رقم القسط</label>
                  <input
                    type="text"
                    className="form-control"
                    value={installment.installment_number}
                    disabled
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">المبلغ</label>
                  <input
                    type="number"
                    className="form-control"
                    name="amount"
                    value={installment.amount}
                    onChange={handleChange}
                    min="0.01"
                    step="0.01"
                    required
                    disabled={installment.status === 'مدفوع'}
                  />
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">تاريخ الاستحقاق</label>
                  <input
                    type="date"
                    className="form-control"
                    name="due_date"
                    value={installment.due_date ? installment.due_date.split('T')[0] : ''}
                    onChange={handleChange}
                    required
                    disabled={installment.status === 'مدفوع'}
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">الحالة</label>
                  <select
                    className="form-control"
                    name="status"
                    value={installment.status}
                    onChange={handleChange}
                    disabled={installment.status === 'مدفوع'}
                  >
                    <option value="غير مدفوع">غير مدفوع</option>
                    <option value="متأخر">متأخر</option>
                    <option value="مدفوع">مدفوع</option>
                  </select>
                </div>
              </div>
            </div>
            
            {installment.payment_date && (
              <div className="row">
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">تاريخ الدفع</label>
                    <input
                      type="text"
                      className="form-control"
                      value={new Date(installment.payment_date).toLocaleDateString('ar-SA')}
                      disabled
                    />
                  </div>
                </div>
              </div>
            )}
            
            <div className="form-group">
              <label className="form-label">ملاحظات</label>
              <textarea
                className="form-control"
                name="notes"
                value={installment.notes}
                onChange={handleChange}
                rows="3"
              ></textarea>
            </div>
            
            <div className="form-actions">
              {installment.status !== 'مدفوع' && (
                <>
                  <button type="submit" className="btn btn-primary">
                    <i className="fas fa-save"></i> حفظ التعديلات
                  </button>
                  
                  <button type="button" className="btn btn-success" onClick={handlePayInstallment}>
                    <i className="fas fa-money-bill-wave"></i> دفع القسط
                  </button>
                </>
              )}
              
              <button type="button" className="btn btn-secondary" onClick={() => navigate(-1)}>
                <i className="fas fa-times"></i> إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = EditInstallment;
