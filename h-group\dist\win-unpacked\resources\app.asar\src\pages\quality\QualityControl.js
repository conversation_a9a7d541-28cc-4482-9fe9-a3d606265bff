const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate } = require('react-router-dom');

const QualityControl = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  
  const [order, setOrder] = useState(null);
  const [productionStages, setProductionStages] = useState([]);
  const [qualityChecks, setQualityChecks] = useState([]);
  const [defects, setDefects] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('checks'); // checks, defects, reports

  const [showCheckModal, setShowCheckModal] = useState(false);
  const [showDefectModal, setShowDefectModal] = useState(false);
  const [selectedStage, setSelectedStage] = useState(null);

  const [newCheck, setNewCheck] = useState({
    checkpoint_id: '',
    stage_id: '',
    inspector_id: '',
    status: 'قيد الفحص',
    quality_score: '',
    defects_found: '',
    corrective_actions: '',
    notes: ''
  });

  const [newDefect, setNewDefect] = useState({
    stage_id: '',
    defect_type: '',
    defect_description: '',
    severity: 'متوسط',
    detected_by: '',
    notes: ''
  });

  useEffect(() => {
    if (orderId) {
      loadQualityData();
    }
  }, [orderId]);

  const loadQualityData = async () => {
    try {
      setLoading(true);

      // تحميل بيانات الطلب
      const orderData = await window.electronAPI.orders.getById(parseInt(orderId));
      setOrder(orderData);

      // تحميل مراحل الإنتاج
      const stages = await window.electronAPI.productionStages.getByOrderId(parseInt(orderId));
      setProductionStages(stages);

      // تحميل فحوصات الجودة
      const checks = await window.electronAPI.qualityChecks.getByOrderId(parseInt(orderId));
      setQualityChecks(checks);

      // تحميل العيوب والإصلاحات
      const defectsData = await window.electronAPI.defectsRepairs.getByOrderId(parseInt(orderId));
      setDefects(defectsData);

      // تحميل العمال
      const workersData = await window.electronAPI.workers.getAll();
      setWorkers(workersData);

    } catch (error) {
      console.error('خطأ في تحميل بيانات فحص الجودة:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCheck = async (e) => {
    e.preventDefault();
    
    try {
      await window.electronAPI.qualityChecks.create({
        ...newCheck,
        order_id: parseInt(orderId),
        quality_score: newCheck.quality_score ? parseInt(newCheck.quality_score) : null
      });

      setShowCheckModal(false);
      setNewCheck({
        checkpoint_id: '',
        stage_id: '',
        inspector_id: '',
        status: 'قيد الفحص',
        quality_score: '',
        defects_found: '',
        corrective_actions: '',
        notes: ''
      });
      
      await loadQualityData();
      alert('تم إنشاء فحص الجودة بنجاح');
    } catch (error) {
      console.error('خطأ في إنشاء فحص الجودة:', error);
      alert('خطأ في إنشاء فحص الجودة: ' + error.message);
    }
  };

  const handleCreateDefect = async (e) => {
    e.preventDefault();
    
    try {
      await window.electronAPI.defectsRepairs.create({
        ...newDefect,
        order_id: parseInt(orderId)
      });

      setShowDefectModal(false);
      setNewDefect({
        stage_id: '',
        defect_type: '',
        defect_description: '',
        severity: 'متوسط',
        detected_by: '',
        notes: ''
      });
      
      await loadQualityData();
      alert('تم تسجيل العيب بنجاح');
    } catch (error) {
      console.error('خطأ في تسجيل العيب:', error);
      alert('خطأ في تسجيل العيب: ' + error.message);
    }
  };

  const handleUpdateCheck = async (checkId, status, score) => {
    try {
      await window.electronAPI.qualityChecks.update(checkId, {
        status,
        quality_score: score,
        approved_by: 1 // سيتم تحديثه لاحقاً بناءً على المستخدم الحالي
      });
      
      await loadQualityData();
      alert('تم تحديث فحص الجودة بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث فحص الجودة:', error);
      alert('خطأ في تحديث فحص الجودة: ' + error.message);
    }
  };

  const getQualityScore = () => {
    if (qualityChecks.length === 0) return 0;
    const passedChecks = qualityChecks.filter(check => check.status === 'مقبول');
    return Math.round((passedChecks.length / qualityChecks.length) * 100);
  };

  const getDefectsSeverityCount = () => {
    const counts = { 'بسيط': 0, 'متوسط': 0, 'خطير': 0, 'حرج': 0 };
    defects.forEach(defect => {
      counts[defect.severity] = (counts[defect.severity] || 0) + 1;
    });
    return counts;
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل بيانات فحص الجودة...')
    );
  }

  if (!order) {
    return React.createElement('div', { className: 'error-container' },
      React.createElement('h2', null, 'الطلب غير موجود'),
      React.createElement('button', { 
        className: 'btn btn-primary',
        onClick: () => navigate('/orders')
      }, 'العودة إلى الطلبات')
    );
  }

  const qualityScore = getQualityScore();
  const defectsCounts = getDefectsSeverityCount();

  return React.createElement('div', { className: 'quality-control-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('div', { className: 'header-info' },
          React.createElement('h1', { className: 'page-title' },
            React.createElement('i', { className: 'fas fa-award' }),
            ' فحص الجودة'
          ),
          React.createElement('div', { className: 'order-info' },
            React.createElement('span', { className: 'order-number' }, `طلب رقم: ${order.order_number}`),
            React.createElement('span', { className: 'customer-name' }, order.customer_name)
          )
        ),
        React.createElement('div', { className: 'header-actions' },
          React.createElement('button', {
            className: 'btn btn-primary',
            onClick: () => setShowCheckModal(true)
          },
            React.createElement('i', { className: 'fas fa-plus' }),
            ' فحص جديد'
          ),
          React.createElement('button', {
            className: 'btn btn-warning',
            onClick: () => setShowDefectModal(true)
          },
            React.createElement('i', { className: 'fas fa-exclamation-triangle' }),
            ' تسجيل عيب'
          )
        )
      )
    ),

    // مؤشرات الجودة
    React.createElement('div', { className: 'quality-indicators' },
      React.createElement('div', { className: 'indicator-card quality-score' },
        React.createElement('div', { className: 'indicator-icon' },
          React.createElement('i', { className: 'fas fa-star' })
        ),
        React.createElement('div', { className: 'indicator-content' },
          React.createElement('h3', null, 'نسبة الجودة'),
          React.createElement('div', { className: 'indicator-value' }, `${qualityScore}%`),
          React.createElement('div', { className: 'indicator-subtitle' }, 
            `${qualityChecks.filter(c => c.status === 'مقبول').length} من ${qualityChecks.length} فحص`
          )
        )
      ),
      React.createElement('div', { className: 'indicator-card defects-count' },
        React.createElement('div', { className: 'indicator-icon' },
          React.createElement('i', { className: 'fas fa-bug' })
        ),
        React.createElement('div', { className: 'indicator-content' },
          React.createElement('h3', null, 'العيوب المكتشفة'),
          React.createElement('div', { className: 'indicator-value' }, defects.length),
          React.createElement('div', { className: 'defects-breakdown' },
            Object.entries(defectsCounts).map(([severity, count]) =>
              count > 0 && React.createElement('span', { 
                key: severity, 
                className: `defect-severity ${severity}` 
              }, `${severity}: ${count}`)
            )
          )
        )
      ),
      React.createElement('div', { className: 'indicator-card checks-today' },
        React.createElement('div', { className: 'indicator-icon' },
          React.createElement('i', { className: 'fas fa-clipboard-check' })
        ),
        React.createElement('div', { className: 'indicator-content' },
          React.createElement('h3', null, 'فحوصات اليوم'),
          React.createElement('div', { className: 'indicator-value' }, 
            qualityChecks.filter(c => 
              c.check_date?.startsWith(new Date().toISOString().split('T')[0])
            ).length
          )
        )
      )
    ),

    // تبويبات المحتوى
    React.createElement('div', { className: 'quality-tabs' },
      React.createElement('div', { className: 'tab-buttons' },
        React.createElement('button', {
          className: `tab-btn ${activeTab === 'checks' ? 'active' : ''}`,
          onClick: () => setActiveTab('checks')
        }, 'فحوصات الجودة'),
        React.createElement('button', {
          className: `tab-btn ${activeTab === 'defects' ? 'active' : ''}`,
          onClick: () => setActiveTab('defects')
        }, 'العيوب والإصلاحات'),
        React.createElement('button', {
          className: `tab-btn ${activeTab === 'reports' ? 'active' : ''}`,
          onClick: () => setActiveTab('reports')
        }, 'التقارير')
      )
    ),

    // محتوى التبويبات
    React.createElement('div', { className: 'tab-content' },
      // تبويب فحوصات الجودة
      activeTab === 'checks' && React.createElement('div', { className: 'checks-tab' },
        React.createElement('div', { className: 'checks-table-container' },
          React.createElement('table', { className: 'checks-table' },
            React.createElement('thead', null,
              React.createElement('tr', null,
                React.createElement('th', null, 'المرحلة'),
                React.createElement('th', null, 'نقطة الفحص'),
                React.createElement('th', null, 'المفتش'),
                React.createElement('th', null, 'تاريخ الفحص'),
                React.createElement('th', null, 'الحالة'),
                React.createElement('th', null, 'درجة الجودة'),
                React.createElement('th', null, 'الإجراءات')
              )
            ),
            React.createElement('tbody', null,
              qualityChecks.length > 0
                ? qualityChecks.map(check =>
                    React.createElement('tr', { key: check.id },
                      React.createElement('td', null, check.stage_name),
                      React.createElement('td', null, check.checkpoint_name),
                      React.createElement('td', null, check.inspector_name || 'غير محدد'),
                      React.createElement('td', null, 
                        new Date(check.check_date).toLocaleDateString('ar-SA')
                      ),
                      React.createElement('td', null,
                        React.createElement('span', { 
                          className: `status-badge ${check.status === 'مقبول' ? 'success' : 
                                                    check.status === 'مرفوض' ? 'danger' : 'warning'}` 
                        }, check.status)
                      ),
                      React.createElement('td', null,
                        check.quality_score ? `${check.quality_score}/10` : '-'
                      ),
                      React.createElement('td', null,
                        React.createElement('div', { className: 'action-buttons' },
                          check.status === 'قيد الفحص' && [
                            React.createElement('button', {
                              key: 'approve',
                              className: 'btn btn-sm btn-success',
                              onClick: () => {
                                const score = prompt('درجة الجودة (1-10):', '8');
                                if (score && !isNaN(score) && score >= 1 && score <= 10) {
                                  handleUpdateCheck(check.id, 'مقبول', parseInt(score));
                                }
                              },
                              title: 'قبول الفحص'
                            },
                              React.createElement('i', { className: 'fas fa-check' })
                            ),
                            React.createElement('button', {
                              key: 'reject',
                              className: 'btn btn-sm btn-danger',
                              onClick: () => handleUpdateCheck(check.id, 'مرفوض', 0),
                              title: 'رفض الفحص'
                            },
                              React.createElement('i', { className: 'fas fa-times' })
                            )
                          ]
                        )
                      )
                    )
                  )
                : React.createElement('tr', null,
                    React.createElement('td', { colSpan: 7, className: 'no-data' },
                      'لا توجد فحوصات جودة'
                    )
                  )
            )
          )
        )
      ),

      // تبويب العيوب والإصلاحات
      activeTab === 'defects' && React.createElement('div', { className: 'defects-tab' },
        React.createElement('div', { className: 'defects-table-container' },
          React.createElement('table', { className: 'defects-table' },
            React.createElement('thead', null,
              React.createElement('tr', null,
                React.createElement('th', null, 'نوع العيب'),
                React.createElement('th', null, 'الوصف'),
                React.createElement('th', null, 'الخطورة'),
                React.createElement('th', null, 'المكتشف'),
                React.createElement('th', null, 'تاريخ الاكتشاف'),
                React.createElement('th', null, 'الحالة'),
                React.createElement('th', null, 'الإجراءات')
              )
            ),
            React.createElement('tbody', null,
              defects.length > 0
                ? defects.map(defect =>
                    React.createElement('tr', { key: defect.id },
                      React.createElement('td', null, defect.defect_type),
                      React.createElement('td', null, defect.defect_description),
                      React.createElement('td', null,
                        React.createElement('span', { 
                          className: `severity-badge ${defect.severity}` 
                        }, defect.severity)
                      ),
                      React.createElement('td', null, defect.detected_by_name || 'غير محدد'),
                      React.createElement('td', null, 
                        new Date(defect.detected_date).toLocaleDateString('ar-SA')
                      ),
                      React.createElement('td', null,
                        React.createElement('span', { 
                          className: `status-badge ${defect.status === 'مصلح' ? 'success' : 'warning'}` 
                        }, defect.status)
                      ),
                      React.createElement('td', null,
                        React.createElement('div', { className: 'action-buttons' },
                          defect.status === 'مكتشف' && React.createElement('button', {
                            className: 'btn btn-sm btn-primary',
                            onClick: () => {
                              const description = prompt('وصف الإصلاح:');
                              const cost = prompt('تكلفة الإصلاح:');
                              const hours = prompt('ساعات الإصلاح:');
                              
                              if (description) {
                                window.electronAPI.defectsRepairs.updateRepair(defect.id, {
                                  repair_description: description,
                                  repair_cost: parseFloat(cost) || 0,
                                  repair_time_hours: parseFloat(hours) || 0,
                                  repaired_by: 1 // سيتم تحديثه بناءً على المستخدم الحالي
                                }).then(() => {
                                  loadQualityData();
                                  alert('تم تسجيل الإصلاح بنجاح');
                                });
                              }
                            },
                            title: 'تسجيل إصلاح'
                          },
                            React.createElement('i', { className: 'fas fa-wrench' })
                          )
                        )
                      )
                    )
                  )
                : React.createElement('tr', null,
                    React.createElement('td', { colSpan: 7, className: 'no-data' },
                      'لا توجد عيوب مسجلة'
                    )
                  )
            )
          )
        )
      ),

      // تبويب التقارير
      activeTab === 'reports' && React.createElement('div', { className: 'reports-tab' },
        React.createElement('div', { className: 'quality-report' },
          React.createElement('h3', null, 'تقرير الجودة الشامل'),
          React.createElement('div', { className: 'report-summary' },
            React.createElement('div', { className: 'summary-section' },
              React.createElement('h4', null, 'ملخص الفحوصات'),
              React.createElement('ul', null,
                React.createElement('li', null, `إجمالي الفحوصات: ${qualityChecks.length}`),
                React.createElement('li', null, `فحوصات مقبولة: ${qualityChecks.filter(c => c.status === 'مقبول').length}`),
                React.createElement('li', null, `فحوصات مرفوضة: ${qualityChecks.filter(c => c.status === 'مرفوض').length}`),
                React.createElement('li', null, `نسبة النجاح: ${qualityScore}%`)
              )
            ),
            React.createElement('div', { className: 'summary-section' },
              React.createElement('h4', null, 'ملخص العيوب'),
              React.createElement('ul', null,
                React.createElement('li', null, `إجمالي العيوب: ${defects.length}`),
                React.createElement('li', null, `عيوب مصلحة: ${defects.filter(d => d.status === 'مصلح').length}`),
                React.createElement('li', null, `عيوب قيد الإصلاح: ${defects.filter(d => d.status === 'قيد الإصلاح').length}`),
                React.createElement('li', null, `تكلفة الإصلاحات: ${defects.reduce((sum, d) => sum + (d.repair_cost || 0), 0)} د.ل`)
              )
            )
          )
        )
      )
    )
  );
};

module.exports = QualityControl;
