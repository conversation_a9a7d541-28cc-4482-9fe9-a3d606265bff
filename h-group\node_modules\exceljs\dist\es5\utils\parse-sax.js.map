{"version": 3, "file": "parse-sax.js", "names": ["SaxesP<PERSON><PERSON>", "require", "PassThrough", "bufferToString", "module", "exports", "iterable", "pipe", "Symbol", "asyncIterator", "saxes<PERSON><PERSON><PERSON>", "error", "on", "err", "events", "value", "push", "eventType", "chunk", "write"], "sources": ["../../../lib/utils/parse-sax.js"], "sourcesContent": ["const {SaxesParser} = require('saxes');\nconst {PassThrough} = require('readable-stream');\nconst {bufferToString} = require('./browser-buffer-decode');\n\nmodule.exports = async function* (iterable) {\n  // TODO: Remove once node v8 is deprecated\n  // Detect and upgrade old streams\n  if (iterable.pipe && !iterable[Symbol.asyncIterator]) {\n    iterable = iterable.pipe(new PassThrough());\n  }\n  const saxesParser = new SaxesParser();\n  let error;\n  saxesParser.on('error', err => {\n    error = err;\n  });\n  let events = [];\n  saxesParser.on('opentag', value => events.push({eventType: 'opentag', value}));\n  saxesParser.on('text', value => events.push({eventType: 'text', value}));\n  saxesParser.on('closetag', value => events.push({eventType: 'closetag', value}));\n  for await (const chunk of iterable) {\n    saxesParser.write(bufferToString(chunk));\n    // saxesParser.write and saxesParser.on() are synchronous,\n    // so we can only reach the below line once all events have been emitted\n    if (error) throw error;\n    // As a performance optimization, we gather all events instead of passing\n    // them one by one, which would cause each event to go through the event queue\n    yield events;\n    events = [];\n  }\n};\n"], "mappings": ";;AAAA,MAAM;EAACA;AAAW,CAAC,GAAGC,OAAO,CAAC,OAAO,CAAC;AACtC,MAAM;EAACC;AAAW,CAAC,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAChD,MAAM;EAACE;AAAc,CAAC,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AAE3DG,MAAM,CAACC,OAAO,GAAG,iBAAiBC,QAAQ,EAAE;EAC1C;EACA;EACA,IAAIA,QAAQ,CAACC,IAAI,IAAI,CAACD,QAAQ,CAACE,MAAM,CAACC,aAAa,CAAC,EAAE;IACpDH,QAAQ,GAAGA,QAAQ,CAACC,IAAI,CAAC,IAAIL,WAAW,CAAC,CAAC,CAAC;EAC7C;EACA,MAAMQ,WAAW,GAAG,IAAIV,WAAW,CAAC,CAAC;EACrC,IAAIW,KAAK;EACTD,WAAW,CAACE,EAAE,CAAC,OAAO,EAAEC,GAAG,IAAI;IAC7BF,KAAK,GAAGE,GAAG;EACb,CAAC,CAAC;EACF,IAAIC,MAAM,GAAG,EAAE;EACfJ,WAAW,CAACE,EAAE,CAAC,SAAS,EAAEG,KAAK,IAAID,MAAM,CAACE,IAAI,CAAC;IAACC,SAAS,EAAE,SAAS;IAAEF;EAAK,CAAC,CAAC,CAAC;EAC9EL,WAAW,CAACE,EAAE,CAAC,MAAM,EAAEG,KAAK,IAAID,MAAM,CAACE,IAAI,CAAC;IAACC,SAAS,EAAE,MAAM;IAAEF;EAAK,CAAC,CAAC,CAAC;EACxEL,WAAW,CAACE,EAAE,CAAC,UAAU,EAAEG,KAAK,IAAID,MAAM,CAACE,IAAI,CAAC;IAACC,SAAS,EAAE,UAAU;IAAEF;EAAK,CAAC,CAAC,CAAC;EAChF,WAAW,MAAMG,KAAK,IAAIZ,QAAQ,EAAE;IAClCI,WAAW,CAACS,KAAK,CAAChB,cAAc,CAACe,KAAK,CAAC,CAAC;IACxC;IACA;IACA,IAAIP,KAAK,EAAE,MAAMA,KAAK;IACtB;IACA;IACA,MAAMG,MAAM;IACZA,MAAM,GAAG,EAAE;EACb;AACF,CAAC"}