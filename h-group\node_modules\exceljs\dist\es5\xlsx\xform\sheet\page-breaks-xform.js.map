{"version": 3, "file": "page-breaks-xform.js", "names": ["BaseXform", "require", "PageBreaksXform", "tag", "render", "xmlStream", "model", "leafNode", "parseOpen", "node", "name", "attributes", "ref", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/page-breaks-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass PageBreaksXform extends BaseXform {\n  get tag() {\n    return 'brk';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode('brk', model);\n  }\n\n  parseOpen(node) {\n    if (node.name === 'brk') {\n      this.model = node.attributes.ref;\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = PageBreaksXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,eAAe,SAASF,SAAS,CAAC;EACtC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,KAAK,EAAED,KAAK,CAAC;EAClC;EAEAE,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;MACvB,IAAI,CAACJ,KAAK,GAAGG,IAAI,CAACE,UAAU,CAACC,GAAG;MAChC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGd,eAAe"}