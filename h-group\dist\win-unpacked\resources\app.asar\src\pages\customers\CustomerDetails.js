const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const CustomerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [customer, setCustomer] = useState(null);
  const [customerOrders, setCustomerOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تحميل بيانات العميل
  useEffect(() => {
    const fetchCustomerData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل بيانات العميل
        const customerData = await window.api.customers.getById(id);
        if (!customerData) {
          throw new Error('لم يتم العثور على العميل');
        }
        setCustomer(customerData);

        // تحميل طلبات العميل
        const orders = await window.api.orders.getByCustomerId(id);
        setCustomerOrders(orders);

      } catch (error) {
        console.error('خطأ في تحميل بيانات العميل:', error);
        setError('حدث خطأ أثناء تحميل بيانات العميل. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomerData();
  }, [id]);

  // تعديل العميل
  const handleEditCustomer = () => {
    if (!hasPermission('customers_edit')) {
      alert('ليس لديك صلاحية لتعديل العميل');
      return;
    }

    navigate(`/customers/edit/${id}`);
  };

  // حذف العميل
  const handleDeleteCustomer = async () => {
    if (!hasPermission('customers_delete')) {
      alert('ليس لديك صلاحية لحذف العميل');
      return;
    }

    if (customerOrders.length > 0) {
      alert('لا يمكن حذف العميل لأنه مرتبط بطلبات. يجب حذف الطلبات أولاً.');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        const result = await window.api.customers.delete(id);
        if (result.success) {
          alert('تم حذف العميل بنجاح');
          navigate('/customers');
        } else {
          throw new Error('فشل في حذف العميل');
        }
      } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        alert('حدث خطأ أثناء حذف العميل. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  // إنشاء طلب جديد للعميل
  const handleCreateOrder = () => {
    if (!hasPermission('orders_create')) {
      alert('ليس لديك صلاحية لإنشاء طلب جديد');
      return;
    }

    navigate(`/orders/new?customer=${id}`);
  };

  if (loading) {
    return <div className="loading">جاري تحميل بيانات العميل...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (!customer) {
    return <div className="alert alert-warning">لم يتم العثور على العميل</div>;
  }

  return (
    <div className="customer-details-page">
      <div className="page-header">
        <h2>تفاصيل العميل</h2>
        <div className="page-actions">
          {hasPermission('customers_edit') && (
            <button className="btn btn-primary" onClick={handleEditCustomer}>
              <i className="fas fa-edit"></i> تعديل
            </button>
          )}

          {hasPermission('customers_delete') && (
            <button className="btn btn-danger" onClick={handleDeleteCustomer}>
              <i className="fas fa-trash"></i> حذف
            </button>
          )}

          {hasPermission('orders_create') && (
            <button className="btn btn-success" onClick={handleCreateOrder}>
              <i className="fas fa-plus"></i> إنشاء طلب جديد
            </button>
          )}

          <Link to="/customers" className="btn btn-secondary">
            <i className="fas fa-arrow-right"></i> العودة للقائمة
          </Link>
        </div>
      </div>

      {/* بيانات العميل */}
      <div className="card mb-4">
        <div className="card-header">معلومات العميل</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="customer-info-item">
                <span className="info-label">الاسم:</span>
                <span className="info-value">{customer.name}</span>
              </div>

              <div className="customer-info-item">
                <span className="info-label">رقم الهاتف:</span>
                <span className="info-value">{customer.phone || '-'}</span>
              </div>

              <div className="customer-info-item">
                <span className="info-label">البريد الإلكتروني:</span>
                <span className="info-value">{customer.email || '-'}</span>
              </div>
            </div>

            <div className="col-md-6">
              <div className="customer-info-item">
                <span className="info-label">العنوان:</span>
                <span className="info-value">{customer.address || '-'}</span>
              </div>

              <div className="customer-info-item">
                <span className="info-label">ملاحظات:</span>
                <span className="info-value">{customer.notes || '-'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* طلبات العميل */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>طلبات العميل</h5>
            <span className="badge bg-primary">{customerOrders.length} طلب</span>
          </div>
        </div>
        <div className="card-body">
          {customerOrders.length === 0 ? (
            <div className="alert alert-info">لا توجد طلبات لهذا العميل</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>المنتج</th>
                    <th>تاريخ الطلب</th>
                    <th>تاريخ التسليم</th>
                    <th>الحالة</th>
                    <th>السعر النهائي</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {customerOrders.map(order => (
                    <tr key={order.id}>
                      <td>{order.order_number}</td>
                      <td>{order.product_name || '-'}</td>
                      <td>{new Date(order.order_date).toLocaleDateString('ar-SA')}</td>
                      <td>{order.delivery_date ? new Date(order.delivery_date).toLocaleDateString('ar-SA') : '-'}</td>
                      <td>
                        <span className={`status-badge ${order.status}`}>
                          {order.status}
                        </span>
                      </td>
                      <td>{order.final_price.toLocaleString()} ر.س</td>
                      <td>
                        <Link to={`/orders/${order.id}`} className="btn btn-sm btn-info">
                          <i className="fas fa-eye"></i>
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = CustomerDetails;
