{"version": 3, "file": "react-router.production.min.js", "sources": ["../../lib/context.ts", "../../lib/hooks.tsx", "../../lib/deprecations.ts", "../../lib/components.tsx", "../../index.ts"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n  viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  AgnosticRouteMatch,\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_decodePath as decodePath,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator, static: isStatic } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches =\n    !isStatic &&\n    dataRouterState &&\n    dataRouterState.matches &&\n    dataRouterState.matches.length > 0\n      ? (dataRouterState.matches as AgnosticRouteMatch<string, RouteObject>[])\n      : matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else if (\n      future?.v7_partialHydration &&\n      parentMatches.length === 0 &&\n      !dataRouterState.initialized &&\n      dataRouterState.matches.length > 0\n    ) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type { FutureConfig as RouterFutureConfig } from \"@remix-run/router\";\nimport type { FutureConfig as RenderFutureConfig } from \"./components\";\n\nconst alreadyWarned: { [key: string]: boolean } = {};\n\nexport function warnOnce(key: string, message: string): void {\n  if (__DEV__ && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\n\nconst logDeprecation = (flag: string, msg: string, link: string) =>\n  warnOnce(\n    flag,\n    `⚠️ React Router Future Flag Warning: ${msg}. ` +\n      `You can use the \\`${flag}\\` future flag to opt-in early. ` +\n      `For more information, see ${link}.`\n  );\n\nexport function logV6DeprecationWarnings(\n  renderFuture: Partial<RenderFutureConfig> | undefined,\n  routerFuture?: Omit<RouterFutureConfig, \"v7_prependBasename\">\n) {\n  if (renderFuture?.v7_startTransition === undefined) {\n    logDeprecation(\n      \"v7_startTransition\",\n      \"React Router will begin wrapping state updates in `React.startTransition` in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\"\n    );\n  }\n\n  if (\n    renderFuture?.v7_relativeSplatPath === undefined &&\n    (!routerFuture || !routerFuture.v7_relativeSplatPath)\n  ) {\n    logDeprecation(\n      \"v7_relativeSplatPath\",\n      \"Relative route resolution within Splat routes is changing in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\"\n    );\n  }\n\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\n        \"v7_fetcherPersist\",\n        \"The persistence behavior of fetchers is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\"\n      );\n    }\n\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\n        \"v7_normalizeFormMethod\",\n        \"Casing of `formMethod` fields is being normalized to uppercase in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\"\n      );\n    }\n\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\n        \"v7_partialHydration\",\n        \"`RouterProvider` hydration behavior is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\"\n      );\n    }\n\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\n        \"v7_skipActionErrorRevalidation\",\n        \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\"\n      );\n    }\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\nimport { logV6DeprecationWarnings } from \"./deprecations\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [router, future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AgnosticPatchRoutesOnNavigationFunction,\n  AgnosticPatchRoutesOnNavigationFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\nimport { logV6DeprecationWarnings } from \"./lib/deprecations\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport type PatchRoutesOnNavigationFunctionArgs =\n  AgnosticPatchRoutesOnNavigationFunctionArgs<RouteObject, RouteMatch>;\n\nexport type PatchRoutesOnNavigationFunction =\n  AgnosticPatchRoutesOnNavigationFunction<RouteObject, RouteMatch>;\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    dataStrategy?: DataStrategyFunction;\n    patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n  logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings,\n};\n"], "names": ["DataRouterContext", "React", "createContext", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useInRouterContext", "useContext", "useLocation", "invariant", "location", "useIsomorphicLayoutEffect", "cb", "static", "useLayoutEffect", "useNavigate", "router", "useDataRouterContext", "DataRouterHook", "UseNavigateStable", "id", "useCurrentRouteId", "DataRouterStateHook", "activeRef", "useRef", "current", "useCallback", "to", "options", "navigate", "_extends", "fromRouteId", "useNavigateStable", "dataRouterContext", "basename", "future", "navigator", "pathname", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getResolveToMatches", "v7_relativeSplatPath", "go", "path", "resolveTo", "parse", "relative", "joinPaths", "replace", "push", "state", "useNavigateUnstable", "OutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useResolvedPath", "_temp2", "useMemo", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "isStatic", "parentMatches", "routeMatch", "length", "parentParams", "params", "parentPathnameBase", "pathnameBase", "route", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "slice", "join", "matchRoutes", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "search", "hash", "key", "navigationType", "NavigationType", "Action", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "preStyles", "padding", "backgroundColor", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "Component", "constructor", "props", "super", "this", "revalidation", "undefined", "componentDidCatch", "errorInfo", "console", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "_dataRouterState", "_future", "errors", "v7_partialHydration", "initialized", "errorIndex", "findIndex", "m", "UNSAFE_invariant", "Math", "min", "renderFallback", "fallbackIndex", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "lazy", "reduceRight", "index", "shouldRenderHydrateFallback", "alreadyWarned", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "<PERSON><PERSON><PERSON>", "ctx", "useDataRouterState", "useRouteContext", "thisRoute", "_state$errors", "UseRouteError", "routeId", "useAsyncValue", "_data", "blockerId", "logV6DeprecationWarnings", "renderFuture", "routerFuture", "v7_startTransition", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_skipActionErrorRevalidation", "startTransitionImpl", "DataRoutes", "_ref2", "Route", "_props", "Router", "_ref5", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "stripBasename", "AwaitRenderStatus", "neverSettledPromise", "Promise", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "resolve", "promise", "pending", "renderError", "reject", "catch", "defineProperty", "get", "_tracked", "success", "then", "data", "_error", "Aborted<PERSON>eferredError", "ResolveAwait", "_ref8", "to<PERSON><PERSON>", "createRoutesFromChildren", "parentPath", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "apply", "caseSensitive", "action", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "mapRouteProperties", "updates", "_ref7", "_ref3", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "setStateImpl", "useState", "setState", "newState", "listen", "useEffect", "_ref4", "UNSAFE_getResolveToMatches", "jsonPath", "fallbackElement", "subscribe", "createHref", "n", "opts", "preventScrollReset", "historyAction", "_ref6", "UseRouteId", "createRouter", "v7_prependBasename", "hydrationData", "dataStrategy", "patchRoutesOnNavigation", "initialize", "UseActionData", "UseLoaderData", "actionData", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "blockerFunction", "arg", "currentLocation", "nextLocation", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "IDLE_BLOCKER", "_temp", "joinedPathname", "pattern", "matchPath", "decodePath", "UseMatches", "convertRouteMatchToUiMatch", "UseNavigation", "navigation", "UseRevalidator", "revalidate", "UseRouteLoaderData"], "mappings": ";;;;;;;;;;q2BAgFO,MAAMA,EACXC,EAAMC,cAA8C,MAKzCC,EAAyBF,EAAMC,cAE1C,MAKWE,EAAeH,EAAMC,cAAqC,MAyC1DG,EAAoBJ,EAAMC,cACrC,MAYWI,EAAkBL,EAAMC,cACnC,MAaWK,EAAeN,EAAMC,cAAkC,CAClEM,OAAQ,KACRC,QAAS,GACTC,aAAa,IAOFC,EAAoBV,EAAMC,cAAmB,MCjFnD,SAASU,IACd,OAA4C,MAArCX,EAAMY,WAAWP,EAC1B,CAYO,SAASQ,IAQd,OANEF,KADFG,EAAAA,kBAAS,GAOFd,EAAMY,WAAWP,GAAiBU,QAC3C,CAkDA,SAASC,EACPC,GAEejB,EAAMY,WAAWR,GAAmBc,QAKjDlB,EAAMmB,gBAAgBF,EAE1B,CAQO,SAASG,IACd,IAAIX,YAAEA,GAAgBT,EAAMY,WAAWN,GAGvC,OAAOG,EA24BT,WACE,IAAIY,OAAEA,GAAWC,EAAqBC,EAAeC,mBACjDC,EAAKC,EAAkBC,EAAoBH,mBAE3CI,EAAY5B,EAAM6B,QAAO,GAsB7B,OArBAb,GAA0B,KACxBY,EAAUE,SAAU,CAAI,IAGO9B,EAAM+B,aACrC,SAACC,EAAiBC,QAAwB,IAAxBA,IAAAA,EAA2B,CAAA,GAKtCL,EAAUE,UAEG,iBAAPE,EACTX,EAAOa,SAASF,GAEhBX,EAAOa,SAASF,EAAEG,EAAA,CAAIC,YAAaX,GAAOQ,IAE9C,GACA,CAACZ,EAAQI,GAIb,CAt6BuBY,GAGvB,WAEI1B,KADFG,EAAAA,kBAAS,GAOT,IAAIwB,EAAoBtC,EAAMY,WAAWb,IACrCwC,SAAEA,EAAQC,OAAEA,EAAMC,UAAEA,GAAczC,EAAMY,WAAWR,IACnDI,QAAEA,GAAYR,EAAMY,WAAWN,IAC7BoC,SAAUC,GAAqB9B,IAEjC+B,EAAqBC,KAAKC,UAC5BC,EAAAA,2BAAoBvC,EAASgC,EAAOQ,uBAGlCpB,EAAY5B,EAAM6B,QAAO,GAqD7B,OApDAb,GAA0B,KACxBY,EAAUE,SAAU,CAAI,IAGO9B,EAAM+B,aACrC,SAACC,EAAiBC,GAKhB,QALwC,IAAxBA,IAAAA,EAA2B,CAAA,IAKtCL,EAAUE,QAAS,OAExB,GAAkB,iBAAPE,EAET,YADAS,EAAUQ,GAAGjB,GAIf,IAAIkB,EAAOC,EAAAA,UACTnB,EACAa,KAAKO,MAAMR,GACXD,EACqB,SAArBV,EAAQoB,UASe,MAArBf,GAA0C,MAAbC,IAC/BW,EAAKR,SACe,MAAlBQ,EAAKR,SACDH,EACAe,EAASA,UAAC,CAACf,EAAUW,EAAKR,aAG/BT,EAAQsB,QAAUd,EAAUc,QAAUd,EAAUe,MACjDN,EACAjB,EAAQwB,MACRxB,EAEJ,GACA,CACEM,EACAE,EACAG,EACAD,EACAL,GAKN,CA1E6CoB,EAC7C,CA2EA,MAAMC,EAAgB3D,EAAMC,cAAuB,MAiB5C,SAAS2D,EAAUC,GACxB,IAAItD,EAASP,EAAMY,WAAWN,GAAcC,OAC5C,OAAIA,EAEAP,EAAA8D,cAACH,EAAcI,SAAQ,CAACC,MAAOH,GAAUtD,GAGtCA,CACT,CAuBO,SAAS0D,EACdjC,EAAMkC,GAEA,IADNb,SAAEA,QAA8C,IAAAa,EAAG,CAAA,EAAEA,GAEjD1B,OAAEA,GAAWxC,EAAMY,WAAWR,IAC9BI,QAAEA,GAAYR,EAAMY,WAAWN,IAC7BoC,SAAUC,GAAqB9B,IACjC+B,EAAqBC,KAAKC,UAC5BC,EAAAA,2BAAoBvC,EAASgC,EAAOQ,uBAGtC,OAAOhD,EAAMmE,SACX,IACEhB,EAAAA,UACEnB,EACAa,KAAKO,MAAMR,GACXD,EACa,SAAbU,IAEJ,CAACrB,EAAIY,EAAoBD,EAAkBU,GAE/C,CAUO,SAASe,EACdC,EACAC,GAEA,OAAOC,EAAcF,EAAQC,EAC/B,CAGO,SAASC,EACdF,EACAC,EACAE,EACAhC,GAGE7B,KADFG,EAAAA,kBAAS,GAOT,IAAI2B,UAAEA,EAAWvB,OAAQuD,GAAazE,EAAMY,WAAWR,IACjDI,QAASkE,GAAkB1E,EAAMY,WAAWN,GAC9CqE,EAAaD,EAAcA,EAAcE,OAAS,GAClDC,EAAeF,EAAaA,EAAWG,OAAS,CAAA,GAC/BH,GAAaA,EAAWjC,SAC7C,IAAIqC,EAAqBJ,EAAaA,EAAWK,aAAe,IAC9CL,GAAcA,EAAWM,MAqC3C,IAEIlE,EAFAmE,EAAsBrE,IAG1B,GAAIyD,EAAa,CAAA,IAAAa,EACf,IAAIC,EACqB,iBAAhBd,EAA2Be,EAAAA,UAAUf,GAAeA,EAGpC,MAAvBS,WAA0BI,EACxBC,EAAkB1C,iBAAlByC,EAA4BG,WAAWP,KAF3CjE,EAAAA,kBAAS,GASTC,EAAWqE,CACb,MACErE,EAAWmE,EAGb,IAAIxC,EAAW3B,EAAS2B,UAAY,IAEhC6C,EAAoB7C,EACxB,GAA2B,MAAvBqC,EAA4B,CAe9B,IAAIS,EAAiBT,EAAmBxB,QAAQ,MAAO,IAAIkC,MAAM,KAEjEF,EAAoB,IADL7C,EAASa,QAAQ,MAAO,IAAIkC,MAAM,KACdC,MAAMF,EAAeZ,QAAQe,KAAK,IACvE,CAEA,IAAInF,GACDiE,GACDD,GACAA,EAAgBhE,SAChBgE,EAAgBhE,QAAQoE,OAAS,EAC5BJ,EAAgBhE,QACjBoF,EAAAA,YAAYvB,EAAQ,CAAE3B,SAAU6C,IAmBlCM,EAAkBC,EACpBtF,GACEA,EAAQuF,KAAKC,GACXC,OAAOC,OAAO,CAAE,EAAEF,EAAO,CACvBlB,OAAQmB,OAAOC,OAAO,CAAE,EAAErB,EAAcmB,EAAMlB,QAC9CpC,SAAUY,EAASA,UAAC,CAClByB,EAEAtC,EAAU0D,eACN1D,EAAU0D,eAAeH,EAAMtD,UAAUA,SACzCsD,EAAMtD,WAEZsC,aACyB,MAAvBgB,EAAMhB,aACFD,EACAzB,EAASA,UAAC,CACRyB,EAEAtC,EAAU0D,eACN1D,EAAU0D,eAAeH,EAAMhB,cAActC,SAC7CsD,EAAMhB,mBAIxBN,EACAF,EACAhC,GAMF,OAAI8B,GAAeuB,EAEf7F,EAAA8D,cAACzD,EAAgB0D,SAAQ,CACvBC,MAAO,CACLjD,SAAQoB,EAAA,CACNO,SAAU,IACV0D,OAAQ,GACRC,KAAM,GACN5C,MAAO,KACP6C,IAAK,WACFvF,GAELwF,eAAgBC,EAAcC,OAACC,MAGhCb,GAKAA,CACT,CAEA,SAASc,IACP,IAAIC,EAAQC,IACRC,EAAUC,EAAAA,qBAAqBH,GAC5BA,EAAMI,OAAUJ,IAAAA,EAAMK,WACzBL,aAAiBM,MACjBN,EAAME,QACNjE,KAAKC,UAAU8D,GACfO,EAAQP,aAAiBM,MAAQN,EAAMO,MAAQ,KAE/CC,EAAY,CAAEC,QAAS,SAAUC,gBADrB,0BAuBhB,OACEtH,EAAA8D,cAAA9D,EAAAuH,SAAA,KACEvH,EAAA8D,cAAI,KAAA,KAAA,iCACJ9D,EAAA8D,cAAA,KAAA,CAAI0D,MAAO,CAAEC,UAAW,WAAaX,GACpCK,EAAQnH,EAAA8D,cAAA,MAAA,CAAK0D,MAAOJ,GAAYD,GAAe,KAvBtC,KA2BhB,CAEA,MAAMO,EAAsB1H,EAAA8D,cAAC6C,QAgBtB,MAAMgB,UAA4B3H,EAAM4H,UAI7CC,YAAYC,GACVC,MAAMD,GACNE,KAAKvE,MAAQ,CACX1C,SAAU+G,EAAM/G,SAChBkH,aAAcH,EAAMG,aACpBrB,MAAOkB,EAAMlB,MAEjB,CAEA1F,gCAAgC0F,GAC9B,MAAO,CAAEA,MAAOA,EAClB,CAEA1F,gCACE4G,EACArE,GAUA,OACEA,EAAM1C,WAAa+G,EAAM/G,UACD,SAAvB0C,EAAMwE,cAAkD,SAAvBH,EAAMG,aAEjC,CACLrB,MAAOkB,EAAMlB,MACb7F,SAAU+G,EAAM/G,SAChBkH,aAAcH,EAAMG,cAQjB,CACLrB,WAAuBsB,IAAhBJ,EAAMlB,MAAsBkB,EAAMlB,MAAQnD,EAAMmD,MACvD7F,SAAU0C,EAAM1C,SAChBkH,aAAcH,EAAMG,cAAgBxE,EAAMwE,aAE9C,CAEAE,kBAAkBvB,EAAYwB,GAC5BC,QAAQzB,MACN,wDACAA,EACAwB,EAEJ,CAEAE,SACE,YAA4BJ,IAArBF,KAAKvE,MAAMmD,MAChB5G,EAAA8D,cAACxD,EAAayD,SAAQ,CAACC,MAAOgE,KAAKF,MAAMS,cACvCvI,EAAA8D,cAACpD,EAAkBqD,SAAQ,CACzBC,MAAOgE,KAAKvE,MAAMmD,MAClB4B,SAAUR,KAAKF,MAAMW,aAIzBT,KAAKF,MAAMU,QAEf,EASF,SAASE,EAAaC,GAAwD,IAAvDJ,aAAEA,EAAYvC,MAAEA,EAAKwC,SAAEA,GAA8BG,EACtErG,EAAoBtC,EAAMY,WAAWb,GAazC,OAREuC,GACAA,EAAkBpB,QAClBoB,EAAkBsG,gBACjB5C,EAAMf,MAAM4D,cAAgB7C,EAAMf,MAAM6D,iBAEzCxG,EAAkBsG,cAAcG,2BAA6B/C,EAAMf,MAAMxD,IAIzEzB,EAAA8D,cAACxD,EAAayD,SAAQ,CAACC,MAAOuE,GAC3BC,EAGP,CAEO,SAAS1C,EACdtF,EACAkE,EACAF,EACAhC,GAC2B,IAAAwG,EAC3B,QAJ2B,IAA3BtE,IAAAA,EAA8B,SACc,IAA5CF,IAAAA,EAA+C,WACX,IAApChC,IAAAA,EAAuC,MAExB,MAAXhC,EAAiB,CAAA,IAAAyI,EACnB,IAAKzE,EACH,OAAO,KAGT,GAAIA,EAAgB0E,OAGlB1I,EAAUgE,EAAgBhE,YACrB,MACLyI,OAAAA,EAAAzG,IAAAyG,EAAQE,qBACiB,IAAzBzE,EAAcE,SACbJ,EAAgB4E,aACjB5E,EAAgBhE,QAAQoE,OAAS,GAUjC,OAAO,KAFPpE,EAAUgE,EAAgBhE,OAG5B,CACF,CAEA,IAAIqF,EAAkBrF,EAGlB0I,EAASF,OAAHA,EAAGxE,QAAAwE,EAAAA,EAAiBE,OAC9B,GAAc,MAAVA,EAAgB,CAClB,IAAIG,EAAaxD,EAAgByD,WAC9BC,GAAMA,EAAEtE,MAAMxD,SAA+ByG,KAAnB,MAANgB,OAAM,EAANA,EAASK,EAAEtE,MAAMxD,OAGtC4H,GAAc,GADhBvI,EAAS0I,kBAAA,GAMT3D,EAAkBA,EAAgBH,MAChC,EACA+D,KAAKC,IAAI7D,EAAgBjB,OAAQyE,EAAa,GAElD,CAIA,IAAIM,GAAiB,EACjBC,GAAiB,EACrB,GAAIpF,GAAmBhC,GAAUA,EAAO2G,oBACtC,IAAK,IAAIU,EAAI,EAAGA,EAAIhE,EAAgBjB,OAAQiF,IAAK,CAC/C,IAAI7D,EAAQH,EAAgBgE,GAM5B,IAJI7D,EAAMf,MAAM6E,iBAAmB9D,EAAMf,MAAM8E,0BAC7CH,EAAgBC,GAGd7D,EAAMf,MAAMxD,GAAI,CAClB,IAAIuI,WAAEA,EAAUd,OAAEA,GAAW1E,EACzByF,EACFjE,EAAMf,MAAMiF,aACmBhC,IAA/B8B,EAAWhE,EAAMf,MAAMxD,OACrByH,QAAqChB,IAA3BgB,EAAOlD,EAAMf,MAAMxD,KACjC,GAAIuE,EAAMf,MAAMkF,MAAQF,EAAkB,CAIxCN,GAAiB,EAEf9D,EADE+D,GAAiB,EACD/D,EAAgBH,MAAM,EAAGkE,EAAgB,GAEzC,CAAC/D,EAAgB,IAErC,KACF,CACF,CACF,CAGF,OAAOA,EAAgBuE,aAAY,CAAC7J,EAAQyF,EAAOqE,KAEjD,IAAIzD,EACA0D,GAA8B,EAC9BzB,EAAuC,KACvCkB,EAAiD,KA0VzD,IAAqBzD,EAzVb9B,IACFoC,EAAQsC,GAAUlD,EAAMf,MAAMxD,GAAKyH,EAAOlD,EAAMf,MAAMxD,SAAMyG,EAC5DW,EAAe7C,EAAMf,MAAM4D,cAAgBnB,EAEvCiC,IACEC,EAAgB,GAAe,IAAVS,GAoVZ/D,EAlVT,kBACA,GAkVIiE,EAAcjE,KAC1BiE,EAAcjE,IAAO,GAhVfgE,GAA8B,EAC9BP,EAAyB,MAChBH,IAAkBS,IAC3BC,GAA8B,EAC9BP,EAAyB/D,EAAMf,MAAM8E,wBAA0B,QAKrE,IAAIvJ,EAAUkE,EAAc8F,OAAO3E,EAAgBH,MAAM,EAAG2E,EAAQ,IAChEI,EAAcA,KAChB,IAAIjC,EAkBJ,OAhBEA,EADE5B,EACSiC,EACFyB,EACEP,EACF/D,EAAMf,MAAM2C,UAOV5H,EAAA8D,cAACkC,EAAMf,MAAM2C,UAAS,MACxB5B,EAAMf,MAAMyF,QACV1E,EAAMf,MAAMyF,QAEZnK,EAGXP,EAAA8D,cAAC4E,EAAa,CACZ1C,MAAOA,EACPuC,aAAc,CACZhI,SACAC,UACAC,YAAgC,MAAnB+D,GAEfgE,SAAUA,GACV,EAMN,OAAOhE,IACJwB,EAAMf,MAAM6D,eAAiB9C,EAAMf,MAAM4D,cAA0B,IAAVwB,GAC1DrK,EAAA8D,cAAC6D,EAAmB,CAClB5G,SAAUyD,EAAgBzD,SAC1BkH,aAAczD,EAAgByD,aAC9BQ,UAAWI,EACXjC,MAAOA,EACP4B,SAAUiC,IACVlC,aAAc,CAAEhI,OAAQ,KAAMC,UAASC,aAAa,KAGtDgK,GACD,GACA,KACL,CAAC,IAEIlJ,WAAAA,GAAc,OAAdA,EAAc,WAAA,aAAdA,EAAc,eAAA,iBAAdA,EAAc,kBAAA,cAAdA,CAAc,EAAdA,GAAc,CAAA,GAMdI,WAAAA,GAAmB,OAAnBA,EAAmB,WAAA,aAAnBA,EAAmB,cAAA,gBAAnBA,EAAmB,cAAA,gBAAnBA,EAAmB,cAAA,gBAAnBA,EAAmB,cAAA,gBAAnBA,EAAmB,mBAAA,qBAAnBA,EAAmB,WAAA,aAAnBA,EAAmB,eAAA,iBAAnBA,EAAmB,kBAAA,cAAnBA,EAAmB,WAAA,aAAnBA,CAAmB,EAAnBA,GAAmB,CAAA,GAmBxB,SAASL,EAAqBqJ,GAC5B,IAAIC,EAAM5K,EAAMY,WAAWb,GAE3B,OADU6K,GAAV9J,EAAS0I,kBAAA,GACFoB,CACT,CAEA,SAASC,EAAmBF,GAC1B,IAAIlH,EAAQzD,EAAMY,WAAWV,GAE7B,OADUuD,GAAV3C,EAAS0I,kBAAA,GACF/F,CACT,CASA,SAAS/B,EAAkBiJ,GACzB,IAAI1F,EARN,SAAyB0F,GACvB,IAAI1F,EAAQjF,EAAMY,WAAWN,GAE7B,OADU2E,GAAVnE,EAAS0I,kBAAA,GACFvE,CACT,CAIc6F,GACRC,EAAY9F,EAAMzE,QAAQyE,EAAMzE,QAAQoE,OAAS,GAKrD,OAHEmG,EAAU9F,MAAMxD,IADlBX,EAAS0I,kBAAA,GAIFuB,EAAU9F,MAAMxD,EACzB,CAsFO,SAASoF,IAAyB,IAAAmE,EACvC,IAAIpE,EAAQ5G,EAAMY,WAAWF,GACzB+C,EAAQoH,EAAmBlJ,EAAoBsJ,eAC/CC,EAAUxJ,EAAkBC,EAAoBsJ,eAIpD,YAAc/C,IAAVtB,EACKA,EAIFoE,OAAPA,EAAOvH,EAAMyF,aAAN8B,EAAAA,EAAeE,EACxB,CAKO,SAASC,IACd,IAAInH,EAAQhE,EAAMY,WAAWT,GAC7B,OAAY,MAAL6D,OAAK,EAALA,EAAOoH,KAChB,CAUA,IAAIC,EAAY,EAuGhB,MAAMd,EAAyC,CAAA,EC7kCxC,SAASe,EACdC,EACAC,GAEID,MAAAA,GAAAA,EAAcE,wBASuBvD,KAA3B,MAAZqD,OAAY,EAAZA,EAAcvI,uBACZwI,GAAiBA,EAAaxI,qBAS9BwI,IACEA,EAAaE,kBAQbF,EAAaG,uBAQbH,EAAarC,oBAQbqC,EAAaI,+BAQrB,CCWA,MACMC,EAAsB7L,EAAsB,gBA8GlD,SAAS8L,EAAUC,GAQW,IARV1H,OAClBA,EAAM7B,OACNA,EAAMiB,MACNA,GAKDsI,EACC,OAAOxH,EAAcF,OAAQ6D,EAAWzE,EAAOjB,EACjD,CAwLO,SAASwJ,EAAMC,GACpBnL,EAAS0I,kBAAA,EAKX,CAqBO,SAAS0C,EAAMC,GAQqB,IAPzC5J,SAAU6J,EAAe,IAAG5D,SAC5BA,EAAW,KACXzH,SAAUsL,EAAY9F,eACtBA,EAAiBC,EAAcC,OAACC,IAAGjE,UACnCA,EACAvB,OAAQoL,GAAa,EAAK9J,OAC1BA,GACY2J,EAETxL,KADHG,EAAAA,kBAAS,GAQT,IAAIyB,EAAW6J,EAAa7I,QAAQ,OAAQ,KACxCgJ,EAAoBvM,EAAMmE,SAC5B,KAAO,CACL5B,WACAE,YACAvB,OAAQoL,EACR9J,OAAML,EAAA,CACJa,sBAAsB,GACnBR,MAGP,CAACD,EAAUC,EAAQC,EAAW6J,IAGJ,iBAAjBD,IACTA,EAAehH,EAAAA,UAAUgH,IAG3B,IAAI3J,SACFA,EAAW,IAAG0D,OACdA,EAAS,GAAEC,KACXA,EAAO,GAAE5C,MACTA,EAAQ,KAAI6C,IACZA,EAAM,WACJ+F,EAEAG,EAAkBxM,EAAMmE,SAAQ,KAClC,IAAIsI,EAAmBC,EAAAA,cAAchK,EAAUH,GAE/C,OAAwB,MAApBkK,EACK,KAGF,CACL1L,SAAU,CACR2B,SAAU+J,EACVrG,SACAC,OACA5C,QACA6C,OAEFC,iBACD,GACA,CAAChE,EAAUG,EAAU0D,EAAQC,EAAM5C,EAAO6C,EAAKC,IASlD,OAAuB,MAAnBiG,EACK,KAIPxM,EAAA8D,cAAC1D,EAAkB2D,SAAQ,CAACC,MAAOuI,GACjCvM,EAAA8D,cAACzD,EAAgB0D,SAAQ,CAACyE,SAAUA,EAAUxE,MAAOwI,IAG3D,CAwCC,IAWIG,WAAAA,GAAiB,OAAjBA,EAAAA,EAAiB,QAAA,GAAA,UAAjBA,EAAAA,EAAiB,QAAA,GAAA,UAAjBA,EAAAA,EAAiB,MAAA,GAAA,QAAjBA,CAAiB,EAAjBA,GAAiB,CAAA,GAMtB,MAAMC,EAAsB,IAAIC,SAAQ,SAExC,MAAMC,UAA2B9M,EAAM4H,UAIrCC,YAAYC,GACVC,MAAMD,GACNE,KAAKvE,MAAQ,CAAEmD,MAAO,KACxB,CAEA1F,gCAAgC0F,GAC9B,MAAO,CAAEA,QACX,CAEAuB,kBAAkBvB,EAAYwB,GAC5BC,QAAQzB,MACN,mDACAA,EACAwB,EAEJ,CAEAE,SACE,IAAIE,SAAEA,EAAQK,aAAEA,EAAYkE,QAAEA,GAAY/E,KAAKF,MAE3CkF,EAAiC,KACjChG,EAA4B2F,EAAkBM,QAElD,GAAMF,aAAmBF,QAMlB,GAAI7E,KAAKvE,MAAMmD,MAAO,CAE3BI,EAAS2F,EAAkB/F,MAC3B,IAAIsG,EAAclF,KAAKvE,MAAMmD,MAC7BoG,EAAUH,QAAQM,SAASC,OAAM,SACjCnH,OAAOoH,eAAeL,EAAS,WAAY,CAAEM,IAAKA,KAAM,IACxDrH,OAAOoH,eAAeL,EAAS,SAAU,CAAEM,IAAKA,IAAMJ,GACxD,MAAYH,EAA2BQ,UAErCP,EAAUD,EACV/F,EACE,WAAYgG,EACRL,EAAkB/F,MAClB,UAAWoG,EACXL,EAAkBa,QAClBb,EAAkBM,UAGxBjG,EAAS2F,EAAkBM,QAC3BhH,OAAOoH,eAAeN,EAAS,WAAY,CAAEO,IAAKA,KAAM,IACxDN,EAAUD,EAAQU,MACfC,GACCzH,OAAOoH,eAAeN,EAAS,QAAS,CAAEO,IAAKA,IAAMI,MACtD9G,GACCX,OAAOoH,eAAeN,EAAS,SAAU,CAAEO,IAAKA,IAAM1G,YA5B1DI,EAAS2F,EAAkBa,QAC3BR,EAAUH,QAAQE,UAClB9G,OAAOoH,eAAeL,EAAS,WAAY,CAAEM,IAAKA,KAAM,IACxDrH,OAAOoH,eAAeL,EAAS,QAAS,CAAEM,IAAKA,IAAMP,IA6BvD,GACE/F,IAAW2F,EAAkB/F,OAC7BoG,EAAQW,kBAAkBC,EAAAA,qBAG1B,MAAMhB,EAGR,GAAI5F,IAAW2F,EAAkB/F,QAAUiC,EAEzC,MAAMmE,EAAQW,OAGhB,GAAI3G,IAAW2F,EAAkB/F,MAE/B,OAAO5G,EAAA8D,cAAC3D,EAAa4D,SAAQ,CAACC,MAAOgJ,EAASxE,SAAUK,IAG1D,GAAI7B,IAAW2F,EAAkBa,QAE/B,OAAOxN,EAAA8D,cAAC3D,EAAa4D,SAAQ,CAACC,MAAOgJ,EAASxE,SAAUA,IAI1D,MAAMwE,CACR,EAOF,SAASa,EAAYC,GAIlB,IAJmBtF,SACpBA,GAGDsF,EACKJ,EAAOvC,IACP4C,EAA+B,mBAAbvF,EAA0BA,EAASkF,GAAQlF,EACjE,OAAOxI,EAAA8D,cAAA9D,EAAAuH,SAAGwG,KAAAA,EACZ,CAaO,SAASC,EACdxF,EACAyF,QAAoB,IAApBA,IAAAA,EAAuB,IAEvB,IAAI5J,EAAwB,GA6D5B,OA3DArE,EAAMkO,SAASC,QAAQ3F,GAAU,CAACkC,EAASL,KACzC,IAAKrK,EAAMoO,eAAe1D,GAGxB,OAGF,IAAI2D,EAAW,IAAIJ,EAAY5D,GAE/B,GAAIK,EAAQ4D,OAAStO,EAAMuH,SAMzB,YAJAlD,EAAOb,KAAK+K,MACVlK,EACA2J,EAAyBtD,EAAQ5C,MAAMU,SAAU6F,IAMnD3D,EAAQ4D,OAAStC,GADnBlL,EAAAA,kBAAS,GAQN4J,EAAQ5C,MAAMuC,OAAUK,EAAQ5C,MAAMU,UADzC1H,EAAS0I,kBAAA,GAKT,IAAIvE,EAAqB,CACvBxD,GAAIiJ,EAAQ5C,MAAMrG,IAAM4M,EAAS1I,KAAK,KACtC6I,cAAe9D,EAAQ5C,MAAM0G,cAC7B9D,QAASA,EAAQ5C,MAAM4C,QACvB9C,UAAW8C,EAAQ5C,MAAMF,UACzByC,MAAOK,EAAQ5C,MAAMuC,MACrBnH,KAAMwH,EAAQ5C,MAAM5E,KACpBgH,OAAQQ,EAAQ5C,MAAMoC,OACtBuE,OAAQ/D,EAAQ5C,MAAM2G,OACtB5F,aAAc6B,EAAQ5C,MAAMe,aAC5BC,cAAe4B,EAAQ5C,MAAMgB,cAC7B4F,iBACiC,MAA/BhE,EAAQ5C,MAAMgB,eACgB,MAA9B4B,EAAQ5C,MAAMe,aAChB8F,iBAAkBjE,EAAQ5C,MAAM6G,iBAChCC,OAAQlE,EAAQ5C,MAAM8G,OACtBzE,KAAMO,EAAQ5C,MAAMqC,MAGlBO,EAAQ5C,MAAMU,WAChBvD,EAAMuD,SAAWwF,EACftD,EAAQ5C,MAAMU,SACd6F,IAIJhK,EAAOb,KAAKyB,EAAM,IAGbZ,CACT,CC7eA,SAASwK,EAAmB5J,GAC1B,IAAI6J,EAAgE,CAGlEJ,iBAAyC,MAAvBzJ,EAAM6D,eAA+C,MAAtB7D,EAAM4D,cAmDzD,OAhDI5D,EAAM2C,WAUR3B,OAAOC,OAAO4I,EAAS,CACrBpE,QAAS1K,EAAM8D,cAAcmB,EAAM2C,WACnCA,eAAWM,IAIXjD,EAAM6E,iBAUR7D,OAAOC,OAAO4I,EAAS,CACrB/E,uBAAwB/J,EAAM8D,cAAcmB,EAAM6E,iBAClDA,qBAAiB5B,IAIjBjD,EAAM6D,eAUR7C,OAAOC,OAAO4I,EAAS,CACrBjG,aAAc7I,EAAM8D,cAAcmB,EAAM6D,eACxCA,mBAAeZ,IAIZ4G,CACT,6wCDwOO,SAAcC,GAAkD,IAAjDvG,SAAEA,EAAQK,aAAEA,EAAYkE,QAAEA,GAAqBgC,EACnE,OACE/O,EAAA8D,cAACgJ,EAAkB,CAACC,QAASA,EAASlE,aAAcA,GAClD7I,EAAA8D,cAAC+J,EAAcrF,KAAAA,GAGrB,iBA1TO,SAAqBwG,GAMc,IANbzM,SAC3BA,EAAQiG,SACRA,EAAQyG,eACRA,EAAcC,aACdA,EAAY1M,OACZA,GACkBwM,EACdG,EAAanP,EAAM6B,SACG,MAAtBsN,EAAWrN,UACbqN,EAAWrN,QAAUsN,sBAAoB,CACvCH,iBACAC,eACAG,UAAU,KAId,IAAIC,EAAUH,EAAWrN,SACpB2B,EAAO8L,GAAgBvP,EAAMwP,SAAS,CACzCf,OAAQa,EAAQb,OAChB1N,SAAUuO,EAAQvO,YAEhB0K,mBAAEA,GAAuBjJ,GAAU,CAAA,EACnCiN,EAAWzP,EAAM+B,aAClB2N,IACCjE,GAAsBI,EAClBA,GAAoB,IAAM0D,EAAaG,KACvCH,EAAaG,EAAS,GAE5B,CAACH,EAAc9D,IAOjB,OAJAzL,EAAMmB,iBAAgB,IAAMmO,EAAQK,OAAOF,IAAW,CAACH,EAASG,IAEhEzP,EAAM4P,WAAU,IAAMtE,EAAyB9I,IAAS,CAACA,IAGvDxC,EAAA8D,cAACoI,EAAM,CACL3J,SAAUA,EACViG,SAAUA,EACVzH,SAAU0C,EAAM1C,SAChBwF,eAAgB9C,EAAMgL,OACtBhM,UAAW6M,EACX9M,OAAQA,GAGd,aAkBO,SAAiBqN,GAKA,IALC7N,GACvBA,EAAEuB,QACFA,EAAOE,MACPA,EAAKJ,SACLA,GACcwM,EAEZlP,KADFG,EAAAA,kBAAS,GAOT,IAAI0B,OAAEA,EAAQtB,OAAQuD,GAAazE,EAAMY,WAAWR,IAShDI,QAAEA,GAAYR,EAAMY,WAAWN,IAC7BoC,SAAUC,GAAqB9B,IACjCqB,EAAWd,IAIX8B,EAAOC,EAAAA,UACTnB,EACAe,EAAmB+M,2BAACtP,EAASgC,EAAOQ,sBACpCL,EACa,SAAbU,GAEE0M,EAAWlN,KAAKC,UAAUI,GAO9B,OALAlD,EAAM4P,WACJ,IAAM1N,EAASW,KAAKO,MAAM2M,GAAW,CAAExM,UAASE,QAAOJ,cACvD,CAACnB,EAAU6N,EAAU1M,EAAUE,EAASE,IAGnC,IACT,WAWO,SAAgBqE,GACrB,OAAOlE,EAAUkE,EAAMjE,QACzB,wCAxPO,SAAuB8E,GAIc,IAJbqH,gBAC7BA,EAAe3O,OACfA,EAAMmB,OACNA,GACoBmG,GACflF,EAAO8L,GAAgBvP,EAAMwP,SAASnO,EAAOoC,QAC9CgI,mBAAEA,GAAuBjJ,GAAU,CAAA,EAEnCiN,EAAWzP,EAAM+B,aAClB2N,IACKjE,GAAsBI,EACxBA,GAAoB,IAAM0D,EAAaG,KAEvCH,EAAaG,EACf,GAEF,CAACH,EAAc9D,IAKjBzL,EAAMmB,iBAAgB,IAAME,EAAO4O,UAAUR,IAAW,CAACpO,EAAQoO,IAEjEzP,EAAM4P,WAAU,QAQb,IAEH,IAAInN,EAAYzC,EAAMmE,SAAQ,KACrB,CACL+L,WAAY7O,EAAO6O,WACnB/J,eAAgB9E,EAAO8E,eACvBlD,GAAKkN,GAAM9O,EAAOa,SAASiO,GAC3B3M,KAAMA,CAACxB,EAAIyB,EAAO2M,IAChB/O,EAAOa,SAASF,EAAI,CAClByB,QACA4M,mBAAoBD,MAAAA,OAAAA,EAAAA,EAAMC,qBAE9B9M,QAASA,CAACvB,EAAIyB,EAAO2M,IACnB/O,EAAOa,SAASF,EAAI,CAClBuB,SAAS,EACTE,QACA4M,mBAAoBD,MAAAA,OAAAA,EAAAA,EAAMC,wBAG/B,CAAChP,IAEAkB,EAAWlB,EAAOkB,UAAY,IAE9BD,EAAoBtC,EAAMmE,SAC5B,KAAO,CACL9C,SACAoB,YACAvB,QAAQ,EACRqB,cAEF,CAAClB,EAAQoB,EAAWF,IActB,OAXAvC,EAAM4P,WACJ,IAAMtE,EAAyB9I,EAAQnB,EAAOmB,SAC9C,CAACnB,EAAQmB,IAUTxC,EAAA8D,cAAA9D,EAAAuH,SACEvH,KAAAA,EAAA8D,cAAC/D,EAAkBgE,SAAQ,CAACC,MAAO1B,GACjCtC,EAAA8D,cAAC5D,EAAuB6D,SAAQ,CAACC,MAAOP,GACtCzD,EAAA8D,cAACoI,EAAM,CACL3J,SAAUA,EACVxB,SAAU0C,EAAM1C,SAChBwF,eAAgB9C,EAAM6M,cACtB7N,UAAWA,EACXD,OAAQ,CACNQ,qBAAsB3B,EAAOmB,OAAOQ,uBAGrCS,EAAM2F,aAAe/H,EAAOmB,OAAO2G,oBAClCnJ,EAAA8D,cAACgI,EAAU,CACTzH,OAAQhD,EAAOgD,OACf7B,OAAQnB,EAAOmB,OACfiB,MAAOA,IAGTuM,KAKP,KAGP,WA0TO,SAAeO,GAGqB,IAHpB/H,SACrBA,EAAQzH,SACRA,GACYwP,EACZ,OAAOnM,EAAU4J,EAAyBxF,GAAWzH,EACvD,uOFuYO,WACL,OAAOW,EAAkBC,EAAoB6O,WAC/C,gDG/lBO,SACLnM,EACA+L,GAUA,OAAOK,eAAa,CAClBlO,SAAU6N,MAAAA,OAAAA,EAAAA,EAAM7N,SAChBC,OAAML,EAAA,CAAA,EACDiO,MAAAA,OAAAA,EAAAA,EAAM5N,OAAM,CACfkO,oBAAoB,IAEtBpB,QAASF,EAAAA,oBAAoB,CAC3BH,eAAgBmB,MAAAA,OAAAA,EAAAA,EAAMnB,eACtBC,aAAckB,MAAAA,OAAAA,EAAAA,EAAMlB,eAEtByB,cAAeP,MAAAA,OAAAA,EAAAA,EAAMO,cACrBtM,SACAwK,qBACA+B,aAAcR,MAAAA,OAAAA,EAAAA,EAAMQ,aACpBC,wBAAyBT,MAAAA,OAAAA,EAAAA,EAAMS,0BAC9BC,YACL,4ED4ZO,SACLtQ,GAEA,OAAOsF,EAAetF,EACxB,kBFuOO,WACL,IAAIiD,EAAQoH,EAAmBlJ,EAAoBoP,eAC/C7F,EAAUxJ,EAAkBC,EAAoBqP,eACpD,OAAOvN,EAAMwN,WAAaxN,EAAMwN,WAAW/F,QAAWhD,CACxD,kBAiCO,WACL,IAAIlE,EAAQhE,EAAMY,WAAWT,GAC7B,OAAY,MAAL6D,OAAK,EAALA,EAAO2J,MAChB,iCAUO,SAAoBuD,GACzB,WAAM7P,EAAMkB,SAAEA,GAAajB,EAAqBC,EAAe4P,YAC3D1N,EAAQoH,EAAmBlJ,EAAoBwP,aAE9CC,EAAYC,GAAiBrR,EAAMwP,SAAS,IAC7C8B,EAAkBtR,EAAM+B,aACzBwP,IACC,GAA2B,mBAAhBL,EACT,QAASA,EAEX,GAAiB,MAAb3O,EACF,OAAO2O,EAAYK,GAMrB,IAAIC,gBAAEA,EAAeC,aAAEA,EAAYnB,cAAEA,GAAkBiB,EACvD,OAAOL,EAAY,CACjBM,gBAAerP,EAAA,CAAA,EACVqP,EAAe,CAClB9O,SACEgK,EAAaA,cAAC8E,EAAgB9O,SAAUH,IACxCiP,EAAgB9O,WAEpB+O,aAAYtP,EAAA,CAAA,EACPsP,EAAY,CACf/O,SACEgK,EAAaA,cAAC+E,EAAa/O,SAAUH,IACrCkP,EAAa/O,WAEjB4N,iBACA,GAEJ,CAAC/N,EAAU2O,IAuBb,OAlBAlR,EAAM4P,WAAU,KACd,IAAItJ,EAAMoL,SAASrG,GAEnB,OADAgG,EAAc/K,GACP,IAAMjF,EAAOsQ,cAAcrL,EAAI,GACrC,CAACjF,IAMJrB,EAAM4P,WAAU,KACK,KAAfwB,GACF/P,EAAOuQ,WAAWR,EAAYE,EAChC,GACC,CAACjQ,EAAQ+P,EAAYE,IAIjBF,GAAc3N,EAAMoO,SAASC,IAAIV,GACpC3N,EAAMoO,SAASvE,IAAI8D,GACnBW,EAAAA,YACN,YArgCO,SACL/P,EAAMgQ,GAEE,IADR3O,SAAEA,QAA8C,IAAA2O,EAAG,CAAA,EAAEA,EAGnDrR,KADFG,EAAAA,kBAAS,GAOT,IAAIyB,SAAEA,EAAQE,UAAEA,GAAczC,EAAMY,WAAWR,IAC3CiG,KAAEA,EAAI3D,SAAEA,EAAQ0D,OAAEA,GAAWnC,EAAgBjC,EAAI,CAAEqB,aAEnD4O,EAAiBvP,EAWrB,MALiB,MAAbH,IACF0P,EACe,MAAbvP,EAAmBH,EAAWe,EAASA,UAAC,CAACf,EAAUG,KAGhDD,EAAUyN,WAAW,CAAExN,SAAUuP,EAAgB7L,SAAQC,QAClE,yCAq2BO,WACL,IAAI5C,EAAQoH,EAAmBlJ,EAAoBqP,eAC/C9F,EAAUxJ,EAAkBC,EAAoBqP,eAEpD,IAAIvN,EAAMyF,QAAmC,MAAzBzF,EAAMyF,OAAOgC,GAMjC,OAAOzH,EAAMuG,WAAWkB,GALtB7C,QAAQzB,MACuDsE,2DAAAA,MAKnE,6BA/zBO,SAGLgH,GAEEvR,KADFG,EAAAA,kBAAS,GAOT,IAAI4B,SAAEA,GAAa7B,IACnB,OAAOb,EAAMmE,SACX,IAAMgO,EAAAA,UAA0BD,EAASE,EAAAA,kBAAW1P,KACpD,CAACA,EAAUwP,GAEf,eAuxBO,WACL,IAAI1R,QAAEA,EAAOwJ,WAAEA,GAAea,EAC5BlJ,EAAoB0Q,YAEtB,OAAOrS,EAAMmE,SACX,IAAM3D,EAAQuF,KAAKwD,GAAM+I,EAAAA,kCAA2B/I,EAAGS,MACvD,CAACxJ,EAASwJ,GAEd,kCAjCO,WAEL,OADYa,EAAmBlJ,EAAoB4Q,eACtCC,UACf,sBA5xBO,WACL,OAAOxS,EAAMY,WAAWP,GAAiBkG,cAC3C,mCAiJO,WACL,OAAOvG,EAAMY,WAAW+C,EAC1B,cAwBO,WAKL,IAAInD,QAAEA,GAAYR,EAAMY,WAAWN,GAC/BqE,EAAanE,EAAQA,EAAQoE,OAAS,GAC1C,OAAOD,EAAcA,EAAWG,OAAiB,EACnD,uCA6mBO,WACL,IAAIxC,EAAoBhB,EAAqBC,EAAekR,gBACxDhP,EAAQoH,EAAmBlJ,EAAoB8Q,gBACnD,OAAOzS,EAAMmE,SACX,KAAO,CACLuO,WAAYpQ,EAAkBjB,OAAOqR,WACrCjP,MAAOA,EAAMwE,gBAEf,CAAC3F,EAAkBjB,OAAOqR,WAAYjP,EAAMwE,cAEhD,yCAmCO,SAA4BiD,GAEjC,OADYL,EAAmBlJ,EAAoBgR,oBACtC3I,WAAWkB,EAC1B"}