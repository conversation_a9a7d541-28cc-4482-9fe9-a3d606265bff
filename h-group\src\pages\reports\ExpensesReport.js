const React = require('react');
const { useState, useEffect } = React;
const { Link } = require('react-router-dom');

const ExpensesReport = () => {
  const [expenses, setExpenses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  // إحصائيات المصروفات
  const [summary, setSummary] = useState({
    totalExpenses: 0,
    expensesByCategory: [],
    averageDaily: 0,
    highestExpense: 0
  });

  useEffect(() => {
    loadExpenses();
  }, [dateRange]);

  const loadExpenses = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات
      const mockExpenses = [
        {
          id: 1,
          date: '2024-05-01',
          description: 'شراء مواد خام - خشب بلوط',
          amount: 5000,
          category: 'مواد خام'
        },
        {
          id: 2,
          date: '2024-05-02',
          description: 'رواتب العمال - شهر مايو',
          amount: 8000,
          category: 'رواتب'
        },
        {
          id: 3,
          date: '2024-05-03',
          description: 'صيانة المعدات',
          amount: 1500,
          category: 'صيانة'
        },
        {
          id: 4,
          date: '2024-05-04',
          description: 'فواتير الكهرباء والماء',
          amount: 800,
          category: 'مرافق'
        }
      ];

      setExpenses(mockExpenses);

      // حساب الإحصائيات
      const total = mockExpenses.reduce((sum, exp) => sum + exp.amount, 0);
      const categories = mockExpenses.reduce((acc, exp) => {
        const existing = acc.find(cat => cat.category === exp.category);
        if (existing) {
          existing.amount += exp.amount;
          existing.count += 1;
        } else {
          acc.push({ category: exp.category, amount: exp.amount, count: 1 });
        }
        return acc;
      }, []);

      setSummary({
        totalExpenses: total,
        expensesByCategory: categories,
        averageDaily: total / 30,
        highestExpense: Math.max(...mockExpenses.map(exp => exp.amount))
      });

    } catch (error) {
      console.error('خطأ في تحميل تقرير المصروفات:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return `${amount.toLocaleString()} د.ل`;
  };

  const exportToExcel = () => {
    console.log('تصدير تقرير المصروفات إلى Excel');
  };

  const printReport = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="reports-page expenses-report-page">
        <div className="loading">جاري تحميل التقرير...</div>
      </div>
    );
  }

  return (
    <div className="reports-page expenses-report-page">
      <div className="page-header">
        <h2>تقرير المصروفات</h2>
        <div className="page-actions">
          <button className="compact-btn compact-btn-primary" onClick={exportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير Excel
          </button>

          <button className="compact-btn compact-btn-secondary" onClick={printReport}>
            <i className="fas fa-print"></i> طباعة
          </button>

          <Link to="/reports" className="compact-btn compact-btn-outline">
            <i className="fas fa-arrow-right"></i> العودة
          </Link>
        </div>
      </div>

      {/* فلاتر التقرير */}
      <div className="form-section">
        <div className="form-section-title">فلاتر التقرير</div>
        <div className="form-row">
          <div className="compact-form-group">
            <label>من تاريخ</label>
            <input
              type="date"
              className="compact-form-control"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
            />
          </div>

          <div className="compact-form-group">
            <label>إلى تاريخ</label>
            <input
              type="date"
              className="compact-form-control"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
            />
          </div>
        </div>

        <div className="action-buttons">
          <button className="action-btn compact-btn-primary" onClick={loadExpenses}>
            <i className="fas fa-sync"></i> تحديث التقرير
          </button>
        </div>
      </div>

      {/* ملخص المصروفات */}
      <div className="stats-grid">
        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-money-bill-wave"></i>
              إجمالي المصروفات
            </div>
            <div className="compact-card-badge badge-danger">
              مصروفات
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{formatCurrency(summary.totalExpenses)}</div>
          </div>
        </div>

        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-calendar-day"></i>
              متوسط يومي
            </div>
            <div className="compact-card-badge badge-warning">
              يومي
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{formatCurrency(summary.averageDaily)}</div>
          </div>
        </div>

        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-arrow-up"></i>
              أعلى مصروف
            </div>
            <div className="compact-card-badge badge-info">
              أعلى
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{formatCurrency(summary.highestExpense)}</div>
          </div>
        </div>

        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-list"></i>
              عدد الفئات
            </div>
            <div className="compact-card-badge badge-secondary">
              فئات
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{summary.expensesByCategory.length}</div>
          </div>
        </div>
      </div>

      {/* المصروفات حسب الفئة */}
      <div className="compact-table-container">
        <div className="compact-card-header">
          <div className="compact-card-title">
            <i className="fas fa-chart-pie"></i>
            المصروفات حسب الفئة
          </div>
        </div>
        <table className="compact-table">
          <thead>
            <tr>
              <th>الفئة</th>
              <th>عدد المصروفات</th>
              <th>إجمالي المبلغ</th>
              <th>النسبة</th>
            </tr>
          </thead>
          <tbody>
            {summary.expensesByCategory.map((category, index) => (
              <tr key={index}>
                <td>{category.category}</td>
                <td>{category.count}</td>
                <td>{formatCurrency(category.amount)}</td>
                <td>{((category.amount / summary.totalExpenses) * 100).toFixed(1)}%</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* تفاصيل المصروفات */}
      <div className="compact-table-container">
        <div className="compact-card-header">
          <div className="compact-card-title">
            <i className="fas fa-list-ul"></i>
            تفاصيل المصروفات
          </div>
        </div>
        <table className="compact-table">
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>الوصف</th>
              <th>الفئة</th>
              <th>المبلغ</th>
            </tr>
          </thead>
          <tbody>
            {expenses.map(expense => (
              <tr key={expense.id}>
                <td>{expense.date}</td>
                <td>{expense.description}</td>
                <td>
                  <span className="compact-status-badge secondary">
                    {expense.category}
                  </span>
                </td>
                <td>{formatCurrency(expense.amount)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

module.exports = ExpensesReport;
