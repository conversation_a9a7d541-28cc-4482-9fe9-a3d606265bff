{"version": 3, "file": "vml-shape-xform.js", "names": ["BaseXform", "require", "VmlTextboxXform", "VmlClientDataXform", "VmlShapeXform", "constructor", "map", "tag", "render", "xmlStream", "model", "index", "openNode", "V_SHAPE_ATTRIBUTES", "leafNode", "color2", "color", "obscured", "closeNode", "parseOpen", "node", "parser", "name", "reset", "margins", "insetmode", "attributes", "anchor", "editAs", "protection", "parseText", "text", "parseClose", "undefined", "inset", "id", "type", "style", "fillcolor", "strokecolor", "note", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/comment/vml-shape-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst VmlTextboxXform = require('./vml-textbox-xform');\nconst VmlClientDataXform = require('./vml-client-data-xform');\n\nclass VmlShapeXform extends BaseXform {\n  constructor() {\n    super();\n    this.map = {\n      'v:textbox': new VmlTextboxXform(),\n      'x:ClientData': new VmlClientDataXform(),\n    };\n  }\n\n  get tag() {\n    return 'v:shape';\n  }\n\n  render(xmlStream, model, index) {\n    xmlStream.openNode('v:shape', VmlShapeXform.V_SHAPE_ATTRIBUTES(model, index));\n\n    xmlStream.leafNode('v:fill', {color2: 'infoBackground [80]'});\n    xmlStream.leafNode('v:shadow', {color: 'none [81]', obscured: 't'});\n    xmlStream.leafNode('v:path', {'o:connecttype': 'none'});\n    this.map['v:textbox'].render(xmlStream, model);\n    this.map['x:ClientData'].render(xmlStream, model);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        this.model = {\n          margins: {\n            insetmode: node.attributes['o:insetmode'],\n          },\n          anchor: '',\n          editAs: '',\n          protection: {},\n        };\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model.margins.inset = this.map['v:textbox'].model && this.map['v:textbox'].model.inset;\n        this.model.protection =\n          this.map['x:ClientData'].model && this.map['x:ClientData'].model.protection;\n        this.model.anchor = this.map['x:ClientData'].model && this.map['x:ClientData'].model.anchor;\n        this.model.editAs = this.map['x:ClientData'].model && this.map['x:ClientData'].model.editAs;\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nVmlShapeXform.V_SHAPE_ATTRIBUTES = (model, index) => ({\n  id: `_x0000_s${1025 + index}`,\n  type: '#_x0000_t202',\n  style:\n    'position:absolute; margin-left:105.3pt;margin-top:10.5pt;width:97.8pt;height:59.1pt;z-index:1;visibility:hidden',\n  fillcolor: 'infoBackground [80]',\n  strokecolor: 'none [81]',\n  'o:insetmode': model.note.margins && model.note.margins.insetmode,\n});\n\nmodule.exports = VmlShapeXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,eAAe,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AACtD,MAAME,kBAAkB,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AAE7D,MAAMG,aAAa,SAASJ,SAAS,CAAC;EACpCK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,GAAG,GAAG;MACT,WAAW,EAAE,IAAIJ,eAAe,CAAC,CAAC;MAClC,cAAc,EAAE,IAAIC,kBAAkB,CAAC;IACzC,CAAC;EACH;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9BF,SAAS,CAACG,QAAQ,CAAC,SAAS,EAAER,aAAa,CAACS,kBAAkB,CAACH,KAAK,EAAEC,KAAK,CAAC,CAAC;IAE7EF,SAAS,CAACK,QAAQ,CAAC,QAAQ,EAAE;MAACC,MAAM,EAAE;IAAqB,CAAC,CAAC;IAC7DN,SAAS,CAACK,QAAQ,CAAC,UAAU,EAAE;MAACE,KAAK,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAG,CAAC,CAAC;IACnER,SAAS,CAACK,QAAQ,CAAC,QAAQ,EAAE;MAAC,eAAe,EAAE;IAAM,CAAC,CAAC;IACvD,IAAI,CAACR,GAAG,CAAC,WAAW,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IAC9C,IAAI,CAACJ,GAAG,CAAC,cAAc,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IAEjDD,SAAS,CAACS,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACf,GAAG;QACX,IAAI,CAACgB,KAAK,CAAC,CAAC;QACZ,IAAI,CAACb,KAAK,GAAG;UACXc,OAAO,EAAE;YACPC,SAAS,EAAEL,IAAI,CAACM,UAAU,CAAC,aAAa;UAC1C,CAAC;UACDC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,CAAC;QACf,CAAC;QACD;MACF;QACE,IAAI,CAACR,MAAM,GAAG,IAAI,CAACf,GAAG,CAACc,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAU,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACV,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACS,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACV,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACW,UAAU,CAACV,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGY,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQX,IAAI;MACV,KAAK,IAAI,CAACf,GAAG;QACX,IAAI,CAACG,KAAK,CAACc,OAAO,CAACU,KAAK,GAAG,IAAI,CAAC5B,GAAG,CAAC,WAAW,CAAC,CAACI,KAAK,IAAI,IAAI,CAACJ,GAAG,CAAC,WAAW,CAAC,CAACI,KAAK,CAACwB,KAAK;QAC3F,IAAI,CAACxB,KAAK,CAACmB,UAAU,GACnB,IAAI,CAACvB,GAAG,CAAC,cAAc,CAAC,CAACI,KAAK,IAAI,IAAI,CAACJ,GAAG,CAAC,cAAc,CAAC,CAACI,KAAK,CAACmB,UAAU;QAC7E,IAAI,CAACnB,KAAK,CAACiB,MAAM,GAAG,IAAI,CAACrB,GAAG,CAAC,cAAc,CAAC,CAACI,KAAK,IAAI,IAAI,CAACJ,GAAG,CAAC,cAAc,CAAC,CAACI,KAAK,CAACiB,MAAM;QAC3F,IAAI,CAACjB,KAAK,CAACkB,MAAM,GAAG,IAAI,CAACtB,GAAG,CAAC,cAAc,CAAC,CAACI,KAAK,IAAI,IAAI,CAACJ,GAAG,CAAC,cAAc,CAAC,CAACI,KAAK,CAACkB,MAAM;QAC3F,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAxB,aAAa,CAACS,kBAAkB,GAAG,CAACH,KAAK,EAAEC,KAAK,MAAM;EACpDwB,EAAE,EAAG,WAAU,IAAI,GAAGxB,KAAM,EAAC;EAC7ByB,IAAI,EAAE,cAAc;EACpBC,KAAK,EACH,iHAAiH;EACnHC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE,WAAW;EACxB,aAAa,EAAE7B,KAAK,CAAC8B,IAAI,CAAChB,OAAO,IAAId,KAAK,CAAC8B,IAAI,CAAChB,OAAO,CAACC;AAC1D,CAAC,CAAC;AAEFgB,MAAM,CAACC,OAAO,GAAGtC,aAAa"}