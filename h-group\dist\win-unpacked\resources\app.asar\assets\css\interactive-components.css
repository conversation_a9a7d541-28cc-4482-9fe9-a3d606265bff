/* مكونات تفاعلية متقدمة */

/* مفاتيح التبديل المحسنة */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: var(--transition-speed);
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: var(--transition-speed);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* أزرار الراديو المحسنة */
.radio-group {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.radio-item {
  position: relative;
  cursor: pointer;
}

.radio-item input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border: 2px solid var(--border-light-color);
  border-radius: var(--radius-lg);
  background: var(--surface-color);
  transition: all var(--transition-speed) var(--transition-ease);
  font-weight: 500;
}

.radio-item input[type="radio"]:checked + .radio-label {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.radio-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  position: relative;
  transition: all var(--transition-speed) var(--transition-ease);
}

.radio-item input[type="radio"]:checked + .radio-label .radio-indicator {
  border-color: var(--primary-color);
}

.radio-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
  transition: transform var(--transition-speed) var(--transition-ease);
}

.radio-item input[type="radio"]:checked + .radio-label .radio-indicator::after {
  transform: translate(-50%, -50%) scale(1);
}

/* مربعات الاختيار المحسنة */
.checkbox-item {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.checkbox-item input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkbox-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-speed) var(--transition-ease);
  background: var(--surface-color);
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-indicator {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.checkbox-indicator::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 6px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg) scale(0);
  transition: transform var(--transition-speed) var(--transition-ease);
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-indicator::after {
  transform: rotate(45deg) scale(1);
}

.checkbox-label {
  font-weight: 500;
  color: var(--text-color);
  transition: color var(--transition-speed);
}

.checkbox-item input[type="checkbox"]:checked ~ .checkbox-label {
  color: var(--primary-color);
}

/* شرائح التمرير المحسنة */
.slider-container {
  position: relative;
  margin: var(--spacing-lg) 0;
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  border-radius: var(--radius-xl);
  background: var(--border-light-color);
  outline: none;
  transition: background var(--transition-speed);
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow);
  transition: all var(--transition-speed) var(--transition-ease);
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: var(--shadow-lg);
}

.slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow);
  transition: all var(--transition-speed) var(--transition-ease);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-light-color);
}

/* أكورديون محسن */
.accordion {
  border: 1px solid var(--border-light-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.accordion-item {
  border-bottom: 1px solid var(--border-light-color);
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  background: var(--surface-color);
  padding: var(--spacing-lg);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all var(--transition-speed) var(--transition-ease);
  font-weight: 600;
  color: var(--text-color);
}

.accordion-header:hover {
  background: var(--border-light-color);
}

.accordion-header.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
  color: var(--light-color);
}

.accordion-icon {
  transition: transform var(--transition-speed) var(--transition-ease);
}

.accordion-header.active .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-speed) var(--transition-ease);
  background: var(--surface-color);
}

.accordion-content.active {
  max-height: 500px;
}

.accordion-body {
  padding: var(--spacing-lg);
  color: var(--text-color);
  line-height: 1.6;
}

/* تبويبات محسنة */
.tabs-container {
  margin-bottom: var(--spacing-lg);
}

.tabs-nav {
  display: flex;
  border-bottom: 2px solid var(--border-light-color);
  margin-bottom: var(--spacing-lg);
  overflow-x: auto;
}

.tab-button {
  background: none;
  border: none;
  padding: var(--spacing-lg) var(--spacing-xl);
  cursor: pointer;
  font-weight: 600;
  color: var(--text-light-color);
  transition: all var(--transition-speed) var(--transition-ease);
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  position: relative;
}

.tab-button:hover {
  color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.1);
}

.tab-content {
  display: none;
  animation: fadeInUp 0.3s ease-out;
}

.tab-content.active {
  display: block;
}

/* نوافذ منبثقة محسنة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed) var(--transition-ease);
  backdrop-filter: blur(5px);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: transform var(--transition-speed) var(--transition-ease);
}

.modal-overlay.active .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-light-color);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: 50%;
  transition: all var(--transition-speed) var(--transition-ease);
}

.modal-close:hover {
  background: var(--border-light-color);
  color: var(--text-color);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}
