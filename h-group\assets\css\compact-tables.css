/* جداول وقوائم مدمجة */

/* الجداول المدمجة */
.compact-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 0.75rem;
}

.compact-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.75rem;
  background: transparent;
}

.compact-table thead {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
}

.compact-table th {
  padding: 0.5rem 0.4rem;
  text-align: right;
  font-weight: 600;
  font-size: 0.7rem;
  border: none;
  letter-spacing: 0.3px;
}

.compact-table td {
  padding: 0.4rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  transition: background-color 0.15s ease;
  font-size: 0.7rem;
}

.compact-table tbody tr {
  transition: all 0.15s ease;
}

.compact-table tbody tr:hover {
  background-color: rgba(30, 60, 114, 0.03);
}

.compact-table tbody tr:last-child td {
  border-bottom: none;
}

/* قوائم مدمجة */
.compact-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 0.75rem;
}

.compact-list-item {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.15s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.compact-list-item:hover {
  background: rgba(30, 60, 114, 0.03);
}

.compact-list-item:last-child {
  border-bottom: none;
}

.compact-list-content {
  flex: 1;
}

.compact-list-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.compact-list-subtitle {
  font-size: 0.65rem;
  color: #6c757d;
}

.compact-list-actions {
  display: flex;
  gap: 0.3rem;
}

/* شارات الحالة المدمجة */
.compact-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.15rem 0.4rem;
  border-radius: 4px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.compact-status-badge.success {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
  border: 1px solid rgba(39, 174, 96, 0.2);
}

.compact-status-badge.warning {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
  border: 1px solid rgba(243, 156, 18, 0.2);
}

.compact-status-badge.danger {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.compact-status-badge.info {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.compact-status-badge.secondary {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

/* نماذج مدمجة */
.compact-form-group {
  margin-bottom: 0.75rem;
}

.compact-form-group label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.75rem;
}

.compact-form-control {
  width: 100%;
  padding: 0.4rem 0.6rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.75rem;
  transition: all 0.2s ease;
  background: white;
}

.compact-form-control:focus {
  outline: none;
  border-color: #1e3c72;
  box-shadow: 0 0 0 2px rgba(30, 60, 114, 0.1);
}

.compact-form-control:hover {
  border-color: #2a5298;
}

/* أزرار مدمجة */
.compact-btn-group {
  display: flex;
  gap: 0.3rem;
  margin-top: 0.5rem;
}

.compact-btn-sm {
  padding: 0.2rem 0.4rem;
  font-size: 0.65rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.2rem;
  font-weight: 500;
}

.compact-btn-sm i {
  font-size: 0.6rem;
}

.compact-btn-sm.primary {
  background: #1e3c72;
  color: white;
}

.compact-btn-sm.primary:hover {
  background: #2a5298;
  color: white;
  text-decoration: none;
}

.compact-btn-sm.success {
  background: #27ae60;
  color: white;
}

.compact-btn-sm.success:hover {
  background: #229954;
  color: white;
  text-decoration: none;
}

.compact-btn-sm.warning {
  background: #f39c12;
  color: white;
}

.compact-btn-sm.warning:hover {
  background: #e67e22;
  color: white;
  text-decoration: none;
}

.compact-btn-sm.danger {
  background: #e74c3c;
  color: white;
}

.compact-btn-sm.danger:hover {
  background: #c0392b;
  color: white;
  text-decoration: none;
}

.compact-btn-sm.outline {
  background: transparent;
  border: 1px solid #1e3c72;
  color: #1e3c72;
}

.compact-btn-sm.outline:hover {
  background: #1e3c72;
  color: white;
  text-decoration: none;
}

/* بحث مدمج */
.compact-search-box {
  position: relative;
  margin-bottom: 0.75rem;
}

.compact-search-input {
  width: 100%;
  padding: 0.4rem 0.6rem 0.4rem 2rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.75rem;
  background: white;
  transition: all 0.2s ease;
}

.compact-search-input:focus {
  outline: none;
  border-color: #1e3c72;
  box-shadow: 0 0 0 2px rgba(30, 60, 114, 0.1);
}

.compact-search-icon {
  position: absolute;
  left: 0.6rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.7rem;
}

/* فلاتر مدمجة */
.compact-filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.compact-filter-btn {
  padding: 0.3rem 0.6rem;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.compact-filter-btn:hover,
.compact-filter-btn.active {
  background: #1e3c72;
  color: white;
  border-color: #1e3c72;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .compact-table {
    font-size: 0.65rem;
  }
  
  .compact-table th,
  .compact-table td {
    padding: 0.3rem 0.25rem;
    font-size: 0.65rem;
  }
  
  .compact-list-item {
    padding: 0.4rem 0.6rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
  
  .compact-list-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .compact-btn-group {
    flex-wrap: wrap;
  }
  
  .compact-filters {
    gap: 0.3rem;
  }
  
  .compact-filter-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .compact-table th,
  .compact-table td {
    padding: 0.25rem 0.2rem;
    font-size: 0.6rem;
  }
  
  .compact-list-title {
    font-size: 0.75rem;
  }
  
  .compact-list-subtitle {
    font-size: 0.6rem;
  }
  
  .compact-btn-sm {
    padding: 0.15rem 0.3rem;
    font-size: 0.6rem;
  }
  
  .compact-form-control,
  .compact-search-input {
    padding: 0.3rem 0.5rem;
    font-size: 0.7rem;
  }
}
