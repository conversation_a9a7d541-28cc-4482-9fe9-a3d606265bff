const React = require('react');
const { useState, useEffect } = React;
const { Link, useParams, useNavigate } = require('react-router-dom');
const { formatCurrency, formatDate } = require('../../utils/formatters');

const FinancialManagement = () => {
  const { section } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(section || 'invoices');
  const [financialData, setFinancialData] = useState({
    invoices: [],
    expenses: [],
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    pendingInvoices: 0,
    paidInvoices: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadFinancialData();
  }, []);

  // تحديث التبويب النشط عند تغيير المسار
  useEffect(() => {
    if (section && ['invoices', 'expenses', 'treasury'].includes(section)) {
      setActiveTab(section);
    }
  }, [section]);

  const loadFinancialData = async () => {
    try {
      setLoading(true);

      const [invoices, expenses] = await Promise.all([
        window.electronAPI.invoices.getAll(),
        window.electronAPI.expenses.getAll()
      ]);

      // حساب الإحصائيات المالية
      const totalRevenue = invoices.reduce((sum, inv) => sum + (inv.paid_amount || 0), 0);
      const totalExpenses = expenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);
      const netProfit = totalRevenue - totalExpenses;

      // الفواتير المعلقة والمدفوعة
      const pendingInvoices = invoices.filter(inv => inv.status === 'غير مدفوعة').length;
      const paidInvoices = invoices.filter(inv => inv.status === 'مدفوعة').length;

      setFinancialData({
        invoices,
        expenses,
        totalRevenue,
        totalExpenses,
        netProfit,
        pendingInvoices,
        paidInvoices
      });

    } catch (error) {
      console.error('خطأ في تحميل البيانات المالية:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'invoices', label: 'الفواتير والمدفوعات', icon: 'fas fa-file-invoice-dollar' },
    { id: 'expenses', label: 'المصنع والتكاليف', icon: 'fas fa-industry' },
    { id: 'reports', label: 'التقارير المالية', icon: 'fas fa-chart-line' }
  ];

  // دالة تغيير التبويب مع تحديث المسار
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    navigate(`/financial/${tabId}`);
  };

  const getInvoiceStatusBadge = (status) => {
    const statusMap = {
      'غير مدفوعة': { class: 'danger', text: 'غير مدفوعة' },
      'مدفوعة جزئياً': { class: 'warning', text: 'مدفوعة جزئياً' },
      'مدفوعة': { class: 'success', text: 'مدفوعة' }
    };
    const statusInfo = statusMap[status] || { class: 'secondary', text: status };
    return React.createElement('span', {
      className: `status-badge ${statusInfo.class}`
    }, statusInfo.text);
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل البيانات المالية...')
    );
  }

  return React.createElement('div', { className: 'financial-management professional-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header modern-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-chart-line' }),
          ' الإدارة المالية'
        ),
        React.createElement('p', { className: 'page-subtitle' },
          'إدارة شاملة للفواتير والمصروفات والتقارير المالية'
        )
      )
    ),

    // إحصائيات سريعة
    React.createElement('div', { className: 'stats-grid' },
      React.createElement('div', { className: 'stat-card success' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-arrow-up' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي الإيرادات'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(financialData.totalRevenue)),
          React.createElement('div', { className: 'stat-detail' }, `${financialData.paidInvoices} فاتورة مدفوعة`)
        )
      ),
      React.createElement('div', { className: 'stat-card danger' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-arrow-down' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي المصروفات'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(financialData.totalExpenses)),
          React.createElement('div', { className: 'stat-detail' }, `${financialData.expenses.length} مصروف`)
        )
      ),
      React.createElement('div', { className: 'stat-card info' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-balance-scale' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'صافي الربح'),
          React.createElement('div', {
            className: `stat-value ${financialData.netProfit >= 0 ? 'text-success' : 'text-danger'}`
          }, formatCurrency(financialData.netProfit)),
          React.createElement('div', { className: 'stat-detail' }, 'الإيرادات - المصروفات')
        )
      ),
      React.createElement('div', { className: 'stat-card warning' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-clock' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'فواتير معلقة'),
          React.createElement('div', { className: 'stat-value' }, financialData.pendingInvoices),
          React.createElement('div', { className: 'stat-detail' }, 'تحتاج متابعة')
        )
      )
    ),

    // التبويبات
    React.createElement('div', { className: 'modern-card glass-card' },
      React.createElement('div', { className: 'card-header modern-card-header' },
        React.createElement('div', { className: 'tabs-container' },
          tabs.map(tab =>
            React.createElement('button', {
              key: tab.id,
              className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,
              onClick: () => handleTabChange(tab.id)
            },
              React.createElement('i', { className: tab.icon }),
              React.createElement('span', null, tab.label)
            )
          )
        )
      ),

      React.createElement('div', { className: 'card-body' },
        // تبويب الفواتير
        activeTab === 'invoices' && React.createElement('div', { className: 'tab-content' },
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'الفواتير والمدفوعات'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/invoices/new',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' فاتورة جديدة'
              )
            )
          ),
          React.createElement('div', { className: 'table-responsive' },
            React.createElement('table', { className: 'modern-table professional-table' },
              React.createElement('thead', null,
                React.createElement('tr', null,
                  React.createElement('th', null, 'رقم الفاتورة'),
                  React.createElement('th', null, 'العميل'),
                  React.createElement('th', null, 'المبلغ الإجمالي'),
                  React.createElement('th', null, 'المبلغ المدفوع'),
                  React.createElement('th', null, 'المتبقي'),
                  React.createElement('th', null, 'تاريخ الإصدار'),
                  React.createElement('th', null, 'الحالة'),
                  React.createElement('th', null, 'الإجراءات')
                )
              ),
              React.createElement('tbody', null,
                financialData.invoices.slice(0, 10).map(invoice => {
                  const remaining = (invoice.total_amount || 0) - (invoice.paid_amount || 0);

                  return React.createElement('tr', { key: invoice.id },
                    React.createElement('td', null, invoice.invoice_number),
                    React.createElement('td', null, invoice.customer_name || '-'),
                    React.createElement('td', null, formatCurrency(invoice.total_amount || 0)),
                    React.createElement('td', null, formatCurrency(invoice.paid_amount || 0)),
                    React.createElement('td', null,
                      React.createElement('span', {
                        className: remaining > 0 ? 'text-danger' : 'text-success'
                      }, formatCurrency(remaining))
                    ),
                    React.createElement('td', null, formatDate(invoice.issue_date)),
                    React.createElement('td', null, getInvoiceStatusBadge(invoice.status)),
                    React.createElement('td', null,
                      React.createElement('div', { className: 'action-buttons' },
                        React.createElement(Link, {
                          to: `/invoices/${invoice.id}`,
                          className: 'action-btn info-btn',
                          title: 'عرض التفاصيل'
                        },
                          React.createElement('i', { className: 'fas fa-eye' })
                        ),
                        React.createElement('button', {
                          className: 'action-btn success-btn',
                          title: 'طباعة'
                        },
                          React.createElement('i', { className: 'fas fa-print' })
                        )
                      )
                    )
                  );
                })
              )
            )
          ),
          financialData.invoices.length > 10 && React.createElement('div', { className: 'text-center mt-3' },
            React.createElement(Link, {
              to: '/invoices',
              className: 'btn btn-outline-primary'
            }, 'عرض جميع الفواتير')
          )
        ),

        // تبويب المصروفات
        activeTab === 'expenses' && React.createElement('div', { className: 'tab-content' },
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'المصنع والتكاليف'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/expenses/new',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' مصروف جديد'
              )
            )
          ),
          React.createElement('div', { className: 'table-responsive' },
            React.createElement('table', { className: 'modern-table professional-table' },
              React.createElement('thead', null,
                React.createElement('tr', null,
                  React.createElement('th', null, 'الوصف'),
                  React.createElement('th', null, 'الفئة'),
                  React.createElement('th', null, 'المبلغ'),
                  React.createElement('th', null, 'التاريخ'),
                  React.createElement('th', null, 'متكرر'),
                  React.createElement('th', null, 'الإجراءات')
                )
              ),
              React.createElement('tbody', null,
                financialData.expenses.slice(0, 10).map(expense =>
                  React.createElement('tr', { key: expense.id },
                    React.createElement('td', null, expense.description || '-'),
                    React.createElement('td', null, expense.category || '-'),
                    React.createElement('td', null, formatCurrency(expense.amount || 0)),
                    React.createElement('td', null, formatDate(expense.expense_date)),
                    React.createElement('td', null,
                      React.createElement('span', {
                        className: `status-badge ${expense.recurring ? 'info' : 'secondary'}`
                      }, expense.recurring ? 'نعم' : 'لا')
                    ),
                    React.createElement('td', null,
                      React.createElement('div', { className: 'action-buttons' },
                        React.createElement(Link, {
                          to: `/expenses/edit/${expense.id}`,
                          className: 'action-btn primary-btn',
                          title: 'تعديل'
                        },
                          React.createElement('i', { className: 'fas fa-edit' })
                        )
                      )
                    )
                  )
                )
              )
            )
          ),
          financialData.expenses.length > 10 && React.createElement('div', { className: 'text-center mt-3' },
            React.createElement(Link, {
              to: '/expenses',
              className: 'btn btn-outline-primary'
            }, 'عرض جميع المصروفات')
          )
        ),

        // تبويب التقارير المالية
        activeTab === 'reports' && React.createElement('div', { className: 'tab-content' },
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'التقارير المالية'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/reports',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-chart-bar' }),
                ' جميع التقارير'
              )
            )
          ),

          // ملخص مالي سريع
          React.createElement('div', { className: 'financial-summary' },
            React.createElement('div', { className: 'row' },
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('div', { className: 'summary-card revenue' },
                  React.createElement('h4', null, 'تحليل الإيرادات'),
                  React.createElement('div', { className: 'summary-item' },
                    React.createElement('span', { className: 'label' }, 'إجمالي الفواتير:'),
                    React.createElement('span', { className: 'value' }, financialData.invoices.length)
                  ),
                  React.createElement('div', { className: 'summary-item' },
                    React.createElement('span', { className: 'label' }, 'الفواتير المدفوعة:'),
                    React.createElement('span', { className: 'value text-success' }, financialData.paidInvoices)
                  ),
                  React.createElement('div', { className: 'summary-item' },
                    React.createElement('span', { className: 'label' }, 'الفواتير المعلقة:'),
                    React.createElement('span', { className: 'value text-danger' }, financialData.pendingInvoices)
                  ),
                  React.createElement('div', { className: 'summary-total' },
                    React.createElement('span', { className: 'label' }, 'إجمالي الإيرادات:'),
                    React.createElement('span', { className: 'value' }, formatCurrency(financialData.totalRevenue))
                  )
                )
              ),
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('div', { className: 'summary-card expenses' },
                  React.createElement('h4', null, 'تحليل المصروفات'),
                  React.createElement('div', { className: 'summary-item' },
                    React.createElement('span', { className: 'label' }, 'إجمالي المصروفات:'),
                    React.createElement('span', { className: 'value' }, financialData.expenses.length)
                  ),
                  React.createElement('div', { className: 'summary-item' },
                    React.createElement('span', { className: 'label' }, 'المصروفات المتكررة:'),
                    React.createElement('span', { className: 'value text-warning' },
                      financialData.expenses.filter(e => e.recurring).length
                    )
                  ),
                  React.createElement('div', { className: 'summary-item' },
                    React.createElement('span', { className: 'label' }, 'متوسط المصروف:'),
                    React.createElement('span', { className: 'value' },
                      formatCurrency(financialData.expenses.length > 0 ?
                        financialData.totalExpenses / financialData.expenses.length : 0
                      )
                    )
                  ),
                  React.createElement('div', { className: 'summary-total' },
                    React.createElement('span', { className: 'label' }, 'إجمالي المصروفات:'),
                    React.createElement('span', { className: 'value' }, formatCurrency(financialData.totalExpenses))
                  )
                )
              )
            ),

            // صافي الربح
            React.createElement('div', { className: 'profit-summary' },
              React.createElement('div', { className: 'profit-card' },
                React.createElement('h3', null, 'صافي الربح'),
                React.createElement('div', {
                  className: `profit-amount ${financialData.netProfit >= 0 ? 'positive' : 'negative'}`
                }, formatCurrency(financialData.netProfit)),
                React.createElement('div', { className: 'profit-details' },
                  React.createElement('span', null,
                    `${formatCurrency(financialData.totalRevenue)} إيرادات - ${formatCurrency(financialData.totalExpenses)} مصروفات`
                  )
                )
              )
            )
          ),

          // روابط سريعة للتقارير
          React.createElement('div', { className: 'quick-reports' },
            React.createElement('h4', null, 'تقارير سريعة'),
            React.createElement('div', { className: 'reports-grid' },
              React.createElement(Link, {
                to: '/reports/financial',
                className: 'report-link'
              },
                React.createElement('i', { className: 'fas fa-chart-pie' }),
                React.createElement('span', null, 'التقرير المالي الشامل')
              ),
              React.createElement(Link, {
                to: '/reports/invoices',
                className: 'report-link'
              },
                React.createElement('i', { className: 'fas fa-file-invoice' }),
                React.createElement('span', null, 'تقرير الفواتير')
              ),
              React.createElement(Link, {
                to: '/reports/expenses',
                className: 'report-link'
              },
                React.createElement('i', { className: 'fas fa-receipt' }),
                React.createElement('span', null, 'تقرير المصروفات')
              ),
              React.createElement(Link, {
                to: '/reports/profit-loss',
                className: 'report-link'
              },
                React.createElement('i', { className: 'fas fa-balance-scale' }),
                React.createElement('span', null, 'تقرير الأرباح والخسائر')
              )
            )
          )
        )
      )
    )
  );
};

module.exports = FinancialManagement;
