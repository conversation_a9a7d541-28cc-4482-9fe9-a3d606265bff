{"version": 3, "file": "worksheet-xform.js", "names": ["_", "require", "co<PERSON><PERSON><PERSON>", "XmlStream", "RelType", "<PERSON><PERSON>", "BaseXform", "ListXform", "RowXform", "ColXform", "DimensionXform", "HyperlinkXform", "MergeCellXform", "DataValidationsXform", "SheetPropertiesXform", "SheetFormatPropertiesXform", "SheetViewXform", "SheetProtectionXform", "PageMarginsXform", "PageSetupXform", "PrintOptionsXform", "AutoFilterXform", "PictureXform", "DrawingXform", "TablePartXform", "RowBreaksXform", "HeaderFooterXform", "ConditionalFormattingsXform", "ExtListXform", "mergeRule", "rule", "extRule", "Object", "keys", "for<PERSON>ach", "key", "value", "extValue", "undefined", "mergeConditionalFormattings", "model", "extModel", "length", "cfMap", "ruleMap", "cf", "ref", "rules", "x14Id", "extCf", "push", "WorkSheetXform", "constructor", "options", "maxRows", "maxCols", "ignoreNodes", "map", "sheetPr", "dimension", "sheetViews", "tag", "count", "childXform", "sheetFormatPr", "cols", "sheetData", "empty", "maxItems", "autoFilter", "mergeCells", "rowBreaks", "hyperlinks", "<PERSON><PERSON><PERSON><PERSON>", "dataValidations", "pageSetup", "headerFooter", "printOptions", "picture", "drawing", "sheetProtection", "tableParts", "conditionalFormatting", "extLst", "prepare", "merges", "comments", "formulae", "siFormulae", "rows", "conditionalFormattings", "rels", "nextRid", "r", "hyperlink", "rId", "Id", "Type", "Hyperlink", "Target", "target", "TargetMode", "comment", "Comments", "id", "vmlDrawing", "VmlDrawing", "item", "refAddress", "decode<PERSON>ddress", "commentRefs", "commentName", "drawingRelsHash", "bookImage", "media", "medium", "type", "imageId", "Image", "name", "extension", "background", "image", "drawingsCount", "anchors", "drawings", "rIdImage", "preImageId", "anchor", "range", "rIdHyperLink", "tooltip", "tables", "table", "Table", "columns", "column", "style", "dxfId", "styles", "addDxfStyle", "render", "xmlStream", "openXml", "StdDocAttributes", "openNode", "WORKSHEET_ATTRIBUTES", "sheetFormatPropertiesModel", "properties", "defaultRowHeight", "dyDescent", "outlineLevelCol", "outlineLevelRow", "defaultColWidth", "sheetPropertiesModel", "outlineProperties", "tabColor", "fitToPage", "pageMarginsModel", "margins", "printOptionsModel", "showRowColHeaders", "showGridLines", "horizontalCentered", "verticalCentered", "sheetProtectionModel", "dimensions", "views", "rel", "leafNode", "closeNode", "parseOpen", "node", "parser", "each", "xform", "reset", "includes", "parseText", "text", "parseClose", "sheetProperties", "assign", "reconcile", "relationships", "reduce", "h", "vmlComment", "vmlDrawings", "index", "note", "commentsMap", "hyperlinkMap", "address", "filter", "Boolean", "row", "cells", "drawingRel", "match", "drawing<PERSON>ame", "backgroundRel", "split", "mediaIndex", "tablePart", "xmlns", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/worksheet-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\n\nconst colCache = require('../../../utils/col-cache');\nconst XmlStream = require('../../../utils/xml-stream');\n\nconst RelType = require('../../rel-type');\n\nconst Merges = require('./merges');\n\nconst BaseXform = require('../base-xform');\nconst ListXform = require('../list-xform');\nconst RowXform = require('./row-xform');\nconst ColXform = require('./col-xform');\nconst DimensionXform = require('./dimension-xform');\nconst HyperlinkXform = require('./hyperlink-xform');\nconst MergeCellXform = require('./merge-cell-xform');\nconst DataValidationsXform = require('./data-validations-xform');\nconst SheetPropertiesXform = require('./sheet-properties-xform');\nconst SheetFormatPropertiesXform = require('./sheet-format-properties-xform');\nconst SheetViewXform = require('./sheet-view-xform');\nconst SheetProtectionXform = require('./sheet-protection-xform');\nconst PageMarginsXform = require('./page-margins-xform');\nconst PageSetupXform = require('./page-setup-xform');\nconst PrintOptionsXform = require('./print-options-xform');\nconst AutoFilterXform = require('./auto-filter-xform');\nconst PictureXform = require('./picture-xform');\nconst DrawingXform = require('./drawing-xform');\nconst TablePartXform = require('./table-part-xform');\nconst RowBreaksXform = require('./row-breaks-xform');\nconst HeaderFooterXform = require('./header-footer-xform');\nconst ConditionalFormattingsXform = require('./cf/conditional-formattings-xform');\nconst ExtListXform = require('./ext-lst-xform');\n\nconst mergeRule = (rule, extRule) => {\n  Object.keys(extRule).forEach(key => {\n    const value = rule[key];\n    const extValue = extRule[key];\n    if (value === undefined && extValue !== undefined) {\n      rule[key] = extValue;\n    }\n  });\n};\n\nconst mergeConditionalFormattings = (model, extModel) => {\n  // conditional formattings are rendered in worksheet.conditionalFormatting and also in\n  // worksheet.extLst.ext.x14:conditionalFormattings\n  // some (e.g. dataBar) are even spread across both!\n  if (!extModel || !extModel.length) {\n    return model;\n  }\n  if (!model || !model.length) {\n    return extModel;\n  }\n\n  // index model rules by x14Id\n  const cfMap = {};\n  const ruleMap = {};\n  model.forEach(cf => {\n    cfMap[cf.ref] = cf;\n    cf.rules.forEach(rule => {\n      const {x14Id} = rule;\n      if (x14Id) {\n        ruleMap[x14Id] = rule;\n      }\n    });\n  });\n\n  extModel.forEach(extCf => {\n    extCf.rules.forEach(extRule => {\n      const rule = ruleMap[extRule.x14Id];\n      if (rule) {\n        // merge with matching rule\n        mergeRule(rule, extRule);\n      } else if (cfMap[extCf.ref]) {\n        // reuse existing cf ref\n        cfMap[extCf.ref].rules.push(extRule);\n      } else {\n        // create new cf\n        model.push({\n          ref: extCf.ref,\n          rules: [extRule],\n        });\n      }\n    });\n  });\n\n  // need to cope with rules in extModel that don't exist in model\n  return model;\n};\n\nclass WorkSheetXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    const {maxRows, maxCols, ignoreNodes} = options || {};\n\n    this.ignoreNodes = ignoreNodes || [];\n\n    this.map = {\n      sheetPr: new SheetPropertiesXform(),\n      dimension: new DimensionXform(),\n      sheetViews: new ListXform({\n        tag: 'sheetViews',\n        count: false,\n        childXform: new SheetViewXform(),\n      }),\n      sheetFormatPr: new SheetFormatPropertiesXform(),\n      cols: new ListXform({tag: 'cols', count: false, childXform: new ColXform()}),\n      sheetData: new ListXform({\n        tag: 'sheetData',\n        count: false,\n        empty: true,\n        childXform: new RowXform({maxItems: maxCols}),\n        maxItems: maxRows,\n      }),\n      autoFilter: new AutoFilterXform(),\n      mergeCells: new ListXform({tag: 'mergeCells', count: true, childXform: new MergeCellXform()}),\n      rowBreaks: new RowBreaksXform(),\n      hyperlinks: new ListXform({\n        tag: 'hyperlinks',\n        count: false,\n        childXform: new HyperlinkXform(),\n      }),\n      pageMargins: new PageMarginsXform(),\n      dataValidations: new DataValidationsXform(),\n      pageSetup: new PageSetupXform(),\n      headerFooter: new HeaderFooterXform(),\n      printOptions: new PrintOptionsXform(),\n      picture: new PictureXform(),\n      drawing: new DrawingXform(),\n      sheetProtection: new SheetProtectionXform(),\n      tableParts: new ListXform({tag: 'tableParts', count: true, childXform: new TablePartXform()}),\n      conditionalFormatting: new ConditionalFormattingsXform(),\n      extLst: new ExtListXform(),\n    };\n  }\n\n  prepare(model, options) {\n    options.merges = new Merges();\n    model.hyperlinks = options.hyperlinks = [];\n    model.comments = options.comments = [];\n\n    options.formulae = {};\n    options.siFormulae = 0;\n    this.map.cols.prepare(model.cols, options);\n    this.map.sheetData.prepare(model.rows, options);\n    this.map.conditionalFormatting.prepare(model.conditionalFormattings, options);\n\n    model.mergeCells = options.merges.mergeCells;\n\n    // prepare relationships\n    const rels = (model.rels = []);\n\n    function nextRid(r) {\n      return `rId${r.length + 1}`;\n    }\n\n    model.hyperlinks.forEach(hyperlink => {\n      const rId = nextRid(rels);\n      hyperlink.rId = rId;\n      rels.push({\n        Id: rId,\n        Type: RelType.Hyperlink,\n        Target: hyperlink.target,\n        TargetMode: 'External',\n      });\n    });\n\n    // prepare comment relationships\n    if (model.comments.length > 0) {\n      const comment = {\n        Id: nextRid(rels),\n        Type: RelType.Comments,\n        Target: `../comments${model.id}.xml`,\n      };\n      rels.push(comment);\n      const vmlDrawing = {\n        Id: nextRid(rels),\n        Type: RelType.VmlDrawing,\n        Target: `../drawings/vmlDrawing${model.id}.vml`,\n      };\n      rels.push(vmlDrawing);\n\n      model.comments.forEach(item => {\n        item.refAddress = colCache.decodeAddress(item.ref);\n      });\n\n      options.commentRefs.push({\n        commentName: `comments${model.id}`,\n        vmlDrawing: `vmlDrawing${model.id}`,\n      });\n    }\n\n    const drawingRelsHash = [];\n    let bookImage;\n    model.media.forEach(medium => {\n      if (medium.type === 'background') {\n        const rId = nextRid(rels);\n        bookImage = options.media[medium.imageId];\n        rels.push({\n          Id: rId,\n          Type: RelType.Image,\n          Target: `../media/${bookImage.name}.${bookImage.extension}`,\n        });\n        model.background = {\n          rId,\n        };\n        model.image = options.media[medium.imageId];\n      } else if (medium.type === 'image') {\n        let {drawing} = model;\n        bookImage = options.media[medium.imageId];\n        if (!drawing) {\n          drawing = model.drawing = {\n            rId: nextRid(rels),\n            name: `drawing${++options.drawingsCount}`,\n            anchors: [],\n            rels: [],\n          };\n          options.drawings.push(drawing);\n          rels.push({\n            Id: drawing.rId,\n            Type: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing',\n            Target: `../drawings/${drawing.name}.xml`,\n          });\n        }\n        let rIdImage =\n          this.preImageId === medium.imageId ? drawingRelsHash[medium.imageId] : drawingRelsHash[drawing.rels.length];\n        if (!rIdImage) {\n          rIdImage = nextRid(drawing.rels);\n          drawingRelsHash[drawing.rels.length] = rIdImage;\n          drawing.rels.push({\n            Id: rIdImage,\n            Type: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/image',\n            Target: `../media/${bookImage.name}.${bookImage.extension}`,\n          });\n        }\n\n        const anchor = {\n          picture: {\n            rId: rIdImage,\n          },\n          range: medium.range,\n        };\n        if (medium.hyperlinks && medium.hyperlinks.hyperlink) {\n          const rIdHyperLink = nextRid(drawing.rels);\n          drawingRelsHash[drawing.rels.length] = rIdHyperLink;\n          anchor.picture.hyperlinks = {\n            tooltip: medium.hyperlinks.tooltip,\n            rId: rIdHyperLink,\n          };\n          drawing.rels.push({\n            Id: rIdHyperLink,\n            Type: RelType.Hyperlink,\n            Target: medium.hyperlinks.hyperlink,\n            TargetMode: 'External',\n          });\n        }\n        this.preImageId = medium.imageId;\n        drawing.anchors.push(anchor);\n      }\n    });\n\n    // prepare tables\n    model.tables.forEach(table => {\n      // relationships\n      const rId = nextRid(rels);\n      table.rId = rId;\n      rels.push({\n        Id: rId,\n        Type: RelType.Table,\n        Target: `../tables/${table.target}`,\n      });\n\n      // dynamic styles\n      table.columns.forEach(column => {\n        const {style} = column;\n        if (style) {\n          column.dxfId = options.styles.addDxfStyle(style);\n        }\n      });\n    });\n\n    // prepare ext items\n    this.map.extLst.prepare(model, options);\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n    xmlStream.openNode('worksheet', WorkSheetXform.WORKSHEET_ATTRIBUTES);\n\n    const sheetFormatPropertiesModel = model.properties\n      ? {\n          defaultRowHeight: model.properties.defaultRowHeight,\n          dyDescent: model.properties.dyDescent,\n          outlineLevelCol: model.properties.outlineLevelCol,\n          outlineLevelRow: model.properties.outlineLevelRow,\n        }\n      : undefined;\n    if (model.properties && model.properties.defaultColWidth) {\n      sheetFormatPropertiesModel.defaultColWidth = model.properties.defaultColWidth;\n    }\n    const sheetPropertiesModel = {\n      outlineProperties: model.properties && model.properties.outlineProperties,\n      tabColor: model.properties && model.properties.tabColor,\n      pageSetup:\n        model.pageSetup && model.pageSetup.fitToPage\n          ? {\n              fitToPage: model.pageSetup.fitToPage,\n            }\n          : undefined,\n    };\n    const pageMarginsModel = model.pageSetup && model.pageSetup.margins;\n    const printOptionsModel = {\n      showRowColHeaders: model.pageSetup && model.pageSetup.showRowColHeaders,\n      showGridLines: model.pageSetup && model.pageSetup.showGridLines,\n      horizontalCentered: model.pageSetup && model.pageSetup.horizontalCentered,\n      verticalCentered: model.pageSetup && model.pageSetup.verticalCentered,\n    };\n    const sheetProtectionModel = model.sheetProtection;\n\n    this.map.sheetPr.render(xmlStream, sheetPropertiesModel);\n    this.map.dimension.render(xmlStream, model.dimensions);\n    this.map.sheetViews.render(xmlStream, model.views);\n    this.map.sheetFormatPr.render(xmlStream, sheetFormatPropertiesModel);\n    this.map.cols.render(xmlStream, model.cols);\n    this.map.sheetData.render(xmlStream, model.rows);\n    this.map.sheetProtection.render(xmlStream, sheetProtectionModel); // Note: must be after sheetData and before autoFilter\n    this.map.autoFilter.render(xmlStream, model.autoFilter);\n    this.map.mergeCells.render(xmlStream, model.mergeCells);\n    this.map.conditionalFormatting.render(xmlStream, model.conditionalFormattings); // Note: must be before dataValidations\n    this.map.dataValidations.render(xmlStream, model.dataValidations);\n\n    // For some reason hyperlinks have to be after the data validations\n    this.map.hyperlinks.render(xmlStream, model.hyperlinks);\n\n    this.map.printOptions.render(xmlStream, printOptionsModel); // Note: must be before pageMargins\n    this.map.pageMargins.render(xmlStream, pageMarginsModel);\n    this.map.pageSetup.render(xmlStream, model.pageSetup);\n    this.map.headerFooter.render(xmlStream, model.headerFooter);\n    this.map.rowBreaks.render(xmlStream, model.rowBreaks);\n    this.map.drawing.render(xmlStream, model.drawing); // Note: must be after rowBreaks\n    this.map.picture.render(xmlStream, model.background); // Note: must be after drawing\n    this.map.tableParts.render(xmlStream, model.tables);\n\n    this.map.extLst.render(xmlStream, model);\n\n    if (model.rels) {\n      // add a <legacyDrawing /> node for each comment\n      model.rels.forEach(rel => {\n        if (rel.Type === RelType.VmlDrawing) {\n          xmlStream.leafNode('legacyDrawing', {'r:id': rel.Id});\n        }\n      });\n    }\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    if (node.name === 'worksheet') {\n      _.each(this.map, xform => {\n        xform.reset();\n      });\n      return true;\n    }\n\n    if (this.map[node.name] && !this.ignoreNodes.includes(node.name)) {\n      this.parser = this.map[node.name];\n      this.parser.parseOpen(node);\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case 'worksheet': {\n        const properties = this.map.sheetFormatPr.model || {};\n        if (this.map.sheetPr.model && this.map.sheetPr.model.tabColor) {\n          properties.tabColor = this.map.sheetPr.model.tabColor;\n        }\n        if (this.map.sheetPr.model && this.map.sheetPr.model.outlineProperties) {\n          properties.outlineProperties = this.map.sheetPr.model.outlineProperties;\n        }\n        const sheetProperties = {\n          fitToPage:\n            (this.map.sheetPr.model &&\n              this.map.sheetPr.model.pageSetup &&\n              this.map.sheetPr.model.pageSetup.fitToPage) ||\n            false,\n          margins: this.map.pageMargins.model,\n        };\n        const pageSetup = Object.assign(sheetProperties, this.map.pageSetup.model, this.map.printOptions.model);\n        const conditionalFormattings = mergeConditionalFormattings(\n          this.map.conditionalFormatting.model,\n          this.map.extLst.model && this.map.extLst.model['x14:conditionalFormattings']\n        );\n        this.model = {\n          dimensions: this.map.dimension.model,\n          cols: this.map.cols.model,\n          rows: this.map.sheetData.model,\n          mergeCells: this.map.mergeCells.model,\n          hyperlinks: this.map.hyperlinks.model,\n          dataValidations: this.map.dataValidations.model,\n          properties,\n          views: this.map.sheetViews.model,\n          pageSetup,\n          headerFooter: this.map.headerFooter.model,\n          background: this.map.picture.model,\n          drawing: this.map.drawing.model,\n          tables: this.map.tableParts.model,\n          conditionalFormattings,\n        };\n\n        if (this.map.autoFilter.model) {\n          this.model.autoFilter = this.map.autoFilter.model;\n        }\n        if (this.map.sheetProtection.model) {\n          this.model.sheetProtection = this.map.sheetProtection.model;\n        }\n\n        return false;\n      }\n\n      default:\n        // not quite sure how we get here!\n        return true;\n    }\n  }\n\n  reconcile(model, options) {\n    // options.merges = new Merges();\n    // options.merges.reconcile(model.mergeCells, model.rows);\n    const rels = (model.relationships || []).reduce((h, rel) => {\n      h[rel.Id] = rel;\n      if (rel.Type === RelType.Comments) {\n        model.comments = options.comments[rel.Target].comments;\n      }\n      if (rel.Type === RelType.VmlDrawing && model.comments && model.comments.length) {\n        const vmlComment = options.vmlDrawings[rel.Target].comments;\n        model.comments.forEach((comment, index) => {\n          comment.note = Object.assign({}, comment.note, vmlComment[index]);\n        });\n      }\n      return h;\n    }, {});\n    options.commentsMap = (model.comments || []).reduce((h, comment) => {\n      if (comment.ref) {\n        h[comment.ref] = comment;\n      }\n      return h;\n    }, {});\n    options.hyperlinkMap = (model.hyperlinks || []).reduce((h, hyperlink) => {\n      if (hyperlink.rId) {\n        h[hyperlink.address] = rels[hyperlink.rId].Target;\n      }\n      return h;\n    }, {});\n    options.formulae = {};\n\n    // compact the rows and cells\n    model.rows = (model.rows && model.rows.filter(Boolean)) || [];\n    model.rows.forEach(row => {\n      row.cells = (row.cells && row.cells.filter(Boolean)) || [];\n    });\n\n    this.map.cols.reconcile(model.cols, options);\n    this.map.sheetData.reconcile(model.rows, options);\n    this.map.conditionalFormatting.reconcile(model.conditionalFormattings, options);\n\n    model.media = [];\n    if (model.drawing) {\n      const drawingRel = rels[model.drawing.rId];\n      const match = drawingRel.Target.match(/\\/drawings\\/([a-zA-Z0-9]+)[.][a-zA-Z]{3,4}$/);\n      if (match) {\n        const drawingName = match[1];\n        const drawing = options.drawings[drawingName];\n        drawing.anchors.forEach(anchor => {\n          if (anchor.medium) {\n            const image = {\n              type: 'image',\n              imageId: anchor.medium.index,\n              range: anchor.range,\n              hyperlinks: anchor.picture.hyperlinks,\n            };\n            model.media.push(image);\n          }\n        });\n      }\n    }\n\n    const backgroundRel = model.background && rels[model.background.rId];\n    if (backgroundRel) {\n      const target = backgroundRel.Target.split('/media/')[1];\n      const imageId = options.mediaIndex && options.mediaIndex[target];\n      if (imageId !== undefined) {\n        model.media.push({\n          type: 'background',\n          imageId,\n        });\n      }\n    }\n\n    model.tables = (model.tables || []).map(tablePart => {\n      const rel = rels[tablePart.rId];\n      return options.tables[rel.Target];\n    });\n\n    delete model.relationships;\n    delete model.hyperlinks;\n    delete model.comments;\n  }\n}\n\nWorkSheetXform.WORKSHEET_ATTRIBUTES = {\n  xmlns: 'http://schemas.openxmlformats.org/spreadsheetml/2006/main',\n  'xmlns:r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',\n  'xmlns:mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',\n  'mc:Ignorable': 'x14ac',\n  'xmlns:x14ac': 'http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac',\n};\n\nmodule.exports = WorkSheetXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAE9C,MAAMC,QAAQ,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAME,SAAS,GAAGF,OAAO,CAAC,2BAA2B,CAAC;AAEtD,MAAMG,OAAO,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAEzC,MAAMI,MAAM,GAAGJ,OAAO,CAAC,UAAU,CAAC;AAElC,MAAMK,SAAS,GAAGL,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMM,SAAS,GAAGN,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMO,QAAQ,GAAGP,OAAO,CAAC,aAAa,CAAC;AACvC,MAAMQ,QAAQ,GAAGR,OAAO,CAAC,aAAa,CAAC;AACvC,MAAMS,cAAc,GAAGT,OAAO,CAAC,mBAAmB,CAAC;AACnD,MAAMU,cAAc,GAAGV,OAAO,CAAC,mBAAmB,CAAC;AACnD,MAAMW,cAAc,GAAGX,OAAO,CAAC,oBAAoB,CAAC;AACpD,MAAMY,oBAAoB,GAAGZ,OAAO,CAAC,0BAA0B,CAAC;AAChE,MAAMa,oBAAoB,GAAGb,OAAO,CAAC,0BAA0B,CAAC;AAChE,MAAMc,0BAA0B,GAAGd,OAAO,CAAC,iCAAiC,CAAC;AAC7E,MAAMe,cAAc,GAAGf,OAAO,CAAC,oBAAoB,CAAC;AACpD,MAAMgB,oBAAoB,GAAGhB,OAAO,CAAC,0BAA0B,CAAC;AAChE,MAAMiB,gBAAgB,GAAGjB,OAAO,CAAC,sBAAsB,CAAC;AACxD,MAAMkB,cAAc,GAAGlB,OAAO,CAAC,oBAAoB,CAAC;AACpD,MAAMmB,iBAAiB,GAAGnB,OAAO,CAAC,uBAAuB,CAAC;AAC1D,MAAMoB,eAAe,GAAGpB,OAAO,CAAC,qBAAqB,CAAC;AACtD,MAAMqB,YAAY,GAAGrB,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAMsB,YAAY,GAAGtB,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAMuB,cAAc,GAAGvB,OAAO,CAAC,oBAAoB,CAAC;AACpD,MAAMwB,cAAc,GAAGxB,OAAO,CAAC,oBAAoB,CAAC;AACpD,MAAMyB,iBAAiB,GAAGzB,OAAO,CAAC,uBAAuB,CAAC;AAC1D,MAAM0B,2BAA2B,GAAG1B,OAAO,CAAC,oCAAoC,CAAC;AACjF,MAAM2B,YAAY,GAAG3B,OAAO,CAAC,iBAAiB,CAAC;AAE/C,MAAM4B,SAAS,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAK;EACnCC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;IAClC,MAAMC,KAAK,GAAGN,IAAI,CAACK,GAAG,CAAC;IACvB,MAAME,QAAQ,GAAGN,OAAO,CAACI,GAAG,CAAC;IAC7B,IAAIC,KAAK,KAAKE,SAAS,IAAID,QAAQ,KAAKC,SAAS,EAAE;MACjDR,IAAI,CAACK,GAAG,CAAC,GAAGE,QAAQ;IACtB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,MAAME,2BAA2B,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACvD;EACA;EACA;EACA,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACC,MAAM,EAAE;IACjC,OAAOF,KAAK;EACd;EACA,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACE,MAAM,EAAE;IAC3B,OAAOD,QAAQ;EACjB;;EAEA;EACA,MAAME,KAAK,GAAG,CAAC,CAAC;EAChB,MAAMC,OAAO,GAAG,CAAC,CAAC;EAClBJ,KAAK,CAACN,OAAO,CAACW,EAAE,IAAI;IAClBF,KAAK,CAACE,EAAE,CAACC,GAAG,CAAC,GAAGD,EAAE;IAClBA,EAAE,CAACE,KAAK,CAACb,OAAO,CAACJ,IAAI,IAAI;MACvB,MAAM;QAACkB;MAAK,CAAC,GAAGlB,IAAI;MACpB,IAAIkB,KAAK,EAAE;QACTJ,OAAO,CAACI,KAAK,CAAC,GAAGlB,IAAI;MACvB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFW,QAAQ,CAACP,OAAO,CAACe,KAAK,IAAI;IACxBA,KAAK,CAACF,KAAK,CAACb,OAAO,CAACH,OAAO,IAAI;MAC7B,MAAMD,IAAI,GAAGc,OAAO,CAACb,OAAO,CAACiB,KAAK,CAAC;MACnC,IAAIlB,IAAI,EAAE;QACR;QACAD,SAAS,CAACC,IAAI,EAAEC,OAAO,CAAC;MAC1B,CAAC,MAAM,IAAIY,KAAK,CAACM,KAAK,CAACH,GAAG,CAAC,EAAE;QAC3B;QACAH,KAAK,CAACM,KAAK,CAACH,GAAG,CAAC,CAACC,KAAK,CAACG,IAAI,CAACnB,OAAO,CAAC;MACtC,CAAC,MAAM;QACL;QACAS,KAAK,CAACU,IAAI,CAAC;UACTJ,GAAG,EAAEG,KAAK,CAACH,GAAG;UACdC,KAAK,EAAE,CAAChB,OAAO;QACjB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,OAAOS,KAAK;AACd,CAAC;AAED,MAAMW,cAAc,SAAS7C,SAAS,CAAC;EACrC8C,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,MAAM;MAACC,OAAO;MAAEC,OAAO;MAAEC;IAAW,CAAC,GAAGH,OAAO,IAAI,CAAC,CAAC;IAErD,IAAI,CAACG,WAAW,GAAGA,WAAW,IAAI,EAAE;IAEpC,IAAI,CAACC,GAAG,GAAG;MACTC,OAAO,EAAE,IAAI5C,oBAAoB,CAAC,CAAC;MACnC6C,SAAS,EAAE,IAAIjD,cAAc,CAAC,CAAC;MAC/BkD,UAAU,EAAE,IAAIrD,SAAS,CAAC;QACxBsD,GAAG,EAAE,YAAY;QACjBC,KAAK,EAAE,KAAK;QACZC,UAAU,EAAE,IAAI/C,cAAc,CAAC;MACjC,CAAC,CAAC;MACFgD,aAAa,EAAE,IAAIjD,0BAA0B,CAAC,CAAC;MAC/CkD,IAAI,EAAE,IAAI1D,SAAS,CAAC;QAACsD,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,IAAItD,QAAQ,CAAC;MAAC,CAAC,CAAC;MAC5EyD,SAAS,EAAE,IAAI3D,SAAS,CAAC;QACvBsD,GAAG,EAAE,WAAW;QAChBC,KAAK,EAAE,KAAK;QACZK,KAAK,EAAE,IAAI;QACXJ,UAAU,EAAE,IAAIvD,QAAQ,CAAC;UAAC4D,QAAQ,EAAEb;QAAO,CAAC,CAAC;QAC7Ca,QAAQ,EAAEd;MACZ,CAAC,CAAC;MACFe,UAAU,EAAE,IAAIhD,eAAe,CAAC,CAAC;MACjCiD,UAAU,EAAE,IAAI/D,SAAS,CAAC;QAACsD,GAAG,EAAE,YAAY;QAAEC,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAInD,cAAc,CAAC;MAAC,CAAC,CAAC;MAC7F2D,SAAS,EAAE,IAAI9C,cAAc,CAAC,CAAC;MAC/B+C,UAAU,EAAE,IAAIjE,SAAS,CAAC;QACxBsD,GAAG,EAAE,YAAY;QACjBC,KAAK,EAAE,KAAK;QACZC,UAAU,EAAE,IAAIpD,cAAc,CAAC;MACjC,CAAC,CAAC;MACF8D,WAAW,EAAE,IAAIvD,gBAAgB,CAAC,CAAC;MACnCwD,eAAe,EAAE,IAAI7D,oBAAoB,CAAC,CAAC;MAC3C8D,SAAS,EAAE,IAAIxD,cAAc,CAAC,CAAC;MAC/ByD,YAAY,EAAE,IAAIlD,iBAAiB,CAAC,CAAC;MACrCmD,YAAY,EAAE,IAAIzD,iBAAiB,CAAC,CAAC;MACrC0D,OAAO,EAAE,IAAIxD,YAAY,CAAC,CAAC;MAC3ByD,OAAO,EAAE,IAAIxD,YAAY,CAAC,CAAC;MAC3ByD,eAAe,EAAE,IAAI/D,oBAAoB,CAAC,CAAC;MAC3CgE,UAAU,EAAE,IAAI1E,SAAS,CAAC;QAACsD,GAAG,EAAE,YAAY;QAAEC,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAIvC,cAAc,CAAC;MAAC,CAAC,CAAC;MAC7F0D,qBAAqB,EAAE,IAAIvD,2BAA2B,CAAC,CAAC;MACxDwD,MAAM,EAAE,IAAIvD,YAAY,CAAC;IAC3B,CAAC;EACH;EAEAwD,OAAOA,CAAC5C,KAAK,EAAEa,OAAO,EAAE;IACtBA,OAAO,CAACgC,MAAM,GAAG,IAAIhF,MAAM,CAAC,CAAC;IAC7BmC,KAAK,CAACgC,UAAU,GAAGnB,OAAO,CAACmB,UAAU,GAAG,EAAE;IAC1ChC,KAAK,CAAC8C,QAAQ,GAAGjC,OAAO,CAACiC,QAAQ,GAAG,EAAE;IAEtCjC,OAAO,CAACkC,QAAQ,GAAG,CAAC,CAAC;IACrBlC,OAAO,CAACmC,UAAU,GAAG,CAAC;IACtB,IAAI,CAAC/B,GAAG,CAACQ,IAAI,CAACmB,OAAO,CAAC5C,KAAK,CAACyB,IAAI,EAAEZ,OAAO,CAAC;IAC1C,IAAI,CAACI,GAAG,CAACS,SAAS,CAACkB,OAAO,CAAC5C,KAAK,CAACiD,IAAI,EAAEpC,OAAO,CAAC;IAC/C,IAAI,CAACI,GAAG,CAACyB,qBAAqB,CAACE,OAAO,CAAC5C,KAAK,CAACkD,sBAAsB,EAAErC,OAAO,CAAC;IAE7Eb,KAAK,CAAC8B,UAAU,GAAGjB,OAAO,CAACgC,MAAM,CAACf,UAAU;;IAE5C;IACA,MAAMqB,IAAI,GAAInD,KAAK,CAACmD,IAAI,GAAG,EAAG;IAE9B,SAASC,OAAOA,CAACC,CAAC,EAAE;MAClB,OAAQ,MAAKA,CAAC,CAACnD,MAAM,GAAG,CAAE,EAAC;IAC7B;IAEAF,KAAK,CAACgC,UAAU,CAACtC,OAAO,CAAC4D,SAAS,IAAI;MACpC,MAAMC,GAAG,GAAGH,OAAO,CAACD,IAAI,CAAC;MACzBG,SAAS,CAACC,GAAG,GAAGA,GAAG;MACnBJ,IAAI,CAACzC,IAAI,CAAC;QACR8C,EAAE,EAAED,GAAG;QACPE,IAAI,EAAE7F,OAAO,CAAC8F,SAAS;QACvBC,MAAM,EAAEL,SAAS,CAACM,MAAM;QACxBC,UAAU,EAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI7D,KAAK,CAAC8C,QAAQ,CAAC5C,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAM4D,OAAO,GAAG;QACdN,EAAE,EAAEJ,OAAO,CAACD,IAAI,CAAC;QACjBM,IAAI,EAAE7F,OAAO,CAACmG,QAAQ;QACtBJ,MAAM,EAAG,cAAa3D,KAAK,CAACgE,EAAG;MACjC,CAAC;MACDb,IAAI,CAACzC,IAAI,CAACoD,OAAO,CAAC;MAClB,MAAMG,UAAU,GAAG;QACjBT,EAAE,EAAEJ,OAAO,CAACD,IAAI,CAAC;QACjBM,IAAI,EAAE7F,OAAO,CAACsG,UAAU;QACxBP,MAAM,EAAG,yBAAwB3D,KAAK,CAACgE,EAAG;MAC5C,CAAC;MACDb,IAAI,CAACzC,IAAI,CAACuD,UAAU,CAAC;MAErBjE,KAAK,CAAC8C,QAAQ,CAACpD,OAAO,CAACyE,IAAI,IAAI;QAC7BA,IAAI,CAACC,UAAU,GAAG1G,QAAQ,CAAC2G,aAAa,CAACF,IAAI,CAAC7D,GAAG,CAAC;MACpD,CAAC,CAAC;MAEFO,OAAO,CAACyD,WAAW,CAAC5D,IAAI,CAAC;QACvB6D,WAAW,EAAG,WAAUvE,KAAK,CAACgE,EAAG,EAAC;QAClCC,UAAU,EAAG,aAAYjE,KAAK,CAACgE,EAAG;MACpC,CAAC,CAAC;IACJ;IAEA,MAAMQ,eAAe,GAAG,EAAE;IAC1B,IAAIC,SAAS;IACbzE,KAAK,CAAC0E,KAAK,CAAChF,OAAO,CAACiF,MAAM,IAAI;MAC5B,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,EAAE;QAChC,MAAMrB,GAAG,GAAGH,OAAO,CAACD,IAAI,CAAC;QACzBsB,SAAS,GAAG5D,OAAO,CAAC6D,KAAK,CAACC,MAAM,CAACE,OAAO,CAAC;QACzC1B,IAAI,CAACzC,IAAI,CAAC;UACR8C,EAAE,EAAED,GAAG;UACPE,IAAI,EAAE7F,OAAO,CAACkH,KAAK;UACnBnB,MAAM,EAAG,YAAWc,SAAS,CAACM,IAAK,IAAGN,SAAS,CAACO,SAAU;QAC5D,CAAC,CAAC;QACFhF,KAAK,CAACiF,UAAU,GAAG;UACjB1B;QACF,CAAC;QACDvD,KAAK,CAACkF,KAAK,GAAGrE,OAAO,CAAC6D,KAAK,CAACC,MAAM,CAACE,OAAO,CAAC;MAC7C,CAAC,MAAM,IAAIF,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;QAClC,IAAI;UAACrC;QAAO,CAAC,GAAGvC,KAAK;QACrByE,SAAS,GAAG5D,OAAO,CAAC6D,KAAK,CAACC,MAAM,CAACE,OAAO,CAAC;QACzC,IAAI,CAACtC,OAAO,EAAE;UACZA,OAAO,GAAGvC,KAAK,CAACuC,OAAO,GAAG;YACxBgB,GAAG,EAAEH,OAAO,CAACD,IAAI,CAAC;YAClB4B,IAAI,EAAG,UAAS,EAAElE,OAAO,CAACsE,aAAc,EAAC;YACzCC,OAAO,EAAE,EAAE;YACXjC,IAAI,EAAE;UACR,CAAC;UACDtC,OAAO,CAACwE,QAAQ,CAAC3E,IAAI,CAAC6B,OAAO,CAAC;UAC9BY,IAAI,CAACzC,IAAI,CAAC;YACR8C,EAAE,EAAEjB,OAAO,CAACgB,GAAG;YACfE,IAAI,EAAE,6EAA6E;YACnFE,MAAM,EAAG,eAAcpB,OAAO,CAACwC,IAAK;UACtC,CAAC,CAAC;QACJ;QACA,IAAIO,QAAQ,GACV,IAAI,CAACC,UAAU,KAAKZ,MAAM,CAACE,OAAO,GAAGL,eAAe,CAACG,MAAM,CAACE,OAAO,CAAC,GAAGL,eAAe,CAACjC,OAAO,CAACY,IAAI,CAACjD,MAAM,CAAC;QAC7G,IAAI,CAACoF,QAAQ,EAAE;UACbA,QAAQ,GAAGlC,OAAO,CAACb,OAAO,CAACY,IAAI,CAAC;UAChCqB,eAAe,CAACjC,OAAO,CAACY,IAAI,CAACjD,MAAM,CAAC,GAAGoF,QAAQ;UAC/C/C,OAAO,CAACY,IAAI,CAACzC,IAAI,CAAC;YAChB8C,EAAE,EAAE8B,QAAQ;YACZ7B,IAAI,EAAE,2EAA2E;YACjFE,MAAM,EAAG,YAAWc,SAAS,CAACM,IAAK,IAAGN,SAAS,CAACO,SAAU;UAC5D,CAAC,CAAC;QACJ;QAEA,MAAMQ,MAAM,GAAG;UACblD,OAAO,EAAE;YACPiB,GAAG,EAAE+B;UACP,CAAC;UACDG,KAAK,EAAEd,MAAM,CAACc;QAChB,CAAC;QACD,IAAId,MAAM,CAAC3C,UAAU,IAAI2C,MAAM,CAAC3C,UAAU,CAACsB,SAAS,EAAE;UACpD,MAAMoC,YAAY,GAAGtC,OAAO,CAACb,OAAO,CAACY,IAAI,CAAC;UAC1CqB,eAAe,CAACjC,OAAO,CAACY,IAAI,CAACjD,MAAM,CAAC,GAAGwF,YAAY;UACnDF,MAAM,CAAClD,OAAO,CAACN,UAAU,GAAG;YAC1B2D,OAAO,EAAEhB,MAAM,CAAC3C,UAAU,CAAC2D,OAAO;YAClCpC,GAAG,EAAEmC;UACP,CAAC;UACDnD,OAAO,CAACY,IAAI,CAACzC,IAAI,CAAC;YAChB8C,EAAE,EAAEkC,YAAY;YAChBjC,IAAI,EAAE7F,OAAO,CAAC8F,SAAS;YACvBC,MAAM,EAAEgB,MAAM,CAAC3C,UAAU,CAACsB,SAAS;YACnCO,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;QACA,IAAI,CAAC0B,UAAU,GAAGZ,MAAM,CAACE,OAAO;QAChCtC,OAAO,CAAC6C,OAAO,CAAC1E,IAAI,CAAC8E,MAAM,CAAC;MAC9B;IACF,CAAC,CAAC;;IAEF;IACAxF,KAAK,CAAC4F,MAAM,CAAClG,OAAO,CAACmG,KAAK,IAAI;MAC5B;MACA,MAAMtC,GAAG,GAAGH,OAAO,CAACD,IAAI,CAAC;MACzB0C,KAAK,CAACtC,GAAG,GAAGA,GAAG;MACfJ,IAAI,CAACzC,IAAI,CAAC;QACR8C,EAAE,EAAED,GAAG;QACPE,IAAI,EAAE7F,OAAO,CAACkI,KAAK;QACnBnC,MAAM,EAAG,aAAYkC,KAAK,CAACjC,MAAO;MACpC,CAAC,CAAC;;MAEF;MACAiC,KAAK,CAACE,OAAO,CAACrG,OAAO,CAACsG,MAAM,IAAI;QAC9B,MAAM;UAACC;QAAK,CAAC,GAAGD,MAAM;QACtB,IAAIC,KAAK,EAAE;UACTD,MAAM,CAACE,KAAK,GAAGrF,OAAO,CAACsF,MAAM,CAACC,WAAW,CAACH,KAAK,CAAC;QAClD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI,CAAChF,GAAG,CAAC0B,MAAM,CAACC,OAAO,CAAC5C,KAAK,EAAEa,OAAO,CAAC;EACzC;EAEAwF,MAAMA,CAACC,SAAS,EAAEtG,KAAK,EAAE;IACvBsG,SAAS,CAACC,OAAO,CAAC5I,SAAS,CAAC6I,gBAAgB,CAAC;IAC7CF,SAAS,CAACG,QAAQ,CAAC,WAAW,EAAE9F,cAAc,CAAC+F,oBAAoB,CAAC;IAEpE,MAAMC,0BAA0B,GAAG3G,KAAK,CAAC4G,UAAU,GAC/C;MACEC,gBAAgB,EAAE7G,KAAK,CAAC4G,UAAU,CAACC,gBAAgB;MACnDC,SAAS,EAAE9G,KAAK,CAAC4G,UAAU,CAACE,SAAS;MACrCC,eAAe,EAAE/G,KAAK,CAAC4G,UAAU,CAACG,eAAe;MACjDC,eAAe,EAAEhH,KAAK,CAAC4G,UAAU,CAACI;IACpC,CAAC,GACDlH,SAAS;IACb,IAAIE,KAAK,CAAC4G,UAAU,IAAI5G,KAAK,CAAC4G,UAAU,CAACK,eAAe,EAAE;MACxDN,0BAA0B,CAACM,eAAe,GAAGjH,KAAK,CAAC4G,UAAU,CAACK,eAAe;IAC/E;IACA,MAAMC,oBAAoB,GAAG;MAC3BC,iBAAiB,EAAEnH,KAAK,CAAC4G,UAAU,IAAI5G,KAAK,CAAC4G,UAAU,CAACO,iBAAiB;MACzEC,QAAQ,EAAEpH,KAAK,CAAC4G,UAAU,IAAI5G,KAAK,CAAC4G,UAAU,CAACQ,QAAQ;MACvDjF,SAAS,EACPnC,KAAK,CAACmC,SAAS,IAAInC,KAAK,CAACmC,SAAS,CAACkF,SAAS,GACxC;QACEA,SAAS,EAAErH,KAAK,CAACmC,SAAS,CAACkF;MAC7B,CAAC,GACDvH;IACR,CAAC;IACD,MAAMwH,gBAAgB,GAAGtH,KAAK,CAACmC,SAAS,IAAInC,KAAK,CAACmC,SAAS,CAACoF,OAAO;IACnE,MAAMC,iBAAiB,GAAG;MACxBC,iBAAiB,EAAEzH,KAAK,CAACmC,SAAS,IAAInC,KAAK,CAACmC,SAAS,CAACsF,iBAAiB;MACvEC,aAAa,EAAE1H,KAAK,CAACmC,SAAS,IAAInC,KAAK,CAACmC,SAAS,CAACuF,aAAa;MAC/DC,kBAAkB,EAAE3H,KAAK,CAACmC,SAAS,IAAInC,KAAK,CAACmC,SAAS,CAACwF,kBAAkB;MACzEC,gBAAgB,EAAE5H,KAAK,CAACmC,SAAS,IAAInC,KAAK,CAACmC,SAAS,CAACyF;IACvD,CAAC;IACD,MAAMC,oBAAoB,GAAG7H,KAAK,CAACwC,eAAe;IAElD,IAAI,CAACvB,GAAG,CAACC,OAAO,CAACmF,MAAM,CAACC,SAAS,EAAEY,oBAAoB,CAAC;IACxD,IAAI,CAACjG,GAAG,CAACE,SAAS,CAACkF,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAAC8H,UAAU,CAAC;IACtD,IAAI,CAAC7G,GAAG,CAACG,UAAU,CAACiF,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAAC+H,KAAK,CAAC;IAClD,IAAI,CAAC9G,GAAG,CAACO,aAAa,CAAC6E,MAAM,CAACC,SAAS,EAAEK,0BAA0B,CAAC;IACpE,IAAI,CAAC1F,GAAG,CAACQ,IAAI,CAAC4E,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACyB,IAAI,CAAC;IAC3C,IAAI,CAACR,GAAG,CAACS,SAAS,CAAC2E,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACiD,IAAI,CAAC;IAChD,IAAI,CAAChC,GAAG,CAACuB,eAAe,CAAC6D,MAAM,CAACC,SAAS,EAAEuB,oBAAoB,CAAC,CAAC,CAAC;IAClE,IAAI,CAAC5G,GAAG,CAACY,UAAU,CAACwE,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAAC6B,UAAU,CAAC;IACvD,IAAI,CAACZ,GAAG,CAACa,UAAU,CAACuE,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAAC8B,UAAU,CAAC;IACvD,IAAI,CAACb,GAAG,CAACyB,qBAAqB,CAAC2D,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACkD,sBAAsB,CAAC,CAAC,CAAC;IAChF,IAAI,CAACjC,GAAG,CAACiB,eAAe,CAACmE,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACkC,eAAe,CAAC;;IAEjE;IACA,IAAI,CAACjB,GAAG,CAACe,UAAU,CAACqE,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACgC,UAAU,CAAC;IAEvD,IAAI,CAACf,GAAG,CAACoB,YAAY,CAACgE,MAAM,CAACC,SAAS,EAAEkB,iBAAiB,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACvG,GAAG,CAACgB,WAAW,CAACoE,MAAM,CAACC,SAAS,EAAEgB,gBAAgB,CAAC;IACxD,IAAI,CAACrG,GAAG,CAACkB,SAAS,CAACkE,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACmC,SAAS,CAAC;IACrD,IAAI,CAAClB,GAAG,CAACmB,YAAY,CAACiE,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACoC,YAAY,CAAC;IAC3D,IAAI,CAACnB,GAAG,CAACc,SAAS,CAACsE,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAAC+B,SAAS,CAAC;IACrD,IAAI,CAACd,GAAG,CAACsB,OAAO,CAAC8D,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACuC,OAAO,CAAC,CAAC,CAAC;IACnD,IAAI,CAACtB,GAAG,CAACqB,OAAO,CAAC+D,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAACiF,UAAU,CAAC,CAAC,CAAC;IACtD,IAAI,CAAChE,GAAG,CAACwB,UAAU,CAAC4D,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAAC4F,MAAM,CAAC;IAEnD,IAAI,CAAC3E,GAAG,CAAC0B,MAAM,CAAC0D,MAAM,CAACC,SAAS,EAAEtG,KAAK,CAAC;IAExC,IAAIA,KAAK,CAACmD,IAAI,EAAE;MACd;MACAnD,KAAK,CAACmD,IAAI,CAACzD,OAAO,CAACsI,GAAG,IAAI;QACxB,IAAIA,GAAG,CAACvE,IAAI,KAAK7F,OAAO,CAACsG,UAAU,EAAE;UACnCoC,SAAS,CAAC2B,QAAQ,CAAC,eAAe,EAAE;YAAC,MAAM,EAAED,GAAG,CAACxE;UAAE,CAAC,CAAC;QACvD;MACF,CAAC,CAAC;IACJ;IAEA8C,SAAS,CAAC4B,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,IAAIA,IAAI,CAACrD,IAAI,KAAK,WAAW,EAAE;MAC7BvH,CAAC,CAAC8K,IAAI,CAAC,IAAI,CAACrH,GAAG,EAAEsH,KAAK,IAAI;QACxBA,KAAK,CAACC,KAAK,CAAC,CAAC;MACf,CAAC,CAAC;MACF,OAAO,IAAI;IACb;IAEA,IAAI,IAAI,CAACvH,GAAG,CAACmH,IAAI,CAACrD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC/D,WAAW,CAACyH,QAAQ,CAACL,IAAI,CAACrD,IAAI,CAAC,EAAE;MAChE,IAAI,CAACsD,MAAM,GAAG,IAAI,CAACpH,GAAG,CAACmH,IAAI,CAACrD,IAAI,CAAC;MACjC,IAAI,CAACsD,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;IAC7B;IACA,OAAO,IAAI;EACb;EAEAM,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACK,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAAC7D,IAAI,EAAE;IACf,IAAI,IAAI,CAACsD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACO,UAAU,CAAC7D,IAAI,CAAC,EAAE;QACjC,IAAI,CAACsD,MAAM,GAAGvI,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQiF,IAAI;MACV,KAAK,WAAW;QAAE;UAChB,MAAM6B,UAAU,GAAG,IAAI,CAAC3F,GAAG,CAACO,aAAa,CAACxB,KAAK,IAAI,CAAC,CAAC;UACrD,IAAI,IAAI,CAACiB,GAAG,CAACC,OAAO,CAAClB,KAAK,IAAI,IAAI,CAACiB,GAAG,CAACC,OAAO,CAAClB,KAAK,CAACoH,QAAQ,EAAE;YAC7DR,UAAU,CAACQ,QAAQ,GAAG,IAAI,CAACnG,GAAG,CAACC,OAAO,CAAClB,KAAK,CAACoH,QAAQ;UACvD;UACA,IAAI,IAAI,CAACnG,GAAG,CAACC,OAAO,CAAClB,KAAK,IAAI,IAAI,CAACiB,GAAG,CAACC,OAAO,CAAClB,KAAK,CAACmH,iBAAiB,EAAE;YACtEP,UAAU,CAACO,iBAAiB,GAAG,IAAI,CAAClG,GAAG,CAACC,OAAO,CAAClB,KAAK,CAACmH,iBAAiB;UACzE;UACA,MAAM0B,eAAe,GAAG;YACtBxB,SAAS,EACN,IAAI,CAACpG,GAAG,CAACC,OAAO,CAAClB,KAAK,IACrB,IAAI,CAACiB,GAAG,CAACC,OAAO,CAAClB,KAAK,CAACmC,SAAS,IAChC,IAAI,CAAClB,GAAG,CAACC,OAAO,CAAClB,KAAK,CAACmC,SAAS,CAACkF,SAAS,IAC5C,KAAK;YACPE,OAAO,EAAE,IAAI,CAACtG,GAAG,CAACgB,WAAW,CAACjC;UAChC,CAAC;UACD,MAAMmC,SAAS,GAAG3C,MAAM,CAACsJ,MAAM,CAACD,eAAe,EAAE,IAAI,CAAC5H,GAAG,CAACkB,SAAS,CAACnC,KAAK,EAAE,IAAI,CAACiB,GAAG,CAACoB,YAAY,CAACrC,KAAK,CAAC;UACvG,MAAMkD,sBAAsB,GAAGnD,2BAA2B,CACxD,IAAI,CAACkB,GAAG,CAACyB,qBAAqB,CAAC1C,KAAK,EACpC,IAAI,CAACiB,GAAG,CAAC0B,MAAM,CAAC3C,KAAK,IAAI,IAAI,CAACiB,GAAG,CAAC0B,MAAM,CAAC3C,KAAK,CAAC,4BAA4B,CAC7E,CAAC;UACD,IAAI,CAACA,KAAK,GAAG;YACX8H,UAAU,EAAE,IAAI,CAAC7G,GAAG,CAACE,SAAS,CAACnB,KAAK;YACpCyB,IAAI,EAAE,IAAI,CAACR,GAAG,CAACQ,IAAI,CAACzB,KAAK;YACzBiD,IAAI,EAAE,IAAI,CAAChC,GAAG,CAACS,SAAS,CAAC1B,KAAK;YAC9B8B,UAAU,EAAE,IAAI,CAACb,GAAG,CAACa,UAAU,CAAC9B,KAAK;YACrCgC,UAAU,EAAE,IAAI,CAACf,GAAG,CAACe,UAAU,CAAChC,KAAK;YACrCkC,eAAe,EAAE,IAAI,CAACjB,GAAG,CAACiB,eAAe,CAAClC,KAAK;YAC/C4G,UAAU;YACVmB,KAAK,EAAE,IAAI,CAAC9G,GAAG,CAACG,UAAU,CAACpB,KAAK;YAChCmC,SAAS;YACTC,YAAY,EAAE,IAAI,CAACnB,GAAG,CAACmB,YAAY,CAACpC,KAAK;YACzCiF,UAAU,EAAE,IAAI,CAAChE,GAAG,CAACqB,OAAO,CAACtC,KAAK;YAClCuC,OAAO,EAAE,IAAI,CAACtB,GAAG,CAACsB,OAAO,CAACvC,KAAK;YAC/B4F,MAAM,EAAE,IAAI,CAAC3E,GAAG,CAACwB,UAAU,CAACzC,KAAK;YACjCkD;UACF,CAAC;UAED,IAAI,IAAI,CAACjC,GAAG,CAACY,UAAU,CAAC7B,KAAK,EAAE;YAC7B,IAAI,CAACA,KAAK,CAAC6B,UAAU,GAAG,IAAI,CAACZ,GAAG,CAACY,UAAU,CAAC7B,KAAK;UACnD;UACA,IAAI,IAAI,CAACiB,GAAG,CAACuB,eAAe,CAACxC,KAAK,EAAE;YAClC,IAAI,CAACA,KAAK,CAACwC,eAAe,GAAG,IAAI,CAACvB,GAAG,CAACuB,eAAe,CAACxC,KAAK;UAC7D;UAEA,OAAO,KAAK;QACd;MAEA;QACE;QACA,OAAO,IAAI;IACf;EACF;EAEA+I,SAASA,CAAC/I,KAAK,EAAEa,OAAO,EAAE;IACxB;IACA;IACA,MAAMsC,IAAI,GAAG,CAACnD,KAAK,CAACgJ,aAAa,IAAI,EAAE,EAAEC,MAAM,CAAC,CAACC,CAAC,EAAElB,GAAG,KAAK;MAC1DkB,CAAC,CAAClB,GAAG,CAACxE,EAAE,CAAC,GAAGwE,GAAG;MACf,IAAIA,GAAG,CAACvE,IAAI,KAAK7F,OAAO,CAACmG,QAAQ,EAAE;QACjC/D,KAAK,CAAC8C,QAAQ,GAAGjC,OAAO,CAACiC,QAAQ,CAACkF,GAAG,CAACrE,MAAM,CAAC,CAACb,QAAQ;MACxD;MACA,IAAIkF,GAAG,CAACvE,IAAI,KAAK7F,OAAO,CAACsG,UAAU,IAAIlE,KAAK,CAAC8C,QAAQ,IAAI9C,KAAK,CAAC8C,QAAQ,CAAC5C,MAAM,EAAE;QAC9E,MAAMiJ,UAAU,GAAGtI,OAAO,CAACuI,WAAW,CAACpB,GAAG,CAACrE,MAAM,CAAC,CAACb,QAAQ;QAC3D9C,KAAK,CAAC8C,QAAQ,CAACpD,OAAO,CAAC,CAACoE,OAAO,EAAEuF,KAAK,KAAK;UACzCvF,OAAO,CAACwF,IAAI,GAAG9J,MAAM,CAACsJ,MAAM,CAAC,CAAC,CAAC,EAAEhF,OAAO,CAACwF,IAAI,EAAEH,UAAU,CAACE,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC;MACJ;MACA,OAAOH,CAAC;IACV,CAAC,EAAE,CAAC,CAAC,CAAC;IACNrI,OAAO,CAAC0I,WAAW,GAAG,CAACvJ,KAAK,CAAC8C,QAAQ,IAAI,EAAE,EAAEmG,MAAM,CAAC,CAACC,CAAC,EAAEpF,OAAO,KAAK;MAClE,IAAIA,OAAO,CAACxD,GAAG,EAAE;QACf4I,CAAC,CAACpF,OAAO,CAACxD,GAAG,CAAC,GAAGwD,OAAO;MAC1B;MACA,OAAOoF,CAAC;IACV,CAAC,EAAE,CAAC,CAAC,CAAC;IACNrI,OAAO,CAAC2I,YAAY,GAAG,CAACxJ,KAAK,CAACgC,UAAU,IAAI,EAAE,EAAEiH,MAAM,CAAC,CAACC,CAAC,EAAE5F,SAAS,KAAK;MACvE,IAAIA,SAAS,CAACC,GAAG,EAAE;QACjB2F,CAAC,CAAC5F,SAAS,CAACmG,OAAO,CAAC,GAAGtG,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,CAACI,MAAM;MACnD;MACA,OAAOuF,CAAC;IACV,CAAC,EAAE,CAAC,CAAC,CAAC;IACNrI,OAAO,CAACkC,QAAQ,GAAG,CAAC,CAAC;;IAErB;IACA/C,KAAK,CAACiD,IAAI,GAAIjD,KAAK,CAACiD,IAAI,IAAIjD,KAAK,CAACiD,IAAI,CAACyG,MAAM,CAACC,OAAO,CAAC,IAAK,EAAE;IAC7D3J,KAAK,CAACiD,IAAI,CAACvD,OAAO,CAACkK,GAAG,IAAI;MACxBA,GAAG,CAACC,KAAK,GAAID,GAAG,CAACC,KAAK,IAAID,GAAG,CAACC,KAAK,CAACH,MAAM,CAACC,OAAO,CAAC,IAAK,EAAE;IAC5D,CAAC,CAAC;IAEF,IAAI,CAAC1I,GAAG,CAACQ,IAAI,CAACsH,SAAS,CAAC/I,KAAK,CAACyB,IAAI,EAAEZ,OAAO,CAAC;IAC5C,IAAI,CAACI,GAAG,CAACS,SAAS,CAACqH,SAAS,CAAC/I,KAAK,CAACiD,IAAI,EAAEpC,OAAO,CAAC;IACjD,IAAI,CAACI,GAAG,CAACyB,qBAAqB,CAACqG,SAAS,CAAC/I,KAAK,CAACkD,sBAAsB,EAAErC,OAAO,CAAC;IAE/Eb,KAAK,CAAC0E,KAAK,GAAG,EAAE;IAChB,IAAI1E,KAAK,CAACuC,OAAO,EAAE;MACjB,MAAMuH,UAAU,GAAG3G,IAAI,CAACnD,KAAK,CAACuC,OAAO,CAACgB,GAAG,CAAC;MAC1C,MAAMwG,KAAK,GAAGD,UAAU,CAACnG,MAAM,CAACoG,KAAK,CAAC,6CAA6C,CAAC;MACpF,IAAIA,KAAK,EAAE;QACT,MAAMC,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC;QAC5B,MAAMxH,OAAO,GAAG1B,OAAO,CAACwE,QAAQ,CAAC2E,WAAW,CAAC;QAC7CzH,OAAO,CAAC6C,OAAO,CAAC1F,OAAO,CAAC8F,MAAM,IAAI;UAChC,IAAIA,MAAM,CAACb,MAAM,EAAE;YACjB,MAAMO,KAAK,GAAG;cACZN,IAAI,EAAE,OAAO;cACbC,OAAO,EAAEW,MAAM,CAACb,MAAM,CAAC0E,KAAK;cAC5B5D,KAAK,EAAED,MAAM,CAACC,KAAK;cACnBzD,UAAU,EAAEwD,MAAM,CAAClD,OAAO,CAACN;YAC7B,CAAC;YACDhC,KAAK,CAAC0E,KAAK,CAAChE,IAAI,CAACwE,KAAK,CAAC;UACzB;QACF,CAAC,CAAC;MACJ;IACF;IAEA,MAAM+E,aAAa,GAAGjK,KAAK,CAACiF,UAAU,IAAI9B,IAAI,CAACnD,KAAK,CAACiF,UAAU,CAAC1B,GAAG,CAAC;IACpE,IAAI0G,aAAa,EAAE;MACjB,MAAMrG,MAAM,GAAGqG,aAAa,CAACtG,MAAM,CAACuG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;MACvD,MAAMrF,OAAO,GAAGhE,OAAO,CAACsJ,UAAU,IAAItJ,OAAO,CAACsJ,UAAU,CAACvG,MAAM,CAAC;MAChE,IAAIiB,OAAO,KAAK/E,SAAS,EAAE;QACzBE,KAAK,CAAC0E,KAAK,CAAChE,IAAI,CAAC;UACfkE,IAAI,EAAE,YAAY;UAClBC;QACF,CAAC,CAAC;MACJ;IACF;IAEA7E,KAAK,CAAC4F,MAAM,GAAG,CAAC5F,KAAK,CAAC4F,MAAM,IAAI,EAAE,EAAE3E,GAAG,CAACmJ,SAAS,IAAI;MACnD,MAAMpC,GAAG,GAAG7E,IAAI,CAACiH,SAAS,CAAC7G,GAAG,CAAC;MAC/B,OAAO1C,OAAO,CAAC+E,MAAM,CAACoC,GAAG,CAACrE,MAAM,CAAC;IACnC,CAAC,CAAC;IAEF,OAAO3D,KAAK,CAACgJ,aAAa;IAC1B,OAAOhJ,KAAK,CAACgC,UAAU;IACvB,OAAOhC,KAAK,CAAC8C,QAAQ;EACvB;AACF;AAEAnC,cAAc,CAAC+F,oBAAoB,GAAG;EACpC2D,KAAK,EAAE,2DAA2D;EAClE,SAAS,EAAE,qEAAqE;EAChF,UAAU,EAAE,6DAA6D;EACzE,cAAc,EAAE,OAAO;EACvB,aAAa,EAAE;AACjB,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG5J,cAAc"}