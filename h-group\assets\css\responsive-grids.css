/* شبكات متجاوبة محسنة لاستغلال المساحة الكاملة */

/* الشاشات الكبيرة جداً - 1920px وأكثر */
@media (min-width: 1920px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.6rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.6rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.6rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
    gap: 0.4rem;
  }
}

/* الشاشات الكبيرة - 1600px إلى 1919px */
@media (min-width: 1600px) and (max-width: 1919px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.7rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
    gap: 0.7rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.7rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }
}

/* الشاشات المتوسطة الكبيرة - 1400px إلى 1599px */
@media (min-width: 1400px) and (max-width: 1599px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.75rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.75rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
    gap: 0.5rem;
  }
}

/* الشاشات المتوسطة - 1200px إلى 1399px */
@media (min-width: 1200px) and (max-width: 1399px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
    gap: 0.75rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(190px, 1fr));
    gap: 0.75rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
    gap: 0.75rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
  }
}

/* الشاشات الصغيرة المتوسطة - 992px إلى 1199px */
@media (min-width: 992px) and (max-width: 1199px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 0.5rem;
  }
}

/* الشاشات الصغيرة - 768px إلى 991px */
@media (min-width: 768px) and (max-width: 991px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.6rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 0.6rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.6rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.5rem;
  }
}

/* الشاشات الصغيرة - 576px إلى 767px */
@media (min-width: 576px) and (max-width: 767px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 0.5rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 0.5rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 0.5rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.4rem;
  }
}

/* الهواتف - أقل من 576px */
@media (max-width: 575px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid,
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid,
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 0.5rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.4rem;
  }
}

/* تحسينات إضافية للشاشات العريضة */
@media (min-width: 2560px) {
  .cards-grid,
  .stats-grid,
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
  }
  
  .customers-grid,
  .orders-grid,
  .reports-grid,
  .invoices-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.5rem;
  }
  
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.3rem;
  }
}

/* ضمان استغلال المساحة الكاملة */
.grid-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* تحسين عرض البطاقات في الشبكات */
.compact-card,
.dashboard-stat-card,
.enhanced-stat-card {
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
}

/* تحسين الجداول للشاشات العريضة */
@media (min-width: 1400px) {
  .compact-table-container {
    width: 100%;
    max-width: none;
  }
  
  .compact-table {
    width: 100%;
    table-layout: auto;
  }
}

/* إزالة أي قيود على العرض */
* {
  max-width: none !important;
}

.container,
.container-fluid,
.row,
.col {
  max-width: none !important;
  width: 100% !important;
}
