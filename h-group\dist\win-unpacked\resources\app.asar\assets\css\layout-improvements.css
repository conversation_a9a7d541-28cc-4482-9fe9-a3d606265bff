/* تحسينات التخطيط والتصميم المتقدمة */

/* تحسينات الشبكة والتخطيط */
.grid-container {
  display: grid;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* تحسينات الفليكس */
.flex-container {
  display: flex;
  gap: var(--spacing-md);
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* تحسينات المسافات */
.spacing-xs { margin: var(--spacing-xs); }
.spacing-sm { margin: var(--spacing-sm); }
.spacing-md { margin: var(--spacing-md); }
.spacing-lg { margin: var(--spacing-lg); }
.spacing-xl { margin: var(--spacing-xl); }
.spacing-2xl { margin: var(--spacing-2xl); }

.padding-xs { padding: var(--spacing-xs); }
.padding-sm { padding: var(--spacing-sm); }
.padding-md { padding: var(--spacing-md); }
.padding-lg { padding: var(--spacing-lg); }
.padding-xl { padding: var(--spacing-xl); }
.padding-2xl { padding: var(--spacing-2xl); }

.margin-top-xs { margin-top: var(--spacing-xs); }
.margin-top-sm { margin-top: var(--spacing-sm); }
.margin-top-md { margin-top: var(--spacing-md); }
.margin-top-lg { margin-top: var(--spacing-lg); }
.margin-top-xl { margin-top: var(--spacing-xl); }

.margin-bottom-xs { margin-bottom: var(--spacing-xs); }
.margin-bottom-sm { margin-bottom: var(--spacing-sm); }
.margin-bottom-md { margin-bottom: var(--spacing-md); }
.margin-bottom-lg { margin-bottom: var(--spacing-lg); }
.margin-bottom-xl { margin-bottom: var(--spacing-xl); }

/* تحسينات النصوص */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }
.text-light { color: var(--text-light-color); }
.text-dark { color: var(--text-color); }

/* تحسينات الخلفيات */
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }
.bg-light { background-color: var(--background-color); }
.bg-white { background-color: var(--surface-color); }

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark-color));
}

/* تحسينات الحدود */
.border { border: 1px solid var(--border-color); }
.border-light { border: 1px solid var(--border-light-color); }
.border-primary { border: 1px solid var(--primary-color); }
.border-secondary { border: 1px solid var(--secondary-color); }

.border-top { border-top: 1px solid var(--border-color); }
.border-bottom { border-bottom: 1px solid var(--border-color); }
.border-left { border-left: 1px solid var(--border-color); }
.border-right { border-right: 1px solid var(--border-color); }

.rounded { border-radius: var(--radius); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: 50%; }

/* تحسينات الظلال */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-none { box-shadow: none; }

/* تحسينات العرض والارتفاع */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

.min-h-screen { min-height: 100vh; }
.min-h-full { min-height: 100%; }

/* تحسينات الموضع */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* تحسينات العرض */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }

/* تحسينات الفيض */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* تحسينات الشفافية */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* تحسينات التحويل */
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }

/* تحسينات الانتقالات */
.transition { transition: all var(--transition-speed) var(--transition-ease); }
.transition-colors { transition: color var(--transition-speed), background-color var(--transition-speed); }
.transition-transform { transition: transform var(--transition-speed); }
.transition-opacity { transition: opacity var(--transition-speed); }

/* تحسينات التفاعل */
.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* تحسينات الاستجابة */
@media (max-width: 640px) {
  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .flex-container {
    flex-direction: column;
  }
  
  .text-2xl { font-size: var(--font-size-xl); }
  .text-3xl { font-size: var(--font-size-2xl); }
}

@media (max-width: 768px) {
  .grid-3, .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1024px) {
  .grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* تحسينات الطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }
  
  .sidebar {
    display: none;
  }
  
  .main-content {
    margin-right: 0;
  }
}
