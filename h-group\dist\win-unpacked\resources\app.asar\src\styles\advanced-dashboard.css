/* لوحة التحكم المتقدمة - تصميم احترافي */

.advanced-dashboard {
  padding: 20px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

/* رأس لوحة التحكم */
.dashboard-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-title {
  color: white;
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.dashboard-title i {
  color: #ff6b35;
  font-size: 2rem;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-time {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-weight: 500;
}

.refresh-btn {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border: none;
  border-radius: 12px;
  padding: 12px 15px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.refresh-btn i {
  font-size: 1.2rem;
}

/* شبكة مؤشرات الأداء المتقدمة */
.advanced-kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

/* بطاقات مؤشرات الأداء المحسنة */
.advanced-kpi-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.advanced-kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 20px 20px 0 0;
}

.advanced-kpi-card.orders-kpi::before {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.advanced-kpi-card.production-kpi::before {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.advanced-kpi-card.inventory-kpi::before {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.advanced-kpi-card.quality-kpi::before {
  background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.advanced-kpi-card.financial-kpi::before {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.advanced-kpi-card.workers-kpi::before {
  background: linear-gradient(90deg, #34495e, #2c3e50);
}

.advanced-kpi-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

/* رأس بطاقة مؤشر الأداء */
.kpi-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.kpi-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  font-size: 1.8rem;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.orders-kpi .kpi-icon {
  background: linear-gradient(45deg, #3498db, #2980b9);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.production-kpi .kpi-icon {
  background: linear-gradient(45deg, #f39c12, #e67e22);
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.inventory-kpi .kpi-icon {
  background: linear-gradient(45deg, #27ae60, #229954);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.quality-kpi .kpi-icon {
  background: linear-gradient(45deg, #9b59b6, #8e44ad);
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.financial-kpi .kpi-icon {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.workers-kpi .kpi-icon {
  background: linear-gradient(45deg, #34495e, #2c3e50);
  box-shadow: 0 4px 15px rgba(52, 73, 94, 0.3);
}

.kpi-header h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  text-align: center;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 5px 10px;
  border-radius: 20px;
}

.kpi-trend.positive {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.kpi-trend.negative {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

/* محتوى بطاقة مؤشر الأداء */
.kpi-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-value {
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 10px;
}

.kpi-breakdown {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.breakdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 10px;
  background: rgba(52, 152, 219, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(52, 152, 219, 0.1);
}

.breakdown-item .label {
  font-size: 0.85rem;
  color: #7f8c8d;
  margin-bottom: 5px;
  font-weight: 500;
}

.breakdown-item .value {
  font-size: 1.4rem;
  font-weight: 700;
}

.value.pending {
  color: #3498db;
}

.value.progress {
  color: #f39c12;
}

.value.completed {
  color: #27ae60;
}

/* الرسم البياني المصغر */
.kpi-chart {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.mini-donut {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.donut-center {
  position: absolute;
  font-size: 0.9rem;
  font-weight: 700;
  color: #2c3e50;
}

/* شاشة التحميل */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-loading p {
  font-size: 1.2rem;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .advanced-dashboard {
    padding: 15px;
  }

  .dashboard-header {
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-title {
    font-size: 1.8rem;
  }

  .advanced-kpi-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .advanced-kpi-card {
    padding: 20px;
  }

  .main-value {
    font-size: 2.5rem;
  }

  .kpi-breakdown {
    flex-direction: column;
    gap: 8px;
  }

  .breakdown-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
