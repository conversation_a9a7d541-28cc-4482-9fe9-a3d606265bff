const React = require('react');
const { Link } = require('react-router-dom');

const NotFound = () => {
  return React.createElement('div', { className: 'not-found-container' },
    React.createElement('div', { className: 'not-found-content' },
      React.createElement('h1', null, '404'),
      React.createElement('h2', null, 'الصفحة غير موجودة'),
      React.createElement('p', null, 'عذراً، الصفحة التي تبحث عنها غير موجودة.'),
      React.createElement(Link, { to: '/', className: 'btn btn-primary' }, 'العودة إلى الصفحة الرئيسية')
    )
  );
};

module.exports = NotFound;
