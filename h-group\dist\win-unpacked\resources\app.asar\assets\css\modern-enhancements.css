/* تحسينات حديثة إضافية للتطبيق */

/* تحسينات الرسوم المتحركة */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* تطبيق الرسوم المتحركة */
.card {
  animation: fadeInUp 0.6s ease-out;
}

.sidebar-item {
  animation: slideInRight 0.4s ease-out;
}

.stat-card {
  animation: scaleIn 0.5s ease-out;
}

.alert {
  animation: slideInLeft 0.4s ease-out;
}

/* تحسينات الجداول المتقدمة */
.table-modern {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.table-modern thead {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
}

.table-modern th {
  color: var(--light-color);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding: var(--spacing-lg);
  border: none;
}

.table-modern tbody tr {
  border-bottom: 1px solid var(--border-light-color);
  transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05), rgba(249, 115, 22, 0.05));
  transform: scale(1.01);
  box-shadow: var(--shadow-md);
}

.table-modern td {
  padding: var(--spacing-lg);
  vertical-align: middle;
  border: none;
}

/* أزرار عائمة */
.floating-action-btn {
  position: fixed;
  bottom: var(--spacing-2xl);
  left: var(--spacing-2xl);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark-color));
  color: var(--light-color);
  border: none;
  box-shadow: var(--shadow-xl);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-ease);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.floating-action-btn:hover {
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
}

/* بطاقات تفاعلية متقدمة */
.interactive-card {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s ease;
}

.interactive-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.interactive-card:hover::before {
  left: 100%;
}

.interactive-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

/* شريط تقدم حديث */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-light-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-xl);
  transition: width 0.8s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* تحسينات النماذج المتقدمة */
.form-floating {
  position: relative;
}

.form-floating .form-control {
  padding-top: var(--spacing-xl);
  padding-bottom: var(--spacing-sm);
}

.form-floating .form-label {
  position: absolute;
  top: 0;
  right: var(--spacing-lg);
  height: 100%;
  padding: var(--spacing-lg) 0;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
  color: var(--text-light-color);
}

.form-floating .form-control:focus ~ .form-label,
.form-floating .form-control:not(:placeholder-shown) ~ .form-label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* تحسينات الإشعارات */
.notification-toast {
  position: fixed;
  top: var(--spacing-xl);
  left: var(--spacing-xl);
  min-width: 300px;
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-lg);
  z-index: 9999;
  animation: slideInLeft 0.4s ease-out;
  border-right: 4px solid var(--primary-color);
}

.notification-toast.success {
  border-right-color: var(--success-color);
}

.notification-toast.warning {
  border-right-color: var(--warning-color);
}

.notification-toast.error {
  border-right-color: var(--danger-color);
}

/* تحسينات الشريط الجانبي */
.sidebar-brand {
  padding: var(--spacing-xl);
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.sidebar-brand h2 {
  color: var(--light-color);
  font-size: var(--font-size-2xl);
  font-weight: 800;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, var(--light-color), var(--secondary-light-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-brand p {
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--font-size-sm);
  margin: var(--spacing-xs) 0 0 0;
  font-weight: 500;
}

/* تحسينات الأيقونات */
.icon-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات الظلال والإضاءة */
.glow-effect {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}

/* تحسينات الخلفيات */
.gradient-bg {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
}

.pattern-bg {
  background-image:
    radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.1) 1px, transparent 0);
  background-size: 20px 20px;
}

/* تحسينات التمرير */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* تحسينات الانتقالات */
.transition-all {
  transition: all var(--transition-speed) var(--transition-ease);
}

.transition-transform {
  transition: transform var(--transition-speed) var(--transition-ease);
}

.transition-opacity {
  transition: opacity var(--transition-speed) var(--transition-ease);
}

/* الشريط العلوي المحسن */
.top-bar {
  background: linear-gradient(135deg, var(--surface-color), #f8fafc);
  border-bottom: 1px solid var(--border-light-color);
  box-shadow: var(--shadow);
  padding: var(--spacing-md) var(--spacing-2xl);
  position: sticky;
  top: 0;
  z-index: 100;
  margin-right: 280px;
}

.top-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.app-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.app-title {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.app-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light-color);
  margin: 0;
  font-weight: 500;
}

.datetime-display {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: center;
}

.time-display,
.date-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 600;
}

.time-display {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.date-display {
  font-size: var(--font-size-sm);
  color: var(--text-light-color);
}

.notifications-section {
  position: relative;
}

.notifications-btn {
  position: relative;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
  color: var(--light-color);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-ease);
  box-shadow: var(--shadow);
}

.notifications-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.notifications-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--danger-color);
  color: var(--light-color);
  font-size: var(--font-size-xs);
  font-weight: 700;
  min-width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.notifications-dropdown {
  position: absolute;
  top: calc(100% + var(--spacing-md));
  left: -150px;
  width: 350px;
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light-color);
  z-index: 1000;
  animation: fadeInUp 0.3s ease-out;
}

.notifications-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-header h5 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-color);
}

.mark-all-read-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: color var(--transition-speed);
}

.mark-all-read-btn:hover {
  color: var(--primary-dark-color);
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light-color);
  transition: background-color var(--transition-speed);
}

.notification-item:hover {
  background: var(--border-light-color);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--light-color);
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--spacing-xs) 0;
}

.notification-message {
  font-size: var(--font-size-xs);
  color: var(--text-light-color);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.4;
}

.notification-time {
  font-size: var(--font-size-xs);
  color: var(--text-light-color);
  opacity: 0.7;
}

.no-notifications {
  padding: var(--spacing-2xl);
  text-align: center;
  color: var(--text-light-color);
}

.no-notifications i {
  font-size: var(--font-size-3xl);
  color: var(--success-color);
  margin-bottom: var(--spacing-md);
}

.quick-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.quick-action-btn {
  background: transparent;
  border: 2px solid var(--border-color);
  color: var(--text-light-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-ease);
}

.quick-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: scale(1.1);
}

/* تحسينات الاستجابة للشريط العلوي */
@media (max-width: 768px) {
  .top-bar {
    margin-right: 0;
    padding: var(--spacing-md);
  }

  .top-bar-content {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .datetime-display {
    order: 3;
    width: 100%;
    flex-direction: row;
    justify-content: center;
    gap: var(--spacing-lg);
  }

  .notifications-dropdown {
    left: -100px;
    width: 280px;
  }
}

/* بطاقات الإحصائيات المحسنة */
.enhanced-stat-card {
  position: relative;
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light-color);
  transition: all 0.4s var(--transition-ease);
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
}

.enhanced-stat-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.enhanced-stat-card.clickable {
  cursor: pointer;
}

.enhanced-stat-card.clickable:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.enhanced-stat-card.loading {
  pointer-events: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--border-light-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.stat-icon-container {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.stat-icon {
  font-size: 24px;
  color: var(--light-color);
  z-index: 2;
  position: relative;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--danger-color);
}

.trend-neutral {
  color: var(--text-light-color);
}

.trend-value {
  font-weight: 700;
}

.stat-card-body {
  margin-bottom: var(--spacing-lg);
}

.stat-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-light-color);
  margin: 0 0 var(--spacing-sm) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: 800;
  color: var(--text-color);
  line-height: 1;
}

.stat-subtitle {
  font-size: var(--font-size-xs);
  color: var(--text-light-color);
  font-weight: 500;
}

.stat-card-footer {
  margin-top: auto;
}

.progress-indicator .progress-bar {
  height: 4px;
  background: var(--border-light-color);
  border-radius: var(--radius);
  overflow: hidden;
}

.progress-indicator .progress-fill {
  height: 100%;
  border-radius: var(--radius);
  transition: width 1s ease-out;
}

.glow-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.enhanced-stat-card:hover .glow-effect {
  opacity: 1;
}

.wave-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.enhanced-stat-card:hover .wave-effect {
  left: 100%;
}

/* ألوان البطاقات */
.stat-card-primary .stat-icon-container {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
}

.stat-card-primary .progress-fill {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light-color));
}

.stat-card-secondary .stat-icon-container {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark-color));
}

.stat-card-secondary .progress-fill {
  background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light-color));
}

.stat-card-success .stat-icon-container {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-card-success .progress-fill {
  background: linear-gradient(90deg, var(--success-color), #10b981);
}

.stat-card-warning .stat-icon-container {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stat-card-warning .progress-fill {
  background: linear-gradient(90deg, var(--warning-color), #f59e0b);
}

.stat-card-danger .stat-icon-container {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.stat-card-danger .progress-fill {
  background: linear-gradient(90deg, var(--danger-color), #ef4444);
}

.stat-card-info .stat-icon-container {
  background: linear-gradient(135deg, var(--info-color), #0891b2);
}

.stat-card-info .progress-fill {
  background: linear-gradient(90deg, var(--info-color), #06b6d4);
}

/* تحسينات إضافية للواجهة */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.neon-glow {
  box-shadow:
    0 0 5px var(--primary-color),
    0 0 10px var(--primary-color),
    0 0 15px var(--primary-color),
    0 0 20px var(--primary-color);
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* تحسينات الأزرار المتقدمة */
.btn-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  color: var(--light-color);
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-gradient:hover::before {
  left: 100%;
}

.btn-3d {
  transform-style: preserve-3d;
  transition: transform 0.2s ease;
}

.btn-3d:hover {
  transform: translateY(-4px) rotateX(15deg);
}

.btn-3d:active {
  transform: translateY(-2px) rotateX(5deg);
}

/* تحسينات البطاقات المتقدمة */
.card-modern {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all 0.4s ease;
  border: 1px solid var(--border-light-color);
  position: relative;
}

.card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.card-modern:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.card-modern .card-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
  color: var(--light-color);
  padding: var(--spacing-lg);
  border-bottom: none;
}

.card-modern .card-body {
  padding: var(--spacing-xl);
}

/* تحسينات النماذج المتقدمة */
.form-modern .form-group {
  position: relative;
  margin-bottom: var(--spacing-xl);
}

.form-modern .form-control {
  background: var(--surface-color);
  border: 2px solid var(--border-light-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  font-size: var(--font-size-md);
  transition: all var(--transition-speed) var(--transition-ease);
  width: 100%;
}

.form-modern .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-modern .form-label {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--surface-color);
  padding: 0 var(--spacing-sm);
  color: var(--text-light-color);
  transition: all var(--transition-speed) var(--transition-ease);
  pointer-events: none;
}

.form-modern .form-control:focus + .form-label,
.form-modern .form-control:not(:placeholder-shown) + .form-label {
  top: -var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: 600;
}

/* تحسينات الإشعارات المتقدمة */
.toast-modern {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light-color);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  position: relative;
  overflow: hidden;
  animation: slideInRight 0.4s ease-out;
}

.toast-modern::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-color);
}

.toast-modern.success::before {
  background: var(--success-color);
}

.toast-modern.warning::before {
  background: var(--warning-color);
}

.toast-modern.error::before {
  background: var(--danger-color);
}

.toast-modern .toast-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.toast-modern .toast-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--light-color);
  font-size: var(--font-size-sm);
}

.toast-modern.success .toast-icon {
  background: var(--success-color);
}

.toast-modern.warning .toast-icon {
  background: var(--warning-color);
}

.toast-modern.error .toast-icon {
  background: var(--danger-color);
}

.toast-modern .toast-title {
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.toast-modern .toast-message {
  color: var(--text-light-color);
  margin: 0;
  line-height: 1.5;
}
