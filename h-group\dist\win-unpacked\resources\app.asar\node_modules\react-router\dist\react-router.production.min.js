/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
import*as e from"react";import{UNSAFE_invariant as t,joinPaths as r,matchPath as a,UNSAFE_decodePath as n,UNSAFE_getResolveToMatches as o,resolveTo as i,parsePath as l,matchRoutes as u,Action as s,UNSAFE_convertRouteMatchToUiMatch as c,stripBasename as d,IDLE_BLOCKER as p,isRouteErrorResponse as m,createMemoryHistory as h,AbortedDeferredError as f,createRouter as v}from"@remix-run/router";export{AbortedDeferredError,Action as NavigationType,createPath,defer,generatePath,isRouteErrorResponse,json,matchPath,matchRoutes,parsePath,redirect,redirectDocument,replace,resolvePath}from"@remix-run/router";const g=e.createContext(null),E=e.createContext(null),y=e.createContext(null),x=e.createContext(null),C=e.createContext(null),b=e.createContext({outlet:null,matches:[],isDataRoute:!1}),R=e.createContext(null);function S(a,{relative:n}={}){_()||t(!1);let{basename:o,navigator:i}=e.useContext(x),{hash:l,pathname:u,search:s}=A(a,{relative:n}),c=u;return"/"!==o&&(c="/"===u?o:r([o,u])),i.createHref({pathname:c,search:s,hash:l})}function _(){return null!=e.useContext(C)}function P(){return _()||t(!1),e.useContext(C).location}function U(){return e.useContext(C).navigationType}function k(r){_()||t(!1);let{pathname:o}=P();return e.useMemo((()=>a(r,n(o))),[o,r])}function D(t){e.useContext(x).static||e.useLayoutEffect(t)}function N(){let{isDataRoute:a}=e.useContext(b);return a?function(){let{router:t}=V(J.UseNavigateStable),r=Y($.UseNavigateStable),a=e.useRef(!1);return D((()=>{a.current=!0})),e.useCallback(((e,n={})=>{a.current&&("number"==typeof e?t.navigate(e):t.navigate(e,{fromRouteId:r,...n}))}),[t,r])}():function(){_()||t(!1);let a=e.useContext(g),{basename:n,future:l,navigator:u}=e.useContext(x),{matches:s}=e.useContext(b),{pathname:c}=P(),d=JSON.stringify(o(s,l.v7_relativeSplatPath)),p=e.useRef(!1);return D((()=>{p.current=!0})),e.useCallback(((e,t={})=>{if(!p.current)return;if("number"==typeof e)return void u.go(e);let o=i(e,JSON.parse(d),c,"path"===t.relative);null==a&&"/"!==n&&(o.pathname="/"===o.pathname?n:r([n,o.pathname])),(t.replace?u.replace:u.push)(o,t.state,t)}),[n,u,d,c,a])}()}const B=e.createContext(null);function F(){return e.useContext(B)}function L(t){let r=e.useContext(b).outlet;return r?e.createElement(B.Provider,{value:t},r):r}function O(){let{matches:t}=e.useContext(b),r=t[t.length-1];return r?r.params:{}}function A(t,{relative:r}={}){let{future:a}=e.useContext(x),{matches:n}=e.useContext(b),{pathname:l}=P(),u=JSON.stringify(o(n,a.v7_relativeSplatPath));return e.useMemo((()=>i(t,JSON.parse(u),l,"path"===r)),[t,u,l,r])}function j(e,t){return I(e,t)}function I(a,n,o,i){_()||t(!1);let{navigator:c,static:d}=e.useContext(x),{matches:p}=e.useContext(b),m=p[p.length-1],h=m?m.params:{};!m||m.pathname;let f=m?m.pathnameBase:"/";m&&m.route;let v,g=P();if(n){let e="string"==typeof n?l(n):n;"/"===f||e.pathname?.startsWith(f)||t(!1),v=e}else v=g;let E=v.pathname||"/",y=E;if("/"!==f){let e=f.replace(/^\//,"").split("/");y="/"+E.replace(/^\//,"").split("/").slice(e.length).join("/")}let R=!d&&o&&o.matches&&o.matches.length>0?o.matches:u(a,{pathname:y}),S=z(R&&R.map((e=>Object.assign({},e,{params:Object.assign({},h,e.params),pathname:r([f,c.encodeLocation?c.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?f:r([f,c.encodeLocation?c.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),p,o,i);return n&&S?e.createElement(C.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...v},navigationType:s.Pop}},S):S}function M(){let t=te(),r=m(t)?`${t.status} ${t.statusText}`:t instanceof Error?t.message:JSON.stringify(t),a=t instanceof Error?t.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},r),a?e.createElement("pre",{style:n},a):null,null)}const T=e.createElement(M,null);class H extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?e.createElement(b.Provider,{value:this.props.routeContext},e.createElement(R.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function w({routeContext:t,match:r,children:a}){let n=e.useContext(g);return n&&n.static&&n.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=r.route.id),e.createElement(b.Provider,{value:t},a)}function z(r,a=[],n=null,o=null){if(null==r){if(!n)return null;if(n.errors)r=n.matches;else{if(!(o?.v7_partialHydration&&0===a.length&&!n.initialized&&n.matches.length>0))return null;r=n.matches}}let i=r,l=n?.errors;if(null!=l){let e=i.findIndex((e=>e.route.id&&void 0!==l?.[e.route.id]));e>=0||t(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,s=-1;if(n&&o&&o.v7_partialHydration)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(s=e),t.route.id){let{loaderData:e,errors:r}=n,a=t.route.loader&&void 0===e[t.route.id]&&(!r||void 0===r[t.route.id]);if(t.route.lazy||a){u=!0,i=s>=0?i.slice(0,s+1):[i[0]];break}}}return i.reduceRight(((t,r,o)=>{let c,d=!1,p=null,m=null;var h;n&&(c=l&&r.route.id?l[r.route.id]:void 0,p=r.route.errorElement||T,u&&(s<0&&0===o?(h="route-fallback",!1||ie[h]||(ie[h]=!0),d=!0,m=null):s===o&&(d=!0,m=r.route.hydrateFallbackElement||null)));let f=a.concat(i.slice(0,o+1)),v=()=>{let a;return a=c?p:d?m:r.route.Component?e.createElement(r.route.Component,null):r.route.element?r.route.element:t,e.createElement(w,{match:r,routeContext:{outlet:t,matches:f,isDataRoute:null!=n},children:a})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?e.createElement(H,{location:n.location,revalidation:n.revalidation,component:p,error:c,children:v(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):v()}),null)}var J=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(J||{}),$=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}($||{});function V(r){let a=e.useContext(g);return a||t(!1),a}function W(r){let a=e.useContext(E);return a||t(!1),a}function Y(r){let a=function(r){let a=e.useContext(b);return a||t(!1),a}(),n=a.matches[a.matches.length-1];return n.route.id||t(!1),n.route.id}function q(){return Y($.UseRouteId)}function G(){return W($.UseNavigation).navigation}function K(){let t=V(J.UseRevalidator),r=W($.UseRevalidator);return e.useMemo((()=>({revalidate:t.router.revalidate,state:r.revalidation})),[t.router.revalidate,r.revalidation])}function Q(){let{matches:t,loaderData:r}=W($.UseMatches);return e.useMemo((()=>t.map((e=>c(e,r)))),[t,r])}function X(){let e=W($.UseLoaderData),t=Y($.UseLoaderData);if(!e.errors||null==e.errors[t])return e.loaderData[t];console.error(`You cannot \`useLoaderData\` in an errorElement (routeId: ${t})`)}function Z(e){return W($.UseRouteLoaderData).loaderData[e]}function ee(){let e=W($.UseActionData),t=Y($.UseLoaderData);return e.actionData?e.actionData[t]:void 0}function te(){let t=e.useContext(R),r=W($.UseRouteError),a=Y($.UseRouteError);return void 0!==t?t:r.errors?.[a]}function re(){return e.useContext(y)?._data}function ae(){return e.useContext(y)?._error}let ne=0;function oe(t){let{router:r,basename:a}=V(J.UseBlocker),n=W($.UseBlocker),[o,i]=e.useState(""),l=e.useCallback((e=>{if("function"!=typeof t)return!!t;if("/"===a)return t(e);let{currentLocation:r,nextLocation:n,historyAction:o}=e;return t({currentLocation:{...r,pathname:d(r.pathname,a)||r.pathname},nextLocation:{...n,pathname:d(n.pathname,a)||n.pathname},historyAction:o})}),[a,t]);return e.useEffect((()=>{let e=String(++ne);return i(e),()=>r.deleteBlocker(e)}),[r]),e.useEffect((()=>{""!==o&&r.getBlocker(o,l)}),[r,o,l]),o&&n.blockers.has(o)?n.blockers.get(o):p}const ie={};function le(e,t){e?.v7_startTransition,void 0!==e?.v7_relativeSplatPath||t&&t.v7_relativeSplatPath,t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}const ue=e.startTransition;function se({fallbackElement:t,router:r,future:a}){let[n,o]=e.useState(r.state),{v7_startTransition:i}=a||{},l=e.useCallback((e=>{i&&ue?ue((()=>o(e))):o(e)}),[o,i]);e.useLayoutEffect((()=>r.subscribe(l)),[r,l]),e.useEffect((()=>{}),[]);let u=e.useMemo((()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:e=>r.navigate(e),push:(e,t,a)=>r.navigate(e,{state:t,preventScrollReset:a?.preventScrollReset}),replace:(e,t,a)=>r.navigate(e,{replace:!0,state:t,preventScrollReset:a?.preventScrollReset})})),[r]),s=r.basename||"/",c=e.useMemo((()=>({router:r,navigator:u,static:!1,basename:s})),[r,u,s]);return e.useEffect((()=>le(a,r.future)),[r,a]),e.createElement(e.Fragment,null,e.createElement(g.Provider,{value:c},e.createElement(E.Provider,{value:n},e.createElement(fe,{basename:s,location:n.location,navigationType:n.historyAction,navigator:u,future:{v7_relativeSplatPath:r.future.v7_relativeSplatPath}},n.initialized||r.future.v7_partialHydration?e.createElement(ce,{routes:r.routes,future:r.future,state:n}):t))),null)}function ce({routes:e,future:t,state:r}){return I(e,void 0,r,t)}function de({basename:t,children:r,initialEntries:a,initialIndex:n,future:o}){let i=e.useRef();null==i.current&&(i.current=h({initialEntries:a,initialIndex:n,v5Compat:!0}));let l=i.current,[u,s]=e.useState({action:l.action,location:l.location}),{v7_startTransition:c}=o||{},d=e.useCallback((e=>{c&&ue?ue((()=>s(e))):s(e)}),[s,c]);return e.useLayoutEffect((()=>l.listen(d)),[l,d]),e.useEffect((()=>le(o)),[o]),e.createElement(fe,{basename:t,children:r,location:u.location,navigationType:u.action,navigator:l,future:o})}function pe({to:r,replace:a,state:n,relative:l}){_()||t(!1);let{future:u,static:s}=e.useContext(x),{matches:c}=e.useContext(b),{pathname:d}=P(),p=N(),m=i(r,o(c,u.v7_relativeSplatPath),d,"path"===l),h=JSON.stringify(m);return e.useEffect((()=>p(JSON.parse(h),{replace:a,state:n,relative:l})),[p,h,l,a,n]),null}function me(e){return L(e.context)}function he(e){t(!1)}function fe({basename:r="/",children:a=null,location:n,navigationType:o=s.Pop,navigator:i,static:u=!1,future:c}){_()&&t(!1);let p=r.replace(/^\/*/,"/"),m=e.useMemo((()=>({basename:p,navigator:i,static:u,future:{v7_relativeSplatPath:!1,...c}})),[p,c,i,u]);"string"==typeof n&&(n=l(n));let{pathname:h="/",search:f="",hash:v="",state:g=null,key:E="default"}=n,y=e.useMemo((()=>{let e=d(h,p);return null==e?null:{location:{pathname:e,search:f,hash:v,state:g,key:E},navigationType:o}}),[p,h,f,v,g,E,o]);return null==y?null:e.createElement(x.Provider,{value:m},e.createElement(C.Provider,{children:a,value:y}))}function ve({children:e,location:t}){return j(be(e),t)}function ge({children:t,errorElement:r,resolve:a}){return e.createElement(xe,{resolve:a,errorElement:r},e.createElement(Ce,null,t))}var Ee=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(Ee||{});const ye=new Promise((()=>{}));class xe extends e.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:t,errorElement:r,resolve:a}=this.props,n=null,o=Ee.pending;if(a instanceof Promise)if(this.state.error){o=Ee.error;let e=this.state.error;n=Promise.reject().catch((()=>{})),Object.defineProperty(n,"_tracked",{get:()=>!0}),Object.defineProperty(n,"_error",{get:()=>e})}else a._tracked?(n=a,o="_error"in n?Ee.error:"_data"in n?Ee.success:Ee.pending):(o=Ee.pending,Object.defineProperty(a,"_tracked",{get:()=>!0}),n=a.then((e=>Object.defineProperty(a,"_data",{get:()=>e})),(e=>Object.defineProperty(a,"_error",{get:()=>e}))));else o=Ee.success,n=Promise.resolve(),Object.defineProperty(n,"_tracked",{get:()=>!0}),Object.defineProperty(n,"_data",{get:()=>a});if(o===Ee.error&&n._error instanceof f)throw ye;if(o===Ee.error&&!r)throw n._error;if(o===Ee.error)return e.createElement(y.Provider,{value:n,children:r});if(o===Ee.success)return e.createElement(y.Provider,{value:n,children:t});throw n}}function Ce({children:t}){let r=re(),a="function"==typeof t?t(r):t;return e.createElement(e.Fragment,null,a)}function be(r,a=[]){let n=[];return e.Children.forEach(r,((r,o)=>{if(!e.isValidElement(r))return;let i=[...a,o];if(r.type===e.Fragment)return void n.push.apply(n,be(r.props.children,i));r.type!==he&&t(!1),r.props.index&&r.props.children&&t(!1);let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:null!=r.props.ErrorBoundary||null!=r.props.errorElement,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=be(r.props.children,i)),n.push(l)})),n}function Re(e){return z(e)}function Se(t){let r={hasErrorBoundary:null!=t.ErrorBoundary||null!=t.errorElement};return t.Component&&Object.assign(r,{element:e.createElement(t.Component),Component:void 0}),t.HydrateFallback&&Object.assign(r,{hydrateFallbackElement:e.createElement(t.HydrateFallback),HydrateFallback:void 0}),t.ErrorBoundary&&Object.assign(r,{errorElement:e.createElement(t.ErrorBoundary),ErrorBoundary:void 0}),r}function _e(e,t){return v({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:h({initialEntries:t?.initialEntries,initialIndex:t?.initialIndex}),hydrationData:t?.hydrationData,routes:e,mapRouteProperties:Se,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation}).initialize()}export{ge as Await,de as MemoryRouter,pe as Navigate,me as Outlet,he as Route,fe as Router,se as RouterProvider,ve as Routes,g as UNSAFE_DataRouterContext,E as UNSAFE_DataRouterStateContext,C as UNSAFE_LocationContext,x as UNSAFE_NavigationContext,b as UNSAFE_RouteContext,le as UNSAFE_logV6DeprecationWarnings,Se as UNSAFE_mapRouteProperties,q as UNSAFE_useRouteId,I as UNSAFE_useRoutesImpl,_e as createMemoryRouter,be as createRoutesFromChildren,be as createRoutesFromElements,Re as renderMatches,ee as useActionData,ae as useAsyncError,re as useAsyncValue,oe as useBlocker,S as useHref,_ as useInRouterContext,X as useLoaderData,P as useLocation,k as useMatch,Q as useMatches,N as useNavigate,G as useNavigation,U as useNavigationType,L as useOutlet,F as useOutletContext,O as useParams,A as useResolvedPath,K as useRevalidator,te as useRouteError,Z as useRouteLoaderData,j as useRoutes};
//# sourceMappingURL=react-router.production.min.js.map
