{"version": 3, "file": "vml-textbox-xform.js", "names": ["BaseXform", "require", "VmlTextboxXform", "tag", "conversionUnit", "value", "multiple", "unit", "parseFloat", "toFixed", "reverseConversionUnit", "inset", "split", "map", "margin", "Number", "render", "xmlStream", "model", "attributes", "style", "note", "margins", "Array", "isArray", "join", "openNode", "leafNode", "closeNode", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/comment/vml-textbox-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass VmlTextboxXform extends BaseXform {\n  get tag() {\n    return 'v:textbox';\n  }\n\n  conversionUnit(value, multiple, unit) {\n    return `${parseFloat(value) * multiple.toFixed(2)}${unit}`;\n  }\n\n  reverseConversionUnit(inset) {\n    return (inset || '').split(',').map(margin => {\n      return Number(parseFloat(this.conversionUnit(parseFloat(margin), 0.1, '')).toFixed(2));\n    });\n  }\n\n  render(xmlStream, model) {\n    const attributes = {\n      style: 'mso-direction-alt:auto',\n    };\n    if (model && model.note) {\n      let {inset} = model.note && model.note.margins;\n      if (Array.isArray(inset)) {\n        inset = inset\n          .map(margin => {\n            return this.conversionUnit(margin, 10, 'mm');\n          })\n          .join(',');\n      }\n      if (inset) {\n        attributes.inset = inset;\n      }\n    }\n    xmlStream.openNode('v:textbox', attributes);\n    xmlStream.leafNode('div', {style: 'text-align:left'});\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          inset: this.reverseConversionUnit(node.attributes.inset),\n        };\n        return true;\n      default:\n        return true;\n    }\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = VmlTextboxXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,eAAe,SAASF,SAAS,CAAC;EACtC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,cAAcA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAE;IACpC,OAAQ,GAAEC,UAAU,CAACH,KAAK,CAAC,GAAGC,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAE,GAAEF,IAAK,EAAC;EAC5D;EAEAG,qBAAqBA,CAACC,KAAK,EAAE;IAC3B,OAAO,CAACA,KAAK,IAAI,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,IAAI;MAC5C,OAAOC,MAAM,CAACP,UAAU,CAAC,IAAI,CAACJ,cAAc,CAACI,UAAU,CAACM,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC,CAAC;EACJ;EAEAO,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,MAAMC,UAAU,GAAG;MACjBC,KAAK,EAAE;IACT,CAAC;IACD,IAAIF,KAAK,IAAIA,KAAK,CAACG,IAAI,EAAE;MACvB,IAAI;QAACV;MAAK,CAAC,GAAGO,KAAK,CAACG,IAAI,IAAIH,KAAK,CAACG,IAAI,CAACC,OAAO;MAC9C,IAAIC,KAAK,CAACC,OAAO,CAACb,KAAK,CAAC,EAAE;QACxBA,KAAK,GAAGA,KAAK,CACVE,GAAG,CAACC,MAAM,IAAI;UACb,OAAO,IAAI,CAACV,cAAc,CAACU,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;QAC9C,CAAC,CAAC,CACDW,IAAI,CAAC,GAAG,CAAC;MACd;MACA,IAAId,KAAK,EAAE;QACTQ,UAAU,CAACR,KAAK,GAAGA,KAAK;MAC1B;IACF;IACAM,SAAS,CAACS,QAAQ,CAAC,WAAW,EAAEP,UAAU,CAAC;IAC3CF,SAAS,CAACU,QAAQ,CAAC,KAAK,EAAE;MAACP,KAAK,EAAE;IAAiB,CAAC,CAAC;IACrDH,SAAS,CAACW,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAAC5B,GAAG;QACX,IAAI,CAACe,KAAK,GAAG;UACXP,KAAK,EAAE,IAAI,CAACD,qBAAqB,CAACoB,IAAI,CAACX,UAAU,CAACR,KAAK;QACzD,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAqB,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACF,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,IAAI,CAAC5B,GAAG;QACX,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEA+B,MAAM,CAACC,OAAO,GAAGjC,eAAe"}