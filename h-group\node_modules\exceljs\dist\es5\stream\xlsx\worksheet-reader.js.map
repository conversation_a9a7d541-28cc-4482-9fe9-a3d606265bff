{"version": 3, "file": "worksheet-reader.js", "names": ["EventEmitter", "require", "parseSax", "_", "utils", "co<PERSON><PERSON><PERSON>", "Dimensions", "Row", "Column", "WorksheetReader", "constructor", "_ref", "workbook", "id", "iterator", "options", "name", "_columns", "_keys", "_dimensions", "destroy", "Error", "dimensions", "columns", "getColumn", "c", "col", "l2n", "length", "n", "push", "getColumnKey", "key", "setColumnKey", "value", "deleteColumnKey", "eachColumnKey", "f", "each", "read", "events", "parse", "eventType", "emit", "error", "Symbol", "asyncIterator", "emitSheet", "emitHyperlinks", "hyperlinks", "worksheets", "sharedStrings", "styles", "properties", "inCols", "inRows", "inHyperlinks", "cols", "row", "current", "worksheetEvents", "node", "min", "parseInt", "attributes", "max", "width", "parseFloat", "styleId", "style", "r", "ht", "height", "s", "getStyleModel", "ref", "t", "text", "v", "hyperlink", "rId", "fromModel", "expandRow", "address", "decode<PERSON>ddress", "cell", "getCell", "cellValue", "formula", "result", "xmlDecode", "index", "sharedString", "isDateFmt", "numFmt", "excelToDate", "model", "date1904", "undefined", "module", "exports"], "sources": ["../../../../lib/stream/xlsx/worksheet-reader.js"], "sourcesContent": ["const {EventEmitter} = require('events');\nconst parseSax = require('../../utils/parse-sax');\n\nconst _ = require('../../utils/under-dash');\nconst utils = require('../../utils/utils');\nconst colCache = require('../../utils/col-cache');\nconst Dimensions = require('../../doc/range');\n\nconst Row = require('../../doc/row');\nconst Column = require('../../doc/column');\n\nclass WorksheetReader extends EventEmitter {\n  constructor({workbook, id, iterator, options}) {\n    super();\n\n    this.workbook = workbook;\n    this.id = id;\n    this.iterator = iterator;\n    this.options = options || {};\n\n    // and a name\n    this.name = `Sheet${this.id}`;\n\n    // column definitions\n    this._columns = null;\n    this._keys = {};\n\n    // keep a record of dimensions\n    this._dimensions = new Dimensions();\n  }\n\n  // destroy - not a valid operation for a streaming writer\n  // even though some streamers might be able to, it's a bad idea.\n  destroy() {\n    throw new Error('Invalid Operation: destroy');\n  }\n\n  // return the current dimensions of the writer\n  get dimensions() {\n    return this._dimensions;\n  }\n\n  // =========================================================================\n  // Columns\n\n  // get the current columns array.\n  get columns() {\n    return this._columns;\n  }\n\n  // get a single column by col number. If it doesn't exist, it and any gaps before it\n  // are created.\n  getColumn(c) {\n    if (typeof c === 'string') {\n      // if it matches a key'd column, return that\n      const col = this._keys[c];\n      if (col) {\n        return col;\n      }\n\n      // otherise, assume letter\n      c = colCache.l2n(c);\n    }\n    if (!this._columns) {\n      this._columns = [];\n    }\n    if (c > this._columns.length) {\n      let n = this._columns.length + 1;\n      while (n <= c) {\n        this._columns.push(new Column(this, n++));\n      }\n    }\n    return this._columns[c - 1];\n  }\n\n  getColumnKey(key) {\n    return this._keys[key];\n  }\n\n  setColumnKey(key, value) {\n    this._keys[key] = value;\n  }\n\n  deleteColumnKey(key) {\n    delete this._keys[key];\n  }\n\n  eachColumnKey(f) {\n    _.each(this._keys, f);\n  }\n\n  async read() {\n    try {\n      for await (const events of this.parse()) {\n        for (const {eventType, value} of events) {\n          this.emit(eventType, value);\n        }\n      }\n      this.emit('finished');\n    } catch (error) {\n      this.emit('error', error);\n    }\n  }\n\n  async *[Symbol.asyncIterator]() {\n    for await (const events of this.parse()) {\n      for (const {eventType, value} of events) {\n        if (eventType === 'row') {\n          yield value;\n        }\n      }\n    }\n  }\n\n  async *parse() {\n    const {iterator, options} = this;\n    let emitSheet = false;\n    let emitHyperlinks = false;\n    let hyperlinks = null;\n    switch (options.worksheets) {\n      case 'emit':\n        emitSheet = true;\n        break;\n      case 'prep':\n        break;\n      default:\n        break;\n    }\n    switch (options.hyperlinks) {\n      case 'emit':\n        emitHyperlinks = true;\n        break;\n      case 'cache':\n        this.hyperlinks = hyperlinks = {};\n        break;\n      default:\n        break;\n    }\n    if (!emitSheet && !emitHyperlinks && !hyperlinks) {\n      return;\n    }\n\n    // references\n    const {sharedStrings, styles, properties} = this.workbook;\n\n    // xml position\n    let inCols = false;\n    let inRows = false;\n    let inHyperlinks = false;\n\n    // parse state\n    let cols = null;\n    let row = null;\n    let c = null;\n    let current = null;\n    for await (const events of parseSax(iterator)) {\n      const worksheetEvents = [];\n      for (const {eventType, value} of events) {\n        if (eventType === 'opentag') {\n          const node = value;\n          if (emitSheet) {\n            switch (node.name) {\n              case 'cols':\n                inCols = true;\n                cols = [];\n                break;\n              case 'sheetData':\n                inRows = true;\n                break;\n\n              case 'col':\n                if (inCols) {\n                  cols.push({\n                    min: parseInt(node.attributes.min, 10),\n                    max: parseInt(node.attributes.max, 10),\n                    width: parseFloat(node.attributes.width),\n                    styleId: parseInt(node.attributes.style || '0', 10),\n                  });\n                }\n                break;\n\n              case 'row':\n                if (inRows) {\n                  const r = parseInt(node.attributes.r, 10);\n                  row = new Row(this, r);\n                  if (node.attributes.ht) {\n                    row.height = parseFloat(node.attributes.ht);\n                  }\n                  if (node.attributes.s) {\n                    const styleId = parseInt(node.attributes.s, 10);\n                    const style = styles.getStyleModel(styleId);\n                    if (style) {\n                      row.style = style;\n                    }\n                  }\n                }\n                break;\n              case 'c':\n                if (row) {\n                  c = {\n                    ref: node.attributes.r,\n                    s: parseInt(node.attributes.s, 10),\n                    t: node.attributes.t,\n                  };\n                }\n                break;\n              case 'f':\n                if (c) {\n                  current = c.f = {text: ''};\n                }\n                break;\n              case 'v':\n                if (c) {\n                  current = c.v = {text: ''};\n                }\n                break;\n              case 'is':\n              case 't':\n                if (c) {\n                  current = c.v = {text: ''};\n                }\n                break;\n              case 'mergeCell':\n                break;\n              default:\n                break;\n            }\n          }\n\n          // =================================================================\n          //\n          if (emitHyperlinks || hyperlinks) {\n            switch (node.name) {\n              case 'hyperlinks':\n                inHyperlinks = true;\n                break;\n              case 'hyperlink':\n                if (inHyperlinks) {\n                  const hyperlink = {\n                    ref: node.attributes.ref,\n                    rId: node.attributes['r:id'],\n                  };\n                  if (emitHyperlinks) {\n                    worksheetEvents.push({eventType: 'hyperlink', value: hyperlink});\n                  } else {\n                    hyperlinks[hyperlink.ref] = hyperlink;\n                  }\n                }\n                break;\n              default:\n                break;\n            }\n          }\n        } else if (eventType === 'text') {\n          // only text data is for sheet values\n          if (emitSheet) {\n            if (current) {\n              current.text += value;\n            }\n          }\n        } else if (eventType === 'closetag') {\n          const node = value;\n          if (emitSheet) {\n            switch (node.name) {\n              case 'cols':\n                inCols = false;\n                this._columns = Column.fromModel(cols);\n                break;\n              case 'sheetData':\n                inRows = false;\n                break;\n\n              case 'row':\n                this._dimensions.expandRow(row);\n                worksheetEvents.push({eventType: 'row', value: row});\n                row = null;\n                break;\n\n              case 'c':\n                if (row && c) {\n                  const address = colCache.decodeAddress(c.ref);\n                  const cell = row.getCell(address.col);\n                  if (c.s) {\n                    const style = styles.getStyleModel(c.s);\n                    if (style) {\n                      cell.style = style;\n                    }\n                  }\n\n                  if (c.f) {\n                    const cellValue = {\n                      formula: c.f.text,\n                    };\n                    if (c.v) {\n                      if (c.t === 'str') {\n                        cellValue.result = utils.xmlDecode(c.v.text);\n                      } else {\n                        cellValue.result = parseFloat(c.v.text);\n                      }\n                    }\n                    cell.value = cellValue;\n                  } else if (c.v) {\n                    switch (c.t) {\n                      case 's': {\n                        const index = parseInt(c.v.text, 10);\n                        if (sharedStrings) {\n                          cell.value = sharedStrings[index];\n                        } else {\n                          cell.value = {\n                            sharedString: index,\n                          };\n                        }\n                        break;\n                      }\n\n                      case 'inlineStr':\n                      case 'str':\n                        cell.value = utils.xmlDecode(c.v.text);\n                        break;\n\n                      case 'e':\n                        cell.value = {error: c.v.text};\n                        break;\n\n                      case 'b':\n                        cell.value = parseInt(c.v.text, 10) !== 0;\n                        break;\n\n                      default:\n                        if (utils.isDateFmt(cell.numFmt)) {\n                          cell.value = utils.excelToDate(\n                            parseFloat(c.v.text),\n                            properties.model && properties.model.date1904\n                          );\n                        } else {\n                          cell.value = parseFloat(c.v.text);\n                        }\n                        break;\n                    }\n                  }\n                  if (hyperlinks) {\n                    const hyperlink = hyperlinks[c.ref];\n                    if (hyperlink) {\n                      cell.text = cell.value;\n                      cell.value = undefined;\n                      cell.hyperlink = hyperlink;\n                    }\n                  }\n                  c = null;\n                }\n                break;\n              default:\n                break;\n            }\n          }\n          if (emitHyperlinks || hyperlinks) {\n            switch (node.name) {\n              case 'hyperlinks':\n                inHyperlinks = false;\n                break;\n              default:\n                break;\n            }\n          }\n        }\n      }\n      if (worksheetEvents.length > 0) {\n        yield worksheetEvents;\n      }\n    }\n  }\n}\n\nmodule.exports = WorksheetReader;\n"], "mappings": ";;AAAA,MAAM;EAACA;AAAY,CAAC,GAAGC,OAAO,CAAC,QAAQ,CAAC;AACxC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEjD,MAAME,CAAC,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAC3C,MAAMG,KAAK,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAC1C,MAAMI,QAAQ,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;AACjD,MAAMK,UAAU,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAE7C,MAAMM,GAAG,GAAGN,OAAO,CAAC,eAAe,CAAC;AACpC,MAAMO,MAAM,GAAGP,OAAO,CAAC,kBAAkB,CAAC;AAE1C,MAAMQ,eAAe,SAAST,YAAY,CAAC;EACzCU,WAAWA,CAAAC,IAAA,EAAoC;IAAA,IAAnC;MAACC,QAAQ;MAAEC,EAAE;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAAJ,IAAA;IAC3C,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;IAE5B;IACA,IAAI,CAACC,IAAI,GAAI,QAAO,IAAI,CAACH,EAAG,EAAC;;IAE7B;IACA,IAAI,CAACI,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;;IAEf;IACA,IAAI,CAACC,WAAW,GAAG,IAAIb,UAAU,CAAC,CAAC;EACrC;;EAEA;EACA;EACAc,OAAOA,CAAA,EAAG;IACR,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;EAC/C;;EAEA;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,WAAW;EACzB;;EAEA;EACA;;EAEA;EACA,IAAII,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACN,QAAQ;EACtB;;EAEA;EACA;EACAO,SAASA,CAACC,CAAC,EAAE;IACX,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB;MACA,MAAMC,GAAG,GAAG,IAAI,CAACR,KAAK,CAACO,CAAC,CAAC;MACzB,IAAIC,GAAG,EAAE;QACP,OAAOA,GAAG;MACZ;;MAEA;MACAD,CAAC,GAAGpB,QAAQ,CAACsB,GAAG,CAACF,CAAC,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,EAAE;IACpB;IACA,IAAIQ,CAAC,GAAG,IAAI,CAACR,QAAQ,CAACW,MAAM,EAAE;MAC5B,IAAIC,CAAC,GAAG,IAAI,CAACZ,QAAQ,CAACW,MAAM,GAAG,CAAC;MAChC,OAAOC,CAAC,IAAIJ,CAAC,EAAE;QACb,IAAI,CAACR,QAAQ,CAACa,IAAI,CAAC,IAAItB,MAAM,CAAC,IAAI,EAAEqB,CAAC,EAAE,CAAC,CAAC;MAC3C;IACF;IACA,OAAO,IAAI,CAACZ,QAAQ,CAACQ,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEAM,YAAYA,CAACC,GAAG,EAAE;IAChB,OAAO,IAAI,CAACd,KAAK,CAACc,GAAG,CAAC;EACxB;EAEAC,YAAYA,CAACD,GAAG,EAAEE,KAAK,EAAE;IACvB,IAAI,CAAChB,KAAK,CAACc,GAAG,CAAC,GAAGE,KAAK;EACzB;EAEAC,eAAeA,CAACH,GAAG,EAAE;IACnB,OAAO,IAAI,CAACd,KAAK,CAACc,GAAG,CAAC;EACxB;EAEAI,aAAaA,CAACC,CAAC,EAAE;IACflC,CAAC,CAACmC,IAAI,CAAC,IAAI,CAACpB,KAAK,EAAEmB,CAAC,CAAC;EACvB;EAEA,MAAME,IAAIA,CAAA,EAAG;IACX,IAAI;MACF,WAAW,MAAMC,MAAM,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE;QACvC,KAAK,MAAM;UAACC,SAAS;UAAER;QAAK,CAAC,IAAIM,MAAM,EAAE;UACvC,IAAI,CAACG,IAAI,CAACD,SAAS,EAAER,KAAK,CAAC;QAC7B;MACF;MACA,IAAI,CAACS,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAEC,KAAK,CAAC;IAC3B;EACF;EAEA,QAAQC,MAAM,CAACC,aAAa,IAAI;IAC9B,WAAW,MAAMN,MAAM,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE;MACvC,KAAK,MAAM;QAACC,SAAS;QAAER;MAAK,CAAC,IAAIM,MAAM,EAAE;QACvC,IAAIE,SAAS,KAAK,KAAK,EAAE;UACvB,MAAMR,KAAK;QACb;MACF;IACF;EACF;EAEA,OAAOO,KAAKA,CAAA,EAAG;IACb,MAAM;MAAC3B,QAAQ;MAAEC;IAAO,CAAC,GAAG,IAAI;IAChC,IAAIgC,SAAS,GAAG,KAAK;IACrB,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,UAAU,GAAG,IAAI;IACrB,QAAQlC,OAAO,CAACmC,UAAU;MACxB,KAAK,MAAM;QACTH,SAAS,GAAG,IAAI;QAChB;MACF,KAAK,MAAM;QACT;MACF;QACE;IACJ;IACA,QAAQhC,OAAO,CAACkC,UAAU;MACxB,KAAK,MAAM;QACTD,cAAc,GAAG,IAAI;QACrB;MACF,KAAK,OAAO;QACV,IAAI,CAACC,UAAU,GAAGA,UAAU,GAAG,CAAC,CAAC;QACjC;MACF;QACE;IACJ;IACA,IAAI,CAACF,SAAS,IAAI,CAACC,cAAc,IAAI,CAACC,UAAU,EAAE;MAChD;IACF;;IAEA;IACA,MAAM;MAACE,aAAa;MAAEC,MAAM;MAAEC;IAAU,CAAC,GAAG,IAAI,CAACzC,QAAQ;;IAEzD;IACA,IAAI0C,MAAM,GAAG,KAAK;IAClB,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,YAAY,GAAG,KAAK;;IAExB;IACA,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIjC,CAAC,GAAG,IAAI;IACZ,IAAIkC,OAAO,GAAG,IAAI;IAClB,WAAW,MAAMnB,MAAM,IAAItC,QAAQ,CAACY,QAAQ,CAAC,EAAE;MAC7C,MAAM8C,eAAe,GAAG,EAAE;MAC1B,KAAK,MAAM;QAAClB,SAAS;QAAER;MAAK,CAAC,IAAIM,MAAM,EAAE;QACvC,IAAIE,SAAS,KAAK,SAAS,EAAE;UAC3B,MAAMmB,IAAI,GAAG3B,KAAK;UAClB,IAAIa,SAAS,EAAE;YACb,QAAQc,IAAI,CAAC7C,IAAI;cACf,KAAK,MAAM;gBACTsC,MAAM,GAAG,IAAI;gBACbG,IAAI,GAAG,EAAE;gBACT;cACF,KAAK,WAAW;gBACdF,MAAM,GAAG,IAAI;gBACb;cAEF,KAAK,KAAK;gBACR,IAAID,MAAM,EAAE;kBACVG,IAAI,CAAC3B,IAAI,CAAC;oBACRgC,GAAG,EAAEC,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACF,GAAG,EAAE,EAAE,CAAC;oBACtCG,GAAG,EAAEF,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACC,GAAG,EAAE,EAAE,CAAC;oBACtCC,KAAK,EAAEC,UAAU,CAACN,IAAI,CAACG,UAAU,CAACE,KAAK,CAAC;oBACxCE,OAAO,EAAEL,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACK,KAAK,IAAI,GAAG,EAAE,EAAE;kBACpD,CAAC,CAAC;gBACJ;gBACA;cAEF,KAAK,KAAK;gBACR,IAAId,MAAM,EAAE;kBACV,MAAMe,CAAC,GAAGP,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACM,CAAC,EAAE,EAAE,CAAC;kBACzCZ,GAAG,GAAG,IAAInD,GAAG,CAAC,IAAI,EAAE+D,CAAC,CAAC;kBACtB,IAAIT,IAAI,CAACG,UAAU,CAACO,EAAE,EAAE;oBACtBb,GAAG,CAACc,MAAM,GAAGL,UAAU,CAACN,IAAI,CAACG,UAAU,CAACO,EAAE,CAAC;kBAC7C;kBACA,IAAIV,IAAI,CAACG,UAAU,CAACS,CAAC,EAAE;oBACrB,MAAML,OAAO,GAAGL,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACS,CAAC,EAAE,EAAE,CAAC;oBAC/C,MAAMJ,KAAK,GAAGjB,MAAM,CAACsB,aAAa,CAACN,OAAO,CAAC;oBAC3C,IAAIC,KAAK,EAAE;sBACTX,GAAG,CAACW,KAAK,GAAGA,KAAK;oBACnB;kBACF;gBACF;gBACA;cACF,KAAK,GAAG;gBACN,IAAIX,GAAG,EAAE;kBACPjC,CAAC,GAAG;oBACFkD,GAAG,EAAEd,IAAI,CAACG,UAAU,CAACM,CAAC;oBACtBG,CAAC,EAAEV,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACS,CAAC,EAAE,EAAE,CAAC;oBAClCG,CAAC,EAAEf,IAAI,CAACG,UAAU,CAACY;kBACrB,CAAC;gBACH;gBACA;cACF,KAAK,GAAG;gBACN,IAAInD,CAAC,EAAE;kBACLkC,OAAO,GAAGlC,CAAC,CAACY,CAAC,GAAG;oBAACwC,IAAI,EAAE;kBAAE,CAAC;gBAC5B;gBACA;cACF,KAAK,GAAG;gBACN,IAAIpD,CAAC,EAAE;kBACLkC,OAAO,GAAGlC,CAAC,CAACqD,CAAC,GAAG;oBAACD,IAAI,EAAE;kBAAE,CAAC;gBAC5B;gBACA;cACF,KAAK,IAAI;cACT,KAAK,GAAG;gBACN,IAAIpD,CAAC,EAAE;kBACLkC,OAAO,GAAGlC,CAAC,CAACqD,CAAC,GAAG;oBAACD,IAAI,EAAE;kBAAE,CAAC;gBAC5B;gBACA;cACF,KAAK,WAAW;gBACd;cACF;gBACE;YACJ;UACF;;UAEA;UACA;UACA,IAAI7B,cAAc,IAAIC,UAAU,EAAE;YAChC,QAAQY,IAAI,CAAC7C,IAAI;cACf,KAAK,YAAY;gBACfwC,YAAY,GAAG,IAAI;gBACnB;cACF,KAAK,WAAW;gBACd,IAAIA,YAAY,EAAE;kBAChB,MAAMuB,SAAS,GAAG;oBAChBJ,GAAG,EAAEd,IAAI,CAACG,UAAU,CAACW,GAAG;oBACxBK,GAAG,EAAEnB,IAAI,CAACG,UAAU,CAAC,MAAM;kBAC7B,CAAC;kBACD,IAAIhB,cAAc,EAAE;oBAClBY,eAAe,CAAC9B,IAAI,CAAC;sBAACY,SAAS,EAAE,WAAW;sBAAER,KAAK,EAAE6C;oBAAS,CAAC,CAAC;kBAClE,CAAC,MAAM;oBACL9B,UAAU,CAAC8B,SAAS,CAACJ,GAAG,CAAC,GAAGI,SAAS;kBACvC;gBACF;gBACA;cACF;gBACE;YACJ;UACF;QACF,CAAC,MAAM,IAAIrC,SAAS,KAAK,MAAM,EAAE;UAC/B;UACA,IAAIK,SAAS,EAAE;YACb,IAAIY,OAAO,EAAE;cACXA,OAAO,CAACkB,IAAI,IAAI3C,KAAK;YACvB;UACF;QACF,CAAC,MAAM,IAAIQ,SAAS,KAAK,UAAU,EAAE;UACnC,MAAMmB,IAAI,GAAG3B,KAAK;UAClB,IAAIa,SAAS,EAAE;YACb,QAAQc,IAAI,CAAC7C,IAAI;cACf,KAAK,MAAM;gBACTsC,MAAM,GAAG,KAAK;gBACd,IAAI,CAACrC,QAAQ,GAAGT,MAAM,CAACyE,SAAS,CAACxB,IAAI,CAAC;gBACtC;cACF,KAAK,WAAW;gBACdF,MAAM,GAAG,KAAK;gBACd;cAEF,KAAK,KAAK;gBACR,IAAI,CAACpC,WAAW,CAAC+D,SAAS,CAACxB,GAAG,CAAC;gBAC/BE,eAAe,CAAC9B,IAAI,CAAC;kBAACY,SAAS,EAAE,KAAK;kBAAER,KAAK,EAAEwB;gBAAG,CAAC,CAAC;gBACpDA,GAAG,GAAG,IAAI;gBACV;cAEF,KAAK,GAAG;gBACN,IAAIA,GAAG,IAAIjC,CAAC,EAAE;kBACZ,MAAM0D,OAAO,GAAG9E,QAAQ,CAAC+E,aAAa,CAAC3D,CAAC,CAACkD,GAAG,CAAC;kBAC7C,MAAMU,IAAI,GAAG3B,GAAG,CAAC4B,OAAO,CAACH,OAAO,CAACzD,GAAG,CAAC;kBACrC,IAAID,CAAC,CAACgD,CAAC,EAAE;oBACP,MAAMJ,KAAK,GAAGjB,MAAM,CAACsB,aAAa,CAACjD,CAAC,CAACgD,CAAC,CAAC;oBACvC,IAAIJ,KAAK,EAAE;sBACTgB,IAAI,CAAChB,KAAK,GAAGA,KAAK;oBACpB;kBACF;kBAEA,IAAI5C,CAAC,CAACY,CAAC,EAAE;oBACP,MAAMkD,SAAS,GAAG;sBAChBC,OAAO,EAAE/D,CAAC,CAACY,CAAC,CAACwC;oBACf,CAAC;oBACD,IAAIpD,CAAC,CAACqD,CAAC,EAAE;sBACP,IAAIrD,CAAC,CAACmD,CAAC,KAAK,KAAK,EAAE;wBACjBW,SAAS,CAACE,MAAM,GAAGrF,KAAK,CAACsF,SAAS,CAACjE,CAAC,CAACqD,CAAC,CAACD,IAAI,CAAC;sBAC9C,CAAC,MAAM;wBACLU,SAAS,CAACE,MAAM,GAAGtB,UAAU,CAAC1C,CAAC,CAACqD,CAAC,CAACD,IAAI,CAAC;sBACzC;oBACF;oBACAQ,IAAI,CAACnD,KAAK,GAAGqD,SAAS;kBACxB,CAAC,MAAM,IAAI9D,CAAC,CAACqD,CAAC,EAAE;oBACd,QAAQrD,CAAC,CAACmD,CAAC;sBACT,KAAK,GAAG;wBAAE;0BACR,MAAMe,KAAK,GAAG5B,QAAQ,CAACtC,CAAC,CAACqD,CAAC,CAACD,IAAI,EAAE,EAAE,CAAC;0BACpC,IAAI1B,aAAa,EAAE;4BACjBkC,IAAI,CAACnD,KAAK,GAAGiB,aAAa,CAACwC,KAAK,CAAC;0BACnC,CAAC,MAAM;4BACLN,IAAI,CAACnD,KAAK,GAAG;8BACX0D,YAAY,EAAED;4BAChB,CAAC;0BACH;0BACA;wBACF;sBAEA,KAAK,WAAW;sBAChB,KAAK,KAAK;wBACRN,IAAI,CAACnD,KAAK,GAAG9B,KAAK,CAACsF,SAAS,CAACjE,CAAC,CAACqD,CAAC,CAACD,IAAI,CAAC;wBACtC;sBAEF,KAAK,GAAG;wBACNQ,IAAI,CAACnD,KAAK,GAAG;0BAACU,KAAK,EAAEnB,CAAC,CAACqD,CAAC,CAACD;wBAAI,CAAC;wBAC9B;sBAEF,KAAK,GAAG;wBACNQ,IAAI,CAACnD,KAAK,GAAG6B,QAAQ,CAACtC,CAAC,CAACqD,CAAC,CAACD,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC;wBACzC;sBAEF;wBACE,IAAIzE,KAAK,CAACyF,SAAS,CAACR,IAAI,CAACS,MAAM,CAAC,EAAE;0BAChCT,IAAI,CAACnD,KAAK,GAAG9B,KAAK,CAAC2F,WAAW,CAC5B5B,UAAU,CAAC1C,CAAC,CAACqD,CAAC,CAACD,IAAI,CAAC,EACpBxB,UAAU,CAAC2C,KAAK,IAAI3C,UAAU,CAAC2C,KAAK,CAACC,QACvC,CAAC;wBACH,CAAC,MAAM;0BACLZ,IAAI,CAACnD,KAAK,GAAGiC,UAAU,CAAC1C,CAAC,CAACqD,CAAC,CAACD,IAAI,CAAC;wBACnC;wBACA;oBACJ;kBACF;kBACA,IAAI5B,UAAU,EAAE;oBACd,MAAM8B,SAAS,GAAG9B,UAAU,CAACxB,CAAC,CAACkD,GAAG,CAAC;oBACnC,IAAII,SAAS,EAAE;sBACbM,IAAI,CAACR,IAAI,GAAGQ,IAAI,CAACnD,KAAK;sBACtBmD,IAAI,CAACnD,KAAK,GAAGgE,SAAS;sBACtBb,IAAI,CAACN,SAAS,GAAGA,SAAS;oBAC5B;kBACF;kBACAtD,CAAC,GAAG,IAAI;gBACV;gBACA;cACF;gBACE;YACJ;UACF;UACA,IAAIuB,cAAc,IAAIC,UAAU,EAAE;YAChC,QAAQY,IAAI,CAAC7C,IAAI;cACf,KAAK,YAAY;gBACfwC,YAAY,GAAG,KAAK;gBACpB;cACF;gBACE;YACJ;UACF;QACF;MACF;MACA,IAAII,eAAe,CAAChC,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAMgC,eAAe;MACvB;IACF;EACF;AACF;AAEAuC,MAAM,CAACC,OAAO,GAAG3F,eAAe"}