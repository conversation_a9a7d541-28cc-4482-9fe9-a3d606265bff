const React = require('react');
const { Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const SettingsList = () => {
  const { hasPermission } = useAuth();
  
  // قائمة الإعدادات المتاحة
  const settings = [
    {
      id: 'general',
      title: 'الإعدادات العامة',
      description: 'إعدادات عامة للنظام مثل اسم المصنع وشعاره ومعلومات الاتصال',
      icon: 'fas fa-cog',
      color: 'primary',
      permission: 'settings_general',
      path: '/settings/general'
    },
    {
      id: 'users',
      title: 'إدارة المستخدمين',
      description: 'إضافة وتعديل وحذف المستخدمين وإدارة الصلاحيات',
      icon: 'fas fa-users-cog',
      color: 'danger',
      permission: 'settings_users',
      path: '/settings/users'
    },
    {
      id: 'pricing',
      title: 'إعدادات التسعير',
      description: 'إعدادات التسعير الافتراضية وهوامش الربح ورسوم العمال',
      icon: 'fas fa-dollar-sign',
      color: 'success',
      permission: 'settings_pricing',
      path: '/settings/pricing'
    },
    {
      id: 'notifications',
      title: 'إعدادات الإشعارات',
      description: 'إعدادات الإشعارات والتنبيهات للمخزون والطلبات والفواتير',
      icon: 'fas fa-bell',
      color: 'warning',
      permission: 'settings_notifications',
      path: '/settings/notifications'
    },
    {
      id: 'backup',
      title: 'النسخ الاحتياطي واستعادة البيانات',
      description: 'إنشاء واستعادة النسخ الاحتياطية لقاعدة البيانات',
      icon: 'fas fa-database',
      color: 'info',
      permission: 'settings_backup',
      path: '/settings/backup'
    },
    {
      id: 'invoice',
      title: 'إعدادات الفواتير',
      description: 'تخصيص قوالب الفواتير وإعدادات الطباعة',
      icon: 'fas fa-file-invoice',
      color: 'secondary',
      permission: 'settings_invoice',
      path: '/settings/invoice'
    }
  ];
  
  // تصفية الإعدادات حسب الصلاحيات
  const filteredSettings = settings.filter(setting => hasPermission(setting.permission));
  
  return (
    <div className="settings-list-page">
      <div className="page-header">
        <h2>الإعدادات</h2>
      </div>
      
      {filteredSettings.length === 0 ? (
        <div className="alert alert-warning">
          ليس لديك صلاحية للوصول إلى أي إعدادات. يرجى التواصل مع مدير النظام.
        </div>
      ) : (
        <div className="row">
          {filteredSettings.map(setting => (
            <div key={setting.id} className="col-md-6 col-lg-4 mb-4">
              <div className={`card setting-card border-${setting.color}`}>
                <div className={`card-header bg-${setting.color} text-white`}>
                  <h5 className="mb-0">
                    <i className={`${setting.icon} me-2`}></i>
                    {setting.title}
                  </h5>
                </div>
                <div className="card-body">
                  <p className="card-text">{setting.description}</p>
                  <Link to={setting.path} className={`btn btn-${setting.color}`}>
                    <i className="fas fa-external-link-alt me-1"></i>
                    فتح الإعدادات
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* معلومات النظام */}
      <div className="card mt-4">
        <div className="card-header">
          <h5>معلومات النظام</h5>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="system-info-item">
                <span className="info-label">اسم التطبيق:</span>
                <span className="info-value">H Group - نظام إدارة ورشة الأثاث</span>
              </div>
              
              <div className="system-info-item">
                <span className="info-label">الإصدار:</span>
                <span className="info-value">1.0.0</span>
              </div>
              
              <div className="system-info-item">
                <span className="info-label">تاريخ آخر تحديث:</span>
                <span className="info-value">{new Date().toLocaleDateString('ar-SA')}</span>
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="system-info-item">
                <span className="info-label">نظام التشغيل:</span>
                <span className="info-value" id="os-info">-</span>
              </div>
              
              <div className="system-info-item">
                <span className="info-label">حجم قاعدة البيانات:</span>
                <span className="info-value" id="db-size">-</span>
              </div>
              
              <div className="system-info-item">
                <span className="info-label">المساحة المتوفرة:</span>
                <span className="info-value" id="free-space">-</span>
              </div>
            </div>
          </div>
          
          <div className="mt-3">
            <button className="btn btn-primary" id="check-updates">
              <i className="fas fa-sync me-1"></i>
              التحقق من وجود تحديثات
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

module.exports = SettingsList;
