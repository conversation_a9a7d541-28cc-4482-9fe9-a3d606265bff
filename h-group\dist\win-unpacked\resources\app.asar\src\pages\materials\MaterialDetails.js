const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const MaterialDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [material, setMaterial] = useState(null);
  const [inventory, setInventory] = useState(null);
  const [usageHistory, setUsageHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تحميل بيانات المادة الخام
  useEffect(() => {
    const fetchMaterialData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل بيانات المادة الخام
        const materialData = await window.api.materials.getById(id);
        if (!materialData) {
          throw new Error('لم يتم العثور على المادة الخام');
        }
        setMaterial(materialData);

        // تحميل بيانات المخزون
        const inventoryData = await window.api.inventory.getByMaterialId(id);
        setInventory(inventoryData);

        // تحميل سجل استخدام المادة الخام
        const usageData = await window.api.orderMaterials.getByMaterialId(id);
        setUsageHistory(usageData);

      } catch (error) {
        console.error('خطأ في تحميل بيانات المادة الخام:', error);
        setError('حدث خطأ أثناء تحميل بيانات المادة الخام. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchMaterialData();
  }, [id]);

  // تعديل المادة الخام
  const handleEditMaterial = () => {
    if (!hasPermission('materials_edit')) {
      alert('ليس لديك صلاحية لتعديل المادة الخام');
      return;
    }

    navigate(`/materials/edit/${id}`);
  };

  // حذف المادة الخام
  const handleDeleteMaterial = async () => {
    if (!hasPermission('materials_delete')) {
      alert('ليس لديك صلاحية لحذف المادة الخام');
      return;
    }

    if (usageHistory.length > 0) {
      alert('لا يمكن حذف المادة الخام لأنها مستخدمة في طلبات. يجب حذف الطلبات أولاً أو إزالة المادة من الطلبات.');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذه المادة الخام؟ سيتم حذف سجل المخزون المرتبط بها أيضاً.')) {
      try {
        const result = await window.api.materials.delete(id);
        if (result.success) {
          alert('تم حذف المادة الخام بنجاح');
          navigate('/materials');
        } else {
          throw new Error('فشل في حذف المادة الخام');
        }
      } catch (error) {
        console.error('خطأ في حذف المادة الخام:', error);
        alert('حدث خطأ أثناء حذف المادة الخام. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  // تحديث المخزون
  const handleUpdateInventory = () => {
    if (!hasPermission('inventory_edit')) {
      alert('ليس لديك صلاحية لتحديث المخزون');
      return;
    }

    navigate(`/inventory/update/${id}`);
  };

  if (loading) {
    return <div className="loading">جاري تحميل بيانات المادة الخام...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (!material) {
    return <div className="alert alert-warning">لم يتم العثور على المادة الخام</div>;
  }

  return (
    <div className="material-details-page">
      <div className="page-header">
        <h2>تفاصيل المادة الخام</h2>
        <div className="page-actions">
          {hasPermission('materials_edit') && (
            <button className="btn btn-primary" onClick={handleEditMaterial}>
              <i className="fas fa-edit"></i> تعديل
            </button>
          )}

          {hasPermission('inventory_edit') && (
            <button className="btn btn-warning" onClick={handleUpdateInventory}>
              <i className="fas fa-boxes"></i> تحديث المخزون
            </button>
          )}

          {hasPermission('materials_delete') && (
            <button className="btn btn-danger" onClick={handleDeleteMaterial}>
              <i className="fas fa-trash"></i> حذف
            </button>
          )}

          <Link to="/materials" className="btn btn-secondary">
            <i className="fas fa-arrow-right"></i> العودة للقائمة
          </Link>
        </div>
      </div>

      {/* بيانات المادة الخام */}
      <div className="row">
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">معلومات المادة الخام</div>
            <div className="card-body">
              <div className="material-info-item">
                <span className="info-label">اسم المادة:</span>
                <span className="info-value">{material.name}</span>
              </div>

              <div className="material-info-item">
                <span className="info-label">وحدة القياس:</span>
                <span className="info-value">{material.unit || '-'}</span>
              </div>

              <div className="material-info-item">
                <span className="info-label">التكلفة لكل وحدة:</span>
                <span className="info-value">
                  {material.cost_per_unit ? `${material.cost_per_unit.toLocaleString()} ر.س` : '-'}
                </span>
              </div>

              <div className="material-info-item">
                <span className="info-label">الحد الأدنى للمخزون:</span>
                <span className="info-value">{material.min_quantity || 0}</span>
              </div>

              <div className="material-info-item">
                <span className="info-label">الوصف:</span>
                <span className="info-value">{material.description || '-'}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">معلومات المخزون</div>
            <div className="card-body">
              {inventory ? (
                <>
                  <div className="inventory-info-item">
                    <span className="info-label">الكمية المتوفرة:</span>
                    <span className="info-value">
                      <strong className={inventory.quantity < material.min_quantity ? 'text-danger' : 'text-success'}>
                        {inventory.quantity || 0} {material.unit}
                      </strong>
                    </span>
                  </div>

                  <div className="inventory-info-item">
                    <span className="info-label">حالة المخزون:</span>
                    <span className="info-value">
                      <span className={`badge ${inventory.quantity < material.min_quantity ? 'bg-danger' : 'bg-success'}`}>
                        {inventory.quantity < material.min_quantity ? 'منخفض' : 'متوفر'}
                      </span>
                    </span>
                  </div>

                  <div className="inventory-info-item">
                    <span className="info-label">آخر تحديث:</span>
                    <span className="info-value">
                      {inventory.last_updated ? new Date(inventory.last_updated).toLocaleString('ar-SA') : '-'}
                    </span>
                  </div>

                  {hasPermission('inventory_edit') && (
                    <div className="mt-3">
                      <button className="btn btn-sm btn-warning" onClick={handleUpdateInventory}>
                        <i className="fas fa-boxes"></i> تحديث المخزون
                      </button>
                    </div>
                  )}
                </>
              ) : (
                <div className="alert alert-warning">
                  لا توجد معلومات مخزون لهذه المادة.
                  {hasPermission('inventory_edit') && (
                    <div className="mt-2">
                      <button className="btn btn-sm btn-warning" onClick={handleUpdateInventory}>
                        <i className="fas fa-plus"></i> إضافة للمخزون
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* سجل استخدام المادة الخام */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>سجل استخدام المادة الخام</h5>
            <span className="badge bg-primary">{usageHistory.length} استخدام</span>
          </div>
        </div>
        <div className="card-body">
          {usageHistory.length === 0 ? (
            <div className="alert alert-info">لم يتم استخدام هذه المادة في أي طلب حتى الآن</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>تاريخ الطلب</th>
                    <th>الكمية المستخدمة</th>
                    <th>التكلفة لكل وحدة</th>
                    <th>التكلفة الإجمالية</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {usageHistory.map(usage => (
                    <tr key={usage.id}>
                      <td>{usage.order_number}</td>
                      <td>{new Date(usage.order_date).toLocaleDateString('ar-SA')}</td>
                      <td>{usage.quantity} {material.unit}</td>
                      <td>{usage.cost_per_unit.toLocaleString()} ر.س</td>
                      <td>{usage.total_cost.toLocaleString()} ر.س</td>
                      <td>
                        <Link to={`/orders/${usage.order_id}`} className="btn btn-sm btn-info">
                          <i className="fas fa-eye"></i> عرض الطلب
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = MaterialDetails;
