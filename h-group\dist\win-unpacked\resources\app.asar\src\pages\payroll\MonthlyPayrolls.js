const React = require('react');
const { useState, useEffect } = React;
const { formatCurrency, formatDate } = require('../../utils/formatters');

const MonthlyPayrolls = () => {
  const [payrolls, setPayrolls] = useState([]);
  const [filteredPayrolls, setFilteredPayrolls] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showDetails, setShowDetails] = useState(null);

  useEffect(() => {
    loadPayrolls();
  }, [selectedMonth, selectedYear]);

  useEffect(() => {
    filterPayrolls();
  }, [payrolls, searchTerm, statusFilter]);

  const loadPayrolls = async () => {
    try {
      setLoading(true);
      const data = await window.electronAPI.monthlyPayrolls.getByPeriod(selectedMonth, selectedYear);
      setPayrolls(data);
    } catch (error) {
      console.error('خطأ في تحميل كشوف المرتبات:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterPayrolls = () => {
    let filtered = payrolls;

    if (searchTerm) {
      filtered = filtered.filter(payroll =>
        payroll.worker_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payroll.employee_number?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(payroll => payroll.payment_status === statusFilter);
    }

    setFilteredPayrolls(filtered);
  };

  const getMonthName = (month) => {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month - 1];
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      'pending': { class: 'warning', text: 'معلق' },
      'paid': { class: 'success', text: 'مدفوع' },
      'partially_paid': { class: 'info', text: 'مدفوع جزئياً' }
    };
    const statusInfo = statusMap[status] || { class: 'secondary', text: status };
    return React.createElement('span', { 
      className: `status-badge ${statusInfo.class}` 
    }, statusInfo.text);
  };

  const calculateTotals = () => {
    return filteredPayrolls.reduce((totals, payroll) => ({
      totalGross: totals.totalGross + (payroll.gross_salary || 0),
      totalDeductions: totals.totalDeductions + (payroll.total_deductions || 0),
      totalNet: totals.totalNet + (payroll.net_salary || 0)
    }), { totalGross: 0, totalDeductions: 0, totalNet: 0 });
  };

  const handleExportToExcel = async () => {
    try {
      const dataToExport = filteredPayrolls.map(payroll => ({
        'اسم العامل': payroll.worker_name,
        'الرقم الوظيفي': payroll.employee_number || '-',
        'الشهر': getMonthName(payroll.payroll_month),
        'السنة': payroll.payroll_year,
        'الأمتار المنجزة': payroll.total_meters_worked || 0,
        'الساعات المنجزة': payroll.total_hours_worked || 0,
        'الراتب الأساسي': formatCurrency(payroll.base_salary || 0),
        'أجر الأمتار': formatCurrency(payroll.meter_earnings || 0),
        'أجر الوقت الإضافي': formatCurrency(payroll.overtime_earnings || 0),
        'المكافآت': formatCurrency(payroll.bonuses_total || 0),
        'الراتب الإجمالي': formatCurrency(payroll.gross_salary || 0),
        'خصم السلف': formatCurrency(payroll.advances_deduction || 0),
        'خصومات أخرى': formatCurrency(payroll.other_deductions || 0),
        'خصم التأمين': formatCurrency(payroll.insurance_deduction || 0),
        'خصم الضريبة': formatCurrency(payroll.tax_deduction || 0),
        'إجمالي الخصومات': formatCurrency(payroll.total_deductions || 0),
        'صافي الراتب': formatCurrency(payroll.net_salary || 0),
        'حالة الدفع': getStatusBadge(payroll.payment_status).props.children
      }));

      const result = await window.electronAPI.exportToExcel(
        dataToExport, 
        `كشوف_المرتبات_${getMonthName(selectedMonth)}_${selectedYear}`
      );

      if (result.success) {
        alert(`تم تصدير كشوف المرتبات بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير كشوف المرتبات');
      }
    } catch (error) {
      console.error('خطأ في تصدير كشوف المرتبات:', error);
      alert('حدث خطأ أثناء تصدير كشوف المرتبات');
    }
  };

  const totals = calculateTotals();

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل كشوف المرتبات...')
    );
  }

  return React.createElement('div', { className: 'monthly-payrolls professional-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header modern-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-file-invoice-dollar' }),
          ' كشوف المرتبات الشهرية'
        ),
        React.createElement('p', { className: 'page-subtitle' }, 
          `${getMonthName(selectedMonth)} ${selectedYear} - ${filteredPayrolls.length} كشف مرتب`
        )
      ),
      React.createElement('div', { className: 'page-actions' },
        React.createElement('div', { className: 'period-selector' },
          React.createElement('select', {
            value: selectedMonth,
            onChange: (e) => setSelectedMonth(parseInt(e.target.value)),
            className: 'form-select'
          },
            Array.from({ length: 12 }, (_, i) => 
              React.createElement('option', { key: i + 1, value: i + 1 }, getMonthName(i + 1))
            )
          ),
          React.createElement('select', {
            value: selectedYear,
            onChange: (e) => setSelectedYear(parseInt(e.target.value)),
            className: 'form-select'
          },
            Array.from({ length: 5 }, (_, i) => {
              const year = new Date().getFullYear() - 2 + i;
              return React.createElement('option', { key: year, value: year }, year);
            })
          )
        ),
        React.createElement('button', {
          className: 'btn btn-success modern-btn',
          onClick: handleExportToExcel
        },
          React.createElement('i', { className: 'fas fa-file-excel' }),
          ' تصدير إلى Excel'
        )
      )
    ),

    // إحصائيات سريعة
    React.createElement('div', { className: 'stats-grid' },
      React.createElement('div', { className: 'stat-card success' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-money-bill-wave' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي الرواتب'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(totals.totalGross))
        )
      ),
      React.createElement('div', { className: 'stat-card warning' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-minus-circle' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي الخصومات'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(totals.totalDeductions))
        )
      ),
      React.createElement('div', { className: 'stat-card info' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-hand-holding-usd' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'صافي المرتبات'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(totals.totalNet))
        )
      )
    ),

    // الفلاتر
    React.createElement('div', { className: 'filters-section' },
      React.createElement('div', { className: 'row' },
        React.createElement('div', { className: 'col-md-6' },
          React.createElement('div', { className: 'search-box' },
            React.createElement('i', { className: 'fas fa-search' }),
            React.createElement('input', {
              type: 'text',
              placeholder: 'البحث في كشوف المرتبات...',
              value: searchTerm,
              onChange: (e) => setSearchTerm(e.target.value),
              className: 'form-control'
            })
          )
        ),
        React.createElement('div', { className: 'col-md-3' },
          React.createElement('select', {
            value: statusFilter,
            onChange: (e) => setStatusFilter(e.target.value),
            className: 'form-select'
          },
            React.createElement('option', { value: 'all' }, 'جميع الحالات'),
            React.createElement('option', { value: 'pending' }, 'معلق'),
            React.createElement('option', { value: 'paid' }, 'مدفوع'),
            React.createElement('option', { value: 'partially_paid' }, 'مدفوع جزئياً')
          )
        )
      )
    ),

    // قائمة كشوف المرتبات
    React.createElement('div', { className: 'modern-card glass-card' },
      React.createElement('div', { className: 'card-header modern-card-header' },
        React.createElement('h3', { className: 'card-title' },
          React.createElement('i', { className: 'fas fa-list' }),
          ' كشوف المرتبات'
        )
      ),
      React.createElement('div', { className: 'card-body' },
        filteredPayrolls.length === 0 
          ? React.createElement('div', { className: 'empty-state' },
              React.createElement('i', { className: 'fas fa-file-invoice-dollar' }),
              React.createElement('h3', null, 'لا توجد كشوف مرتبات'),
              React.createElement('p', null, 'لم يتم العثور على كشوف مرتبات للفترة المحددة')
            )
          : React.createElement('div', { className: 'table-responsive' },
              React.createElement('table', { className: 'modern-table professional-table' },
                React.createElement('thead', null,
                  React.createElement('tr', null,
                    React.createElement('th', null, 'العامل'),
                    React.createElement('th', null, 'الأمتار'),
                    React.createElement('th', null, 'الراتب الإجمالي'),
                    React.createElement('th', null, 'الخصومات'),
                    React.createElement('th', null, 'صافي الراتب'),
                    React.createElement('th', null, 'الحالة'),
                    React.createElement('th', null, 'الإجراءات')
                  )
                ),
                React.createElement('tbody', null,
                  filteredPayrolls.map(payroll =>
                    React.createElement('tr', { key: payroll.id },
                      React.createElement('td', null,
                        React.createElement('div', { className: 'worker-info' },
                          React.createElement('strong', null, payroll.worker_name),
                          payroll.employee_number && React.createElement('small', null, 
                            React.createElement('br'),
                            `رقم: ${payroll.employee_number}`
                          )
                        )
                      ),
                      React.createElement('td', null, 
                        React.createElement('div', { className: 'text-center' },
                          React.createElement('strong', null, payroll.total_meters_worked || 0),
                          React.createElement('small', { className: 'd-block' }, 'متر مربع')
                        )
                      ),
                      React.createElement('td', null, formatCurrency(payroll.gross_salary || 0)),
                      React.createElement('td', null, formatCurrency(payroll.total_deductions || 0)),
                      React.createElement('td', null,
                        React.createElement('strong', { className: 'text-success' }, 
                          formatCurrency(payroll.net_salary || 0)
                        )
                      ),
                      React.createElement('td', null, getStatusBadge(payroll.payment_status)),
                      React.createElement('td', null,
                        React.createElement('div', { className: 'action-buttons' },
                          React.createElement('button', {
                            className: 'action-btn info-btn',
                            onClick: () => setShowDetails(payroll),
                            title: 'عرض التفاصيل'
                          },
                            React.createElement('i', { className: 'fas fa-eye' })
                          ),
                          React.createElement('button', {
                            className: 'action-btn success-btn',
                            title: 'طباعة كشف المرتب'
                          },
                            React.createElement('i', { className: 'fas fa-print' })
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
      )
    ),

    // نافذة تفاصيل كشف المرتب
    showDetails && React.createElement('div', { className: 'modal-overlay' },
      React.createElement('div', { className: 'modal-content large' },
        React.createElement('div', { className: 'modal-header' },
          React.createElement('h3', null, `كشف مرتب - ${showDetails.worker_name}`),
          React.createElement('button', {
            className: 'close-btn',
            onClick: () => setShowDetails(null)
          }, '×')
        ),
        React.createElement('div', { className: 'modal-body' },
          React.createElement('div', { className: 'payroll-details' },
            React.createElement('div', { className: 'row' },
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('h4', null, 'معلومات العامل'),
                React.createElement('table', { className: 'details-table' },
                  React.createElement('tr', null,
                    React.createElement('td', null, 'الاسم:'),
                    React.createElement('td', null, showDetails.worker_name)
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'الرقم الوظيفي:'),
                    React.createElement('td', null, showDetails.employee_number || '-')
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'الشهر:'),
                    React.createElement('td', null, `${getMonthName(showDetails.payroll_month)} ${showDetails.payroll_year}`)
                  )
                )
              ),
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('h4', null, 'تفاصيل العمل'),
                React.createElement('table', { className: 'details-table' },
                  React.createElement('tr', null,
                    React.createElement('td', null, 'الأمتار المنجزة:'),
                    React.createElement('td', null, `${showDetails.total_meters_worked || 0} متر مربع`)
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'الساعات المنجزة:'),
                    React.createElement('td', null, `${showDetails.total_hours_worked || 0} ساعة`)
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'الساعات الإضافية:'),
                    React.createElement('td', null, `${showDetails.overtime_hours || 0} ساعة`)
                  )
                )
              )
            ),
            React.createElement('div', { className: 'row mt-4' },
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('h4', null, 'تفاصيل الراتب'),
                React.createElement('table', { className: 'details-table' },
                  React.createElement('tr', null,
                    React.createElement('td', null, 'الراتب الأساسي:'),
                    React.createElement('td', null, formatCurrency(showDetails.base_salary || 0))
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'أجر الأمتار:'),
                    React.createElement('td', null, formatCurrency(showDetails.meter_earnings || 0))
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'أجر الوقت الإضافي:'),
                    React.createElement('td', null, formatCurrency(showDetails.overtime_earnings || 0))
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'المكافآت:'),
                    React.createElement('td', null, formatCurrency(showDetails.bonuses_total || 0))
                  ),
                  React.createElement('tr', { className: 'total-row' },
                    React.createElement('td', null, React.createElement('strong', null, 'الراتب الإجمالي:')),
                    React.createElement('td', null, React.createElement('strong', null, formatCurrency(showDetails.gross_salary || 0)))
                  )
                )
              ),
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('h4', null, 'الخصومات'),
                React.createElement('table', { className: 'details-table' },
                  React.createElement('tr', null,
                    React.createElement('td', null, 'خصم السلف:'),
                    React.createElement('td', null, formatCurrency(showDetails.advances_deduction || 0))
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'خصومات أخرى:'),
                    React.createElement('td', null, formatCurrency(showDetails.other_deductions || 0))
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'خصم التأمين:'),
                    React.createElement('td', null, formatCurrency(showDetails.insurance_deduction || 0))
                  ),
                  React.createElement('tr', null,
                    React.createElement('td', null, 'خصم الضريبة:'),
                    React.createElement('td', null, formatCurrency(showDetails.tax_deduction || 0))
                  ),
                  React.createElement('tr', { className: 'total-row' },
                    React.createElement('td', null, React.createElement('strong', null, 'إجمالي الخصومات:')),
                    React.createElement('td', null, React.createElement('strong', null, formatCurrency(showDetails.total_deductions || 0)))
                  )
                )
              )
            ),
            React.createElement('div', { className: 'final-amount' },
              React.createElement('h3', null, 'صافي الراتب المستحق: ', 
                React.createElement('span', { className: 'amount' }, formatCurrency(showDetails.net_salary || 0))
              )
            )
          )
        ),
        React.createElement('div', { className: 'modal-footer' },
          React.createElement('button', {
            className: 'btn btn-primary',
            onClick: () => window.print()
          }, 'طباعة'),
          React.createElement('button', {
            className: 'btn btn-secondary',
            onClick: () => setShowDetails(null)
          }, 'إغلاق')
        )
      )
    )
  );
};

module.exports = MonthlyPayrolls;
