const React = require('react');
const { createContext, useContext, useState, useEffect } = React;

// إنشاء سياق الإشعارات
const NotificationsContext = createContext();

// مزود سياق الإشعارات
const NotificationsProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // تحميل الإشعارات عند بدء التطبيق
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        // تحميل الإشعارات من قاعدة البيانات
        const data = await window.api.notifications.getAll();
        setNotifications(data);

        // حساب عدد الإشعارات غير المقروءة
        const unread = data.filter(notification => !notification.read).length;
        setUnreadCount(unread);
      } catch (error) {
        console.error('خطأ في تحميل الإشعارات:', error);
      }
    };

    fetchNotifications();

    // تحديث الإشعارات كل دقيقة
    const intervalId = setInterval(fetchNotifications, 60000);

    return () => clearInterval(intervalId);
  }, []);

  // إضافة إشعار جديد
  const addNotification = async (notification) => {
    try {
      // إضافة الإشعار إلى قاعدة البيانات
      const result = await window.api.notifications.create(notification);

      // تحديث قائمة الإشعارات
      setNotifications(prev => [result, ...prev]);
      setUnreadCount(prev => prev + 1);

      return result;
    } catch (error) {
      console.error('خطأ في إضافة إشعار جديد:', error);
      throw error;
    }
  };

  // تعليم إشعار كمقروء
  const markAsRead = async (id) => {
    try {
      // تعليم الإشعار كمقروء في قاعدة البيانات
      await window.api.notifications.markAsRead(id);

      // تحديث قائمة الإشعارات
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id
            ? { ...notification, read: true }
            : notification
        )
      );

      // تحديث عدد الإشعارات غير المقروءة
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('خطأ في تعليم الإشعار كمقروء:', error);
      throw error;
    }
  };

  // تعليم جميع الإشعارات كمقروءة
  const markAllAsRead = async () => {
    try {
      // تعليم جميع الإشعارات كمقروءة في قاعدة البيانات
      await window.api.notifications.markAllAsRead();

      // تحديث قائمة الإشعارات
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      // تحديث عدد الإشعارات غير المقروءة
      setUnreadCount(0);
    } catch (error) {
      console.error('خطأ في تعليم جميع الإشعارات كمقروءة:', error);
      throw error;
    }
  };

  // حذف إشعار
  const deleteNotification = async (id) => {
    try {
      // حذف الإشعار من قاعدة البيانات
      await window.api.notifications.delete(id);

      // تحديث قائمة الإشعارات
      const notification = notifications.find(n => n.id === id);
      setNotifications(prev => prev.filter(n => n.id !== id));

      // تحديث عدد الإشعارات غير المقروءة إذا كان الإشعار غير مقروء
      if (notification && !notification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('خطأ في حذف الإشعار:', error);
      throw error;
    }
  };

  // التحقق من الأقساط المستحقة
  const checkDueInstallments = async () => {
    try {
      // الحصول على الأقساط المستحقة اليوم
      const dueInstallments = await window.api.installments.getDueInstallments();

      // إنشاء إشعارات للأقساط المستحقة
      for (const installment of dueInstallments) {
        await addNotification({
          type: 'installment_due',
          title: 'قسط مستحق اليوم',
          message: `القسط رقم ${installment.installment_number} للفاتورة ${installment.invoice_number} (${installment.customer_name}) مستحق اليوم`,
          link: `/installments/due`,
          read: false,
          created_at: new Date().toISOString()
        });
      }

      // الحصول على الأقساط المتأخرة
      const lateInstallments = await window.api.installments.getLateInstallments();

      // إنشاء إشعارات للأقساط المتأخرة
      for (const installment of lateInstallments) {
        const daysLate = Math.floor((new Date() - new Date(installment.due_date)) / (1000 * 60 * 60 * 24));

        await addNotification({
          type: 'installment_late',
          title: 'قسط متأخر',
          message: `القسط رقم ${installment.installment_number} للفاتورة ${installment.invoice_number} (${installment.customer_name}) متأخر بـ ${daysLate} يوم`,
          link: `/installments/due`,
          read: false,
          created_at: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('خطأ في التحقق من الأقساط المستحقة:', error);
    }
  };

  // التحقق من المخزون المنخفض
  const checkLowInventory = async () => {
    try {
      // الحصول على المواد ذات المخزون المنخفض
      const lowInventoryItems = await window.api.inventory.getLowInventoryItems();

      // إنشاء إشعارات للمواد ذات المخزون المنخفض
      for (const item of lowInventoryItems) {
        await addNotification({
          type: 'low_inventory',
          title: 'مخزون منخفض',
          message: `المادة ${item.name} وصلت إلى مستوى منخفض (${item.quantity} ${item.unit})`,
          link: `/inventory`,
          read: false,
          created_at: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('خطأ في التحقق من المخزون المنخفض:', error);
    }
  };

  // التحقق من الطلبات المتأخرة
  const checkLateOrders = async () => {
    try {
      // الحصول على الطلبات المتأخرة
      const lateOrders = await window.api.orders.getLateOrders();

      // إنشاء إشعارات للطلبات المتأخرة
      for (const order of lateOrders) {
        const daysLate = Math.floor((new Date() - new Date(order.delivery_date)) / (1000 * 60 * 60 * 24));

        await addNotification({
          type: 'order_late',
          title: 'طلب متأخر',
          message: `الطلب رقم ${order.order_number} (${order.customer_name}) متأخر بـ ${daysLate} يوم عن موعد التسليم`,
          link: `/orders/${order.id}`,
          read: false,
          created_at: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('خطأ في التحقق من الطلبات المتأخرة:', error);
    }
  };

  // تشغيل فحص الإشعارات عند بدء التطبيق
  useEffect(() => {
    const checkNotifications = async () => {
      await checkDueInstallments();
      await checkLowInventory();
      await checkLateOrders();
    };

    checkNotifications();

    // تشغيل فحص الإشعارات كل ساعة
    const intervalId = setInterval(checkNotifications, 3600000);

    return () => clearInterval(intervalId);
  }, []);

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    checkDueInstallments,
    checkLowInventory,
    checkLateOrders
  };

  return React.createElement(NotificationsContext.Provider, { value }, children);
};

// هوك استخدام سياق الإشعارات
const useNotifications = () => {
  const context = useContext(NotificationsContext);

  if (!context) {
    throw new Error('useNotifications يجب استخدامه داخل NotificationsProvider');
  }

  return context;
};

module.exports = {
  NotificationsContext,
  NotificationsProvider,
  useNotifications
};
