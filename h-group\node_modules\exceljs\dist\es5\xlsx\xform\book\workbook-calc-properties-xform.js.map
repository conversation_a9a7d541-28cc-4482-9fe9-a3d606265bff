{"version": 3, "file": "workbook-calc-properties-xform.js", "names": ["BaseXform", "require", "WorkbookCalcPropertiesXform", "render", "xmlStream", "model", "leafNode", "calcId", "fullCalcOnLoad", "undefined", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/book/workbook-calc-properties-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass WorkbookCalcPropertiesXform extends BaseXform {\n  render(xmlStream, model) {\n    xmlStream.leafNode('calcPr', {\n      calcId: 171027,\n      fullCalcOnLoad: model.fullCalcOnLoad ? 1 : undefined,\n    });\n  }\n\n  parseOpen(node) {\n    if (node.name === 'calcPr') {\n      this.model = {};\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = WorkbookCalcPropertiesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,2BAA2B,SAASF,SAAS,CAAC;EAClDG,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,QAAQ,EAAE;MAC3BC,MAAM,EAAE,MAAM;MACdC,cAAc,EAAEH,KAAK,CAACG,cAAc,GAAG,CAAC,GAAGC;IAC7C,CAAC,CAAC;EACJ;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACP,KAAK,GAAG,CAAC,CAAC;MACf,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAQ,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGd,2BAA2B"}