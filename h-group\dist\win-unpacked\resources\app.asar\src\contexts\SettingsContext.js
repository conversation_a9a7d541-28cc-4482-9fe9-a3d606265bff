const React = require('react');
const { createContext, useState, useEffect, useContext } = React;

// إنشاء سياق الإعدادات
const SettingsContext = createContext();

// الإعدادات الافتراضية
const defaultSettings = {
  theme: 'light',
  language: 'ar',
  currency: 'SAR',
  defaultWorkerFee: 0,
  defaultFactoryFee: 0,
  defaultDesignerFee: 0,
  defaultOwnerMargin: 0,
  showLowStockNotifications: true,
  showDueInvoicesNotifications: true,
  backupReminders: true,
  backupFrequency: 'weekly',
  lastBackupDate: null
};

// مزود سياق الإعدادات
const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تحميل الإعدادات من التخزين عند بدء التطبيق
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedSettings = await window.api.settings.getAll();
        if (storedSettings) {
          setSettings({ ...defaultSettings, ...storedSettings });
        }
      } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  // تحديث إعداد معين
  const updateSetting = async (key, value) => {
    try {
      setLoading(true);
      setError(null);
      await window.api.settings.set(key, value);
      setSettings(prevSettings => ({ ...prevSettings, [key]: value }));
    } catch (error) {
      console.error(`خطأ في تحديث الإعداد "${key}":`, error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // تحديث مجموعة من الإعدادات
  const updateSettings = async (newSettings) => {
    try {
      setLoading(true);
      setError(null);

      // تحديث كل إعداد على حدة
      for (const [key, value] of Object.entries(newSettings)) {
        await window.api.settings.set(key, value);
      }

      setSettings(prevSettings => ({ ...prevSettings, ...newSettings }));
    } catch (error) {
      console.error('خطأ في تحديث الإعدادات:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // إنشاء نسخة احتياطية
  const createBackup = async (path) => {
    try {
      setLoading(true);
      setError(null);
      await window.api.settings.backup(path);

      // تحديث تاريخ آخر نسخة احتياطية
      const lastBackupDate = new Date().toISOString();
      await window.api.settings.set('lastBackupDate', lastBackupDate);
      setSettings(prevSettings => ({ ...prevSettings, lastBackupDate }));

      return { success: true, path };
    } catch (error) {
      console.error('خطأ في إنشاء نسخة احتياطية:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // استعادة نسخة احتياطية
  const restoreBackup = async (path) => {
    try {
      setLoading(true);
      setError(null);
      await window.api.settings.restore(path);

      // إعادة تحميل الإعدادات بعد الاستعادة
      const storedSettings = await window.api.settings.getAll();
      if (storedSettings) {
        setSettings({ ...defaultSettings, ...storedSettings });
      }

      return { success: true, path };
    } catch (error) {
      console.error('خطأ في استعادة نسخة احتياطية:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    settings,
    loading,
    error,
    updateSetting,
    updateSettings,
    createBackup,
    restoreBackup
  };

  return React.createElement(SettingsContext.Provider, { value }, children);
};

// هوك استخدام سياق الإعدادات
const useSettings = () => {
  return useContext(SettingsContext);
};

module.exports = {
  SettingsContext,
  SettingsProvider,
  useSettings
};
