const React = require('react');
const { useState, useRef, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useNotifications } = require('../../contexts/NotificationsContext');

const NotificationsMenu = () => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification
  } = useNotifications();

  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);
  const navigate = useNavigate();

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // تبديل حالة القائمة
  const toggleMenu = () => {
    setIsOpen(prev => !prev);
  };

  // التعامل مع النقر على إشعار
  const handleNotificationClick = async (notification) => {
    try {
      // تعليم الإشعار كمقروء
      if (!notification.read) {
        await markAsRead(notification.id);
      }

      // الانتقال إلى الرابط المرتبط بالإشعار
      if (notification.link) {
        navigate(notification.link);
      }

      // إغلاق القائمة
      setIsOpen(false);
    } catch (error) {
      console.error('خطأ في التعامل مع النقر على الإشعار:', error);
    }
  };

  // تعليم جميع الإشعارات كمقروءة
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('خطأ في تعليم جميع الإشعارات كمقروءة:', error);
    }
  };

  // حذف إشعار
  const handleDeleteNotification = async (event, id) => {
    try {
      // منع انتشار الحدث
      event.stopPropagation();

      await deleteNotification(id);
    } catch (error) {
      console.error('خطأ في حذف الإشعار:', error);
    }
  };

  // الحصول على أيقونة الإشعار بناءً على نوعه
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'installment_due':
        return React.createElement('i', { className: 'fas fa-calendar-day text-warning' });
      case 'installment_late':
        return React.createElement('i', { className: 'fas fa-calendar-times text-danger' });
      case 'low_inventory':
        return React.createElement('i', { className: 'fas fa-boxes text-warning' });
      case 'order_late':
        return React.createElement('i', { className: 'fas fa-truck-loading text-danger' });
      default:
        return React.createElement('i', { className: 'fas fa-bell text-primary' });
    }
  };

  return React.createElement('div', { className: 'notifications-menu', ref: menuRef },
    React.createElement('button', {
      className: 'notifications-toggle',
      onClick: toggleMenu
    },
      React.createElement('i', { className: 'fas fa-bell' }),
      unreadCount > 0 && React.createElement('span', { className: 'notifications-badge' }, unreadCount)
    ),
    isOpen && React.createElement('div', { className: 'notifications-dropdown' },
      React.createElement('div', { className: 'notifications-header' },
        React.createElement('h5', null, 'الإشعارات'),
        notifications.length > 0 && React.createElement('button', {
          className: 'mark-all-read',
          onClick: handleMarkAllAsRead
        }, 'تعليم الكل كمقروء')
      ),
      React.createElement('div', { className: 'notifications-list' },
        notifications.length === 0
          ? React.createElement('div', { className: 'no-notifications' }, 'لا توجد إشعارات')
          : notifications.map(notification =>
              React.createElement('div', {
                key: notification.id,
                className: `notification-item ${!notification.read ? 'unread' : ''}`,
                onClick: () => handleNotificationClick(notification)
              },
                React.createElement('div', { className: 'notification-content' },
                  React.createElement('div', { className: 'notification-title' }, notification.title),
                  React.createElement('div', { className: 'notification-message' }, notification.message)
                )
              )
            )
      )
    )
  );
};

module.exports = NotificationsMenu;
