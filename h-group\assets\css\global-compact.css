/* تحسينات شاملة لجميع صفحات التطبيق */

/* إعادة تعريف البطاقات العامة */
.card,
.stat-card,
.info-card,
.data-card,
.content-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 0.75rem !important;
}

.card:hover,
.stat-card:hover,
.info-card:hover,
.data-card:hover,
.content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
  border-color: #1e3c72;
}

/* رؤوس البطاقات */
.card-header,
.card-title,
h1, h2, h3, h4, h5, h6 {
  font-size: 0.9rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  padding-bottom: 0.4rem !important;
}

/* نصوص البطاقات */
.card-text,
.card-body p,
.card-content,
.description {
  font-size: 0.75rem !important;
  line-height: 1.4 !important;
  margin-bottom: 0.4rem !important;
}

/* القيم والأرقام */
.value,
.amount,
.price,
.quantity,
.count,
.number {
  font-size: 1.1rem !important;
  font-weight: 700 !important;
  color: #2c3e50 !important;
}

/* الأيقونات */
.fa, .fas, .far, .fab,
i[class*="fa-"] {
  font-size: 0.8rem !important;
}

/* الأزرار المدمجة */
.btn,
.button,
.action-btn {
  padding: 0.3rem 0.6rem !important;
  font-size: 0.7rem !important;
  border-radius: 6px !important;
  margin: 0.2rem !important;
}

.btn-sm,
.btn-small {
  padding: 0.2rem 0.4rem !important;
  font-size: 0.65rem !important;
}

.btn-lg,
.btn-large {
  padding: 0.4rem 0.8rem !important;
  font-size: 0.8rem !important;
}

/* الشارات */
.badge,
.label,
.tag,
.status {
  font-size: 0.6rem !important;
  padding: 0.15rem 0.4rem !important;
  border-radius: 4px !important;
}

/* الجداول */
.table,
.data-table,
.list-table {
  font-size: 0.7rem !important;
}

.table th,
.table td,
.data-table th,
.data-table td {
  padding: 0.4rem !important;
  font-size: 0.7rem !important;
}

.table th {
  font-size: 0.65rem !important;
  font-weight: 600 !important;
}

/* النماذج */
.form-group,
.input-group,
.field-group {
  margin-bottom: 0.75rem !important;
}

.form-control,
.input,
.select,
.textarea {
  padding: 0.4rem 0.6rem !important;
  font-size: 0.75rem !important;
  border-radius: 6px !important;
}

.form-label,
.input-label,
label {
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.3rem !important;
}

/* الشبكات المحسنة */
.row {
  margin: 0 !important;
  gap: 0.75rem !important;
}

.col,
.column,
[class*="col-"] {
  padding: 0 !important;
}

/* قوائم البيانات */
.list-group,
.data-list,
.items-list {
  margin-bottom: 0.75rem !important;
}

.list-group-item,
.list-item,
.data-item {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.75rem !important;
  border-width: 1px !important;
}

/* المودال والنوافذ المنبثقة */
.modal-content,
.popup-content,
.dialog-content {
  padding: 1rem !important;
  border-radius: 8px !important;
}

.modal-header,
.popup-header,
.dialog-header {
  padding: 0.75rem 1rem !important;
  margin-bottom: 0.5rem !important;
}

.modal-title,
.popup-title,
.dialog-title {
  font-size: 1rem !important;
  font-weight: 600 !important;
}

.modal-body,
.popup-body,
.dialog-body {
  padding: 0.5rem 1rem !important;
  font-size: 0.75rem !important;
}

.modal-footer,
.popup-footer,
.dialog-footer {
  padding: 0.5rem 1rem !important;
  gap: 0.5rem !important;
}

/* التنبيهات */
.alert,
.notification,
.message {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.75rem !important;
  border-radius: 6px !important;
  margin-bottom: 0.75rem !important;
}

/* التقدم والمؤشرات */
.progress {
  height: 6px !important;
  border-radius: 3px !important;
}

.progress-bar {
  font-size: 0.6rem !important;
}

/* التبويبات */
.nav-tabs,
.tabs,
.tab-list {
  margin-bottom: 0.75rem !important;
}

.nav-link,
.tab,
.tab-button {
  padding: 0.4rem 0.8rem !important;
  font-size: 0.75rem !important;
  border-radius: 6px 6px 0 0 !important;
}

/* البحث والفلاتر */
.search-box,
.filter-box,
.search-container {
  margin-bottom: 0.75rem !important;
}

.search-input,
.filter-input {
  padding: 0.4rem 0.6rem !important;
  font-size: 0.75rem !important;
  border-radius: 6px !important;
}

/* الصفحات والتنقل */
.pagination,
.pager {
  margin: 0.75rem 0 !important;
}

.page-link,
.pager-link {
  padding: 0.3rem 0.6rem !important;
  font-size: 0.7rem !important;
}

/* الشاشات الصغيرة */
@media (max-width: 768px) {
  .card,
  .stat-card,
  .info-card,
  .data-card,
  .content-card {
    padding: 0.6rem !important;
    margin-bottom: 0.6rem !important;
  }
  
  .card-header,
  .card-title,
  h1, h2, h3, h4, h5, h6 {
    font-size: 0.8rem !important;
  }
  
  .card-text,
  .card-body p,
  .card-content {
    font-size: 0.7rem !important;
  }
  
  .value,
  .amount,
  .price,
  .quantity,
  .count,
  .number {
    font-size: 1rem !important;
  }
  
  .btn,
  .button,
  .action-btn {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.65rem !important;
  }
}

@media (max-width: 480px) {
  .card,
  .stat-card,
  .info-card,
  .data-card,
  .content-card {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  
  .card-header,
  .card-title,
  h1, h2, h3, h4, h5, h6 {
    font-size: 0.75rem !important;
  }
  
  .card-text,
  .card-body p,
  .card-content {
    font-size: 0.65rem !important;
  }
  
  .value,
  .amount,
  .price,
  .quantity,
  .count,
  .number {
    font-size: 0.9rem !important;
  }
  
  .btn,
  .button,
  .action-btn {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.6rem !important;
  }
  
  .table th,
  .table td {
    padding: 0.3rem 0.2rem !important;
    font-size: 0.65rem !important;
  }
}
