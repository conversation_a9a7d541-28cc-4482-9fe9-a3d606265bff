const React = require('react');
const { useState, useEffect } = React;

const ProductionReports = () => {
  const [reportData, setReportData] = useState({
    overview: {
      totalOrders: 0,
      completedOrders: 0,
      inProgressOrders: 0,
      averageCompletionTime: 0,
      totalProductionHours: 0,
      efficiency: 0
    },
    stageAnalysis: [],
    workerPerformance: [],
    materialUsage: [],
    qualityMetrics: {
      totalChecks: 0,
      passedChecks: 0,
      failedChecks: 0,
      defectsCount: 0,
      averageQualityScore: 0
    },
    timeAnalysis: [],
    costAnalysis: {
      totalMaterialCost: 0,
      totalLaborCost: 0,
      totalOverhead: 0,
      averageCostPerOrder: 0
    }
  });

  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadReportData();
  }, [dateRange]);

  const loadReportData = async () => {
    try {
      setLoading(true);

      // تحميل جميع الطلبات في النطاق الزمني
      const allOrders = await window.electronAPI.orders.getAll();
      const filteredOrders = allOrders.filter(order => {
        const orderDate = new Date(order.created_at);
        const startDate = new Date(dateRange.startDate);
        const endDate = new Date(dateRange.endDate);
        return orderDate >= startDate && orderDate <= endDate;
      });

      // حساب الإحصائيات العامة
      const completedOrders = filteredOrders.filter(o => o.status === 'مكتمل');
      const inProgressOrders = filteredOrders.filter(o => o.status === 'قيد التنفيذ');

      // تحميل مراحل الإنتاج
      let allStages = [];
      let totalProductionHours = 0;
      let totalEstimatedHours = 0;

      for (const order of filteredOrders) {
        try {
          const stages = await window.electronAPI.productionStages.getByOrderId(order.id);
          allStages.push(...stages.map(stage => ({ ...stage, order_id: order.id })));
          
          stages.forEach(stage => {
            if (stage.actual_hours) totalProductionHours += stage.actual_hours;
            if (stage.estimated_hours) totalEstimatedHours += stage.estimated_hours;
          });
        } catch (error) {
          console.warn(`خطأ في تحميل مراحل الطلب ${order.id}:`, error);
        }
      }

      // حساب الكفاءة
      const efficiency = totalEstimatedHours > 0 
        ? (totalEstimatedHours / totalProductionHours) * 100 
        : 0;

      // تحليل المراحل
      const stageGroups = {};
      allStages.forEach(stage => {
        if (!stageGroups[stage.stage_name]) {
          stageGroups[stage.stage_name] = {
            name: stage.stage_name,
            count: 0,
            totalHours: 0,
            completedCount: 0,
            averageHours: 0,
            efficiency: 0
          };
        }
        
        stageGroups[stage.stage_name].count++;
        if (stage.actual_hours) {
          stageGroups[stage.stage_name].totalHours += stage.actual_hours;
        }
        if (stage.status === 'مكتملة') {
          stageGroups[stage.stage_name].completedCount++;
        }
      });

      const stageAnalysis = Object.values(stageGroups).map(stage => ({
        ...stage,
        averageHours: stage.count > 0 ? stage.totalHours / stage.count : 0,
        completionRate: stage.count > 0 ? (stage.completedCount / stage.count) * 100 : 0
      }));

      // تحليل أداء العمال
      const workers = await window.electronAPI.workers.getAll();
      const workerPerformance = [];

      for (const worker of workers) {
        const workerStages = allStages.filter(stage => stage.assigned_worker_id === worker.id);
        const completedStages = workerStages.filter(stage => stage.status === 'مكتملة');
        const totalHours = workerStages.reduce((sum, stage) => sum + (stage.actual_hours || 0), 0);
        
        if (workerStages.length > 0) {
          workerPerformance.push({
            name: worker.name,
            totalTasks: workerStages.length,
            completedTasks: completedStages.length,
            totalHours: totalHours,
            averageHoursPerTask: totalHours / workerStages.length,
            completionRate: (completedStages.length / workerStages.length) * 100,
            efficiency: 85 // سيتم حسابها لاحقاً بناءً على الوقت المقدر مقابل الفعلي
          });
        }
      }

      // تحليل استخدام المواد
      const materialUsage = [];
      const materials = await window.electronAPI.materials.getAll();

      for (const material of materials) {
        let totalUsed = 0;
        let totalCost = 0;
        let ordersCount = 0;

        for (const order of filteredOrders) {
          try {
            const orderMaterials = await window.electronAPI.orderMaterials.getByOrderId(order.id);
            const materialUsage = orderMaterials.find(om => om.material_id === material.id);
            
            if (materialUsage) {
              totalUsed += materialUsage.actual_quantity_used || materialUsage.quantity || 0;
              totalCost += materialUsage.total_cost || 0;
              ordersCount++;
            }
          } catch (error) {
            console.warn(`خطأ في تحميل مواد الطلب ${order.id}:`, error);
          }
        }

        if (totalUsed > 0) {
          materialUsage.push({
            name: material.name,
            totalUsed,
            totalCost,
            ordersCount,
            averageUsagePerOrder: totalUsed / ordersCount,
            unit: material.unit
          });
        }
      }

      // مقاييس الجودة
      let qualityMetrics = {
        totalChecks: 0,
        passedChecks: 0,
        failedChecks: 0,
        defectsCount: 0,
        averageQualityScore: 0
      };

      try {
        let allChecks = [];
        let allDefects = [];

        for (const order of filteredOrders) {
          const checks = await window.electronAPI.qualityChecks.getByOrderId(order.id);
          const defects = await window.electronAPI.defectsRepairs.getByOrderId(order.id);
          allChecks.push(...checks);
          allDefects.push(...defects);
        }

        const passedChecks = allChecks.filter(c => c.status === 'مقبول');
        const failedChecks = allChecks.filter(c => c.status === 'مرفوض');
        const totalScore = allChecks.reduce((sum, c) => sum + (c.quality_score || 0), 0);

        qualityMetrics = {
          totalChecks: allChecks.length,
          passedChecks: passedChecks.length,
          failedChecks: failedChecks.length,
          defectsCount: allDefects.length,
          averageQualityScore: allChecks.length > 0 ? totalScore / allChecks.length : 0
        };
      } catch (error) {
        console.warn('خطأ في تحميل بيانات الجودة:', error);
      }

      // تحليل زمني (آخر 7 أيام)
      const timeAnalysis = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const dayOrders = filteredOrders.filter(order => 
          order.created_at?.startsWith(dateStr)
        );
        const dayCompleted = filteredOrders.filter(order => 
          order.status === 'مكتمل' && order.updated_at?.startsWith(dateStr)
        );

        timeAnalysis.push({
          date: dateStr,
          ordersCreated: dayOrders.length,
          ordersCompleted: dayCompleted.length,
          revenue: dayCompleted.reduce((sum, order) => sum + (order.final_price || 0), 0)
        });
      }

      // تحليل التكاليف
      const totalMaterialCost = materialUsage.reduce((sum, m) => sum + m.totalCost, 0);
      const totalLaborCost = allStages.reduce((sum, stage) => 
        sum + ((stage.actual_hours || 0) * 50), 0 // 50 د.ل للساعة
      );
      const totalOverhead = filteredOrders.length * 100; // 100 د.ل رسوم عامة لكل طلب

      const costAnalysis = {
        totalMaterialCost,
        totalLaborCost,
        totalOverhead,
        averageCostPerOrder: filteredOrders.length > 0 
          ? (totalMaterialCost + totalLaborCost + totalOverhead) / filteredOrders.length 
          : 0
      };

      // حساب متوسط وقت الإنجاز
      const completedOrdersWithTime = completedOrders.filter(order => 
        order.created_at && order.updated_at
      );
      const averageCompletionTime = completedOrdersWithTime.length > 0
        ? completedOrdersWithTime.reduce((sum, order) => {
            const start = new Date(order.created_at);
            const end = new Date(order.updated_at);
            return sum + (end - start) / (1000 * 60 * 60 * 24); // بالأيام
          }, 0) / completedOrdersWithTime.length
        : 0;

      setReportData({
        overview: {
          totalOrders: filteredOrders.length,
          completedOrders: completedOrders.length,
          inProgressOrders: inProgressOrders.length,
          averageCompletionTime: Math.round(averageCompletionTime),
          totalProductionHours: Math.round(totalProductionHours),
          efficiency: Math.round(efficiency)
        },
        stageAnalysis,
        workerPerformance,
        materialUsage,
        qualityMetrics,
        timeAnalysis,
        costAnalysis
      });

    } catch (error) {
      console.error('خطأ في تحميل بيانات التقرير:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = () => {
    // تصدير التقرير إلى Excel
    const reportContent = {
      'نظرة عامة': [reportData.overview],
      'تحليل المراحل': reportData.stageAnalysis,
      'أداء العمال': reportData.workerPerformance,
      'استخدام المواد': reportData.materialUsage,
      'مقاييس الجودة': [reportData.qualityMetrics],
      'التحليل الزمني': reportData.timeAnalysis,
      'تحليل التكاليف': [reportData.costAnalysis]
    };

    // هنا يمكن إضافة منطق تصدير Excel
    console.log('تصدير التقرير:', reportContent);
    alert('سيتم تصدير التقرير قريباً');
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل تقارير الإنتاج...')
    );
  }

  return React.createElement('div', { className: 'production-reports-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-chart-bar' }),
          ' تقارير الإنتاج والتحليلات'
        ),
        React.createElement('div', { className: 'header-actions' },
          React.createElement('button', {
            className: 'btn btn-success',
            onClick: handleExportReport
          },
            React.createElement('i', { className: 'fas fa-file-excel' }),
            ' تصدير التقرير'
          )
        )
      )
    ),

    // فلاتر التاريخ
    React.createElement('div', { className: 'date-filters' },
      React.createElement('div', { className: 'filter-group' },
        React.createElement('label', null, 'من تاريخ:'),
        React.createElement('input', {
          type: 'date',
          value: dateRange.startDate,
          onChange: (e) => setDateRange({...dateRange, startDate: e.target.value})
        })
      ),
      React.createElement('div', { className: 'filter-group' },
        React.createElement('label', null, 'إلى تاريخ:'),
        React.createElement('input', {
          type: 'date',
          value: dateRange.endDate,
          onChange: (e) => setDateRange({...dateRange, endDate: e.target.value})
        })
      )
    ),

    // تبويبات التقرير
    React.createElement('div', { className: 'report-tabs' },
      React.createElement('div', { className: 'tab-buttons' },
        ['overview', 'stages', 'workers', 'materials', 'quality', 'timeline', 'costs'].map(tab =>
          React.createElement('button', {
            key: tab,
            className: `tab-btn ${activeTab === tab ? 'active' : ''}`,
            onClick: () => setActiveTab(tab)
          },
            tab === 'overview' ? 'نظرة عامة' :
            tab === 'stages' ? 'تحليل المراحل' :
            tab === 'workers' ? 'أداء العمال' :
            tab === 'materials' ? 'استخدام المواد' :
            tab === 'quality' ? 'مقاييس الجودة' :
            tab === 'timeline' ? 'التحليل الزمني' : 'تحليل التكاليف'
          )
        )
      )
    ),

    // محتوى التقرير
    React.createElement('div', { className: 'report-content' },
      // نظرة عامة
      activeTab === 'overview' && React.createElement('div', { className: 'overview-section' },
        React.createElement('div', { className: 'stats-grid' },
          React.createElement('div', { className: 'stat-card' },
            React.createElement('div', { className: 'stat-icon' },
              React.createElement('i', { className: 'fas fa-shopping-cart' })
            ),
            React.createElement('div', { className: 'stat-content' },
              React.createElement('h3', null, 'إجمالي الطلبات'),
              React.createElement('div', { className: 'stat-value' }, reportData.overview.totalOrders)
            )
          ),
          React.createElement('div', { className: 'stat-card' },
            React.createElement('div', { className: 'stat-icon' },
              React.createElement('i', { className: 'fas fa-check-circle' })
            ),
            React.createElement('div', { className: 'stat-content' },
              React.createElement('h3', null, 'الطلبات المكتملة'),
              React.createElement('div', { className: 'stat-value' }, reportData.overview.completedOrders)
            )
          ),
          React.createElement('div', { className: 'stat-card' },
            React.createElement('div', { className: 'stat-icon' },
              React.createElement('i', { className: 'fas fa-clock' })
            ),
            React.createElement('div', { className: 'stat-content' },
              React.createElement('h3', null, 'متوسط وقت الإنجاز'),
              React.createElement('div', { className: 'stat-value' }, `${reportData.overview.averageCompletionTime} يوم`)
            )
          ),
          React.createElement('div', { className: 'stat-card' },
            React.createElement('div', { className: 'stat-icon' },
              React.createElement('i', { className: 'fas fa-tachometer-alt' })
            ),
            React.createElement('div', { className: 'stat-content' },
              React.createElement('h3', null, 'كفاءة الإنتاج'),
              React.createElement('div', { className: 'stat-value' }, `${reportData.overview.efficiency}%`)
            )
          ),
          React.createElement('div', { className: 'stat-card' },
            React.createElement('div', { className: 'stat-icon' },
              React.createElement('i', { className: 'fas fa-hourglass-half' })
            ),
            React.createElement('div', { className: 'stat-content' },
              React.createElement('h3', null, 'ساعات الإنتاج'),
              React.createElement('div', { className: 'stat-value' }, `${reportData.overview.totalProductionHours} ساعة`)
            )
          )
        )
      )
    )
  );
};

module.exports = ProductionReports;
