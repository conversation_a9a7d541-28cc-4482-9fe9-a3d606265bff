/* تخطيط شامل ومحسن للتطبيق */

/* إعادة تعيين شاملة */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f8f9fa;
  color: #2c3e50;
  line-height: 1.6;
  direction: rtl;
  overflow-x: hidden;
}

/* إخفاء جميع العناصر الجانبية القديمة */
.sidebar,
.side-menu,
.left-sidebar,
.right-sidebar,
.sidebar-container {
  display: none !important;
}

/* تخطيط التطبيق الرئيسي */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.app-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* الشريط العلوي الثابت */
header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
}

/* شريط العلامة التجارية */
.navbar {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 0.75rem 0;
  box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
  width: 100%;
}

.navbar-container {
  max-width: none;
  margin: 0;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  color: #ff6b35;
  text-decoration: none;
}

.navbar-brand i {
  font-size: 1.1rem;
  color: #ff6b35;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* شريط التنقل الرئيسي */
.main-navigation {
  background: white;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.nav-container {
  max-width: none;
  margin: 0;
  padding: 0 1rem;
  width: 100%;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow-x: auto;
  padding: 0.5rem 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
  width: 100%;
}

.nav-menu::-webkit-scrollbar {
  display: none;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 0.75rem;
  color: #6c757d;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  position: relative;
  min-width: fit-content;
}

.nav-item:hover {
  background: rgba(30, 60, 114, 0.1);
  color: #1e3c72;
  text-decoration: none;
}

.nav-item.active {
  background: #1e3c72;
  color: white;
  box-shadow: 0 2px 8px rgba(30, 60, 114, 0.3);
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: #ff6b35;
  border-radius: 50%;
}

.nav-item i {
  font-size: 0.8rem;
  width: 14px;
  text-align: center;
}

/* منطقة المحتوى الرئيسية */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: calc(100vh - 140px);
}

.main-content {
  flex: 1;
  padding: 1rem;
  max-width: none;
  margin: 0;
  width: 100%;
  min-height: calc(100vh - 140px);
}

/* إزالة أي هوامش أو حشو إضافي */
.container,
.container-fluid,
.main-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* التأكد من أن المحتوى يملأ العرض الكامل */
.row {
  margin: 0 !important;
  width: 100% !important;
}

.col,
.col-12,
.col-md-12,
.col-lg-12 {
  padding: 0 !important;
  width: 100% !important;
}

/* إصلاح أي تخطيط bootstrap قديم */
.d-flex {
  display: flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.w-100 {
  width: 100% !important;
}

.h-100 {
  height: 100% !important;
}

/* قائمة الإشعارات */
.notifications-menu {
  position: relative;
}

.notifications-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.notifications-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notifications-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff6b35;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  margin-top: 0.5rem;
  overflow: hidden;
  z-index: 1001;
  color: #2c3e50;
}

/* قائمة المستخدم */
.user-menu {
  position: relative;
}

.user-menu-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.4rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  transition: all 0.3s ease;
  font-size: 0.75rem;
}

.user-menu-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  margin-top: 0.5rem;
  overflow: hidden;
  z-index: 1001;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  color: #2c3e50;
  text-decoration: none;
  transition: all 0.15s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: right;
  cursor: pointer;
  font-size: 0.75rem;
}

.user-menu-item:hover {
  background: #f8f9fa;
  color: #1e3c72;
  text-decoration: none;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 0.75rem;
  }

  .navbar-brand {
    font-size: 0.9rem;
  }

  .nav-container {
    padding: 0 0.75rem;
  }

  .nav-menu {
    gap: 0.25rem;
  }

  .nav-item {
    padding: 0.4rem 0.6rem;
    font-size: 0.7rem;
  }

  .main-content {
    padding: 0.75rem;
  }

  .navbar-menu {
    gap: 0.4rem;
  }

  .user-menu-button {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .navbar-brand span {
    display: none;
  }

  .nav-item span {
    display: none;
  }

  .nav-item {
    padding: 0.4rem;
    min-width: 36px;
    justify-content: center;
  }

  .main-content {
    padding: 0.6rem;
  }
}

/* التأكد من عدم وجود تمرير أفقي */
body {
  overflow-x: hidden;
}

html {
  overflow-x: hidden;
}

/* إصلاح أي مشاكل في العرض */
* {
  max-width: 100%;
}

/* إخفاء أي عناصر قد تسبب مشاكل في التخطيط */
.sidebar-toggle,
.menu-toggle,
.hamburger-menu {
  display: none !important;
}
