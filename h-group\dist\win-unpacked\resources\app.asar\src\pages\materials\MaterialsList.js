const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { formatCurrency, formatNumber } = require('../../utils/formatters');

const MaterialsList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [materials, setMaterials] = useState([]);
  const [filteredMaterials, setFilteredMaterials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');

  // تحميل المواد الخام
  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع المواد الخام مع معلومات المخزون
        const data = await window.api.materials.getAllWithInventory();
        setMaterials(data);
        setFilteredMaterials(data);
      } catch (error) {
        console.error('خطأ في تحميل المواد الخام:', error);
        setError('حدث خطأ أثناء تحميل المواد الخام. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchMaterials();
  }, []);

  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...materials];

    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(material =>
        material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (material.description && material.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (material.unit && material.unit.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredMaterials(result);
  }, [materials, searchTerm]);

  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
  };

  // الانتقال إلى صفحة تفاصيل المادة الخام
  const handleViewMaterial = (id) => {
    navigate(`/materials/${id}`);
  };

  // إنشاء مادة خام جديدة
  const handleCreateMaterial = () => {
    if (!hasPermission('materials_create')) {
      alert('ليس لديك صلاحية لإنشاء مادة خام جديدة');
      return;
    }

    navigate('/materials/new');
  };

  // تعديل المادة الخام
  const handleEditMaterial = (id) => {
    if (!hasPermission('materials_edit')) {
      alert('ليس لديك صلاحية لتعديل المادة الخام');
      return;
    }

    navigate(`/materials/edit/${id}`);
  };

  // حذف المادة الخام
  const handleDeleteMaterial = async (id) => {
    if (!hasPermission('materials_delete')) {
      alert('ليس لديك صلاحية لحذف المادة الخام');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذه المادة الخام؟ سيتم حذف سجل المخزون المرتبط بها أيضاً.')) {
      try {
        const result = await window.api.materials.delete(id);
        if (result.success) {
          // تحديث القائمة بعد الحذف
          setMaterials(materials.filter(material => material.id !== id));
          alert('تم حذف المادة الخام بنجاح');
        } else {
          throw new Error('فشل في حذف المادة الخام');
        }
      } catch (error) {
        console.error('خطأ في حذف المادة الخام:', error);
        alert('حدث خطأ أثناء حذف المادة الخام. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  // تحديث المخزون
  const handleUpdateInventory = (id) => {
    if (!hasPermission('inventory_edit')) {
      alert('ليس لديك صلاحية لتحديث المخزون');
      return;
    }

    navigate(`/inventory/update/${id}`);
  };

  // تصدير المواد الخام إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredMaterials.map(material => ({
        'اسم المادة': material.name,
        'الوصف': material.description || '-',
        'وحدة القياس': material.unit || '-',
        'التكلفة لكل وحدة': formatCurrency(material.cost_per_unit || 0),
        'الكمية المتوفرة': formatNumber(material.quantity || 0),
        'الحد الأدنى': formatNumber(material.min_quantity || 0),
        'حالة المخزون': material.quantity < material.min_quantity ? 'منخفض' : 'متوفر'
      }));

      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.api.exportToExcel(dataToExport, 'المواد الخام');

      if (result.success) {
        alert(`تم تصدير المواد الخام بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير المواد الخام');
      }
    } catch (error) {
      console.error('خطأ في تصدير المواد الخام:', error);
      alert('حدث خطأ أثناء تصدير المواد الخام. يرجى المحاولة مرة أخرى.');
    }
  };

  if (loading) {
    return <div className="loading">جاري تحميل المواد الخام...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="materials-list-page">
      <div className="page-header">
        <h2>المواد الخام</h2>
        <div className="page-actions">
          {hasPermission('materials_create') && (
            <button className="btn btn-primary" onClick={handleCreateMaterial}>
              <i className="fas fa-plus"></i> إضافة مادة خام جديدة
            </button>
          )}

          <button className="btn btn-success" onClick={handleExportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="card mb-4">
        <div className="card-header">فلاتر البحث</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">بحث</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="اسم المادة أو الوصف أو وحدة القياس"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="filter-actions">
            <button className="btn btn-secondary" onClick={handleResetFilters}>
              <i className="fas fa-redo"></i> إعادة تعيين الفلاتر
            </button>
          </div>
        </div>
      </div>

      {/* قائمة المواد الخام */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>قائمة المواد الخام</h5>
            <span className="badge bg-primary">{filteredMaterials.length} مادة</span>
          </div>
        </div>
        <div className="card-body">
          {filteredMaterials.length === 0 ? (
            <div className="alert alert-info">لا توجد مواد خام مطابقة للفلاتر المحددة</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>اسم المادة</th>
                    <th>وحدة القياس</th>
                    <th>التكلفة لكل وحدة</th>
                    <th>الكمية المتوفرة</th>
                    <th>الحد الأدنى</th>
                    <th>حالة المخزون</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMaterials.map(material => (
                    <tr key={material.id} className={material.quantity < material.min_quantity ? 'table-danger' : ''}>
                      <td>{material.name}</td>
                      <td>{material.unit || '-'}</td>
                      <td>{formatCurrency(material.cost_per_unit || 0)}</td>
                      <td>{formatNumber(material.quantity || 0)}</td>
                      <td>{formatNumber(material.min_quantity || 0)}</td>
                      <td>
                        <span className={`badge ${material.quantity < material.min_quantity ? 'bg-danger' : 'bg-success'}`}>
                          {material.quantity < material.min_quantity ? 'منخفض' : 'متوفر'}
                        </span>
                      </td>
                      <td>
                        <div className="btn-group">
                          <button
                            className="btn btn-sm btn-info"
                            onClick={() => handleViewMaterial(material.id)}
                            title="عرض التفاصيل"
                          >
                            <i className="fas fa-eye"></i>
                          </button>

                          {hasPermission('materials_edit') && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handleEditMaterial(material.id)}
                              title="تعديل"
                            >
                              <i className="fas fa-edit"></i>
                            </button>
                          )}

                          {hasPermission('inventory_edit') && (
                            <button
                              className="btn btn-sm btn-warning"
                              onClick={() => handleUpdateInventory(material.id)}
                              title="تحديث المخزون"
                            >
                              <i className="fas fa-boxes"></i>
                            </button>
                          )}

                          {hasPermission('materials_delete') && (
                            <button
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteMaterial(material.id)}
                              title="حذف"
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = MaterialsList;
