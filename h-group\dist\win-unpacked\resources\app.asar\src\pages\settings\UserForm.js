const React = require('react');
const { useState } = React;

const UserForm = () => {
  const [formData, setFormData] = useState({
    username: '',
    full_name: '',
    password: '',
    confirmPassword: '',
    role: 'user',
    permissions: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'الاسم الكامل مطلوب';
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة';
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      // محاكاة إنشاء المستخدم
      console.log('إنشاء مستخدم جديد:', formData);
      
      // إعادة تعيين النموذج
      setFormData({
        username: '',
        full_name: '',
        password: '',
        confirmPassword: '',
        role: 'user',
        permissions: ''
      });
      
      alert('تم إنشاء المستخدم بنجاح');
    } catch (error) {
      console.error('خطأ في إنشاء المستخدم:', error);
      alert('حدث خطأ أثناء إنشاء المستخدم');
    } finally {
      setLoading(false);
    }
  };

  return React.createElement('div', { className: 'user-form' },
    React.createElement('h1', null, 'إضافة مستخدم جديد'),

    React.createElement('form', { onSubmit: handleSubmit },
      React.createElement('div', { className: 'form-group' },
        React.createElement('label', null, 'اسم المستخدم *'),
        React.createElement('input', {
          type: 'text',
          value: formData.username,
          onChange: (e) => handleChange('username', e.target.value),
          className: errors.username ? 'error' : ''
        }),
        errors.username && React.createElement('div', { className: 'error-message' }, errors.username)
      ),

      React.createElement('div', { className: 'form-group' },
        React.createElement('label', null, 'الاسم الكامل *'),
        React.createElement('input', {
          type: 'text',
          value: formData.full_name,
          onChange: (e) => handleChange('full_name', e.target.value),
          className: errors.full_name ? 'error' : ''
        }),
        errors.full_name && React.createElement('div', { className: 'error-message' }, errors.full_name)
      ),

      React.createElement('div', { className: 'form-group' },
        React.createElement('label', null, 'كلمة المرور *'),
        React.createElement('input', {
          type: 'password',
          value: formData.password,
          onChange: (e) => handleChange('password', e.target.value),
          className: errors.password ? 'error' : ''
        }),
        errors.password && React.createElement('div', { className: 'error-message' }, errors.password)
      ),

      React.createElement('div', { className: 'form-group' },
        React.createElement('label', null, 'تأكيد كلمة المرور *'),
        React.createElement('input', {
          type: 'password',
          value: formData.confirmPassword,
          onChange: (e) => handleChange('confirmPassword', e.target.value),
          className: errors.confirmPassword ? 'error' : ''
        }),
        errors.confirmPassword && React.createElement('div', { className: 'error-message' }, errors.confirmPassword)
      ),

      React.createElement('div', { className: 'form-group' },
        React.createElement('label', null, 'الدور'),
        React.createElement('select', {
          value: formData.role,
          onChange: (e) => handleChange('role', e.target.value)
        },
          React.createElement('option', { value: 'user' }, 'مستخدم'),
          React.createElement('option', { value: 'manager' }, 'مدير'),
          React.createElement('option', { value: 'admin' }, 'مدير عام')
        )
      ),

      React.createElement('div', { className: 'form-group' },
        React.createElement('label', null, 'الصلاحيات'),
        React.createElement('textarea', {
          value: formData.permissions,
          onChange: (e) => handleChange('permissions', e.target.value),
          placeholder: 'مثال: orders,customers,reports (اتركه فارغاً لجميع الصلاحيات)'
        })
      ),

      React.createElement('div', { className: 'form-actions' },
        React.createElement('button', {
          type: 'submit',
          className: 'btn btn-primary',
          disabled: loading
        }, loading ? 'جاري الإنشاء...' : 'إنشاء المستخدم'),
        React.createElement('button', {
          type: 'button',
          className: 'btn btn-secondary'
        }, 'إلغاء')
      )
    )
  );
};

module.exports = UserForm;
