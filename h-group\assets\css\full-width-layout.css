/* تحسينات لاستغلال المساحة الكاملة للشاشة */

/* إزالة جميع القيود على العرض */
html, body {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden;
}

/* الحاوي الرئيسي */
.app-container,
.main-container,
.layout-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* الشريط العلوي */
.top-navbar,
.header,
.navbar {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.navbar-container,
.header-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 1rem !important;
}

/* شريط التنقل */
.main-navigation,
.nav-bar {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.nav-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 1rem !important;
}

/* المحتوى الرئيسي */
.main-content,
.content-area,
.page-content {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 1rem !important;
  box-sizing: border-box;
}

/* الحاويات والصفوف */
.container,
.container-fluid,
.row,
.col,
.column {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* لوحة التحكم */
.dashboard,
.dashboard-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.dashboard-content {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 1rem !important;
}

/* الشبكات */
.grid,
.cards-grid,
.stats-grid,
.reports-grid,
.dashboard-grid {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* البطاقات */
.card-container,
.cards-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* الجداول */
.table-container,
.data-table-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  overflow-x: auto;
}

.table,
.data-table {
  width: 100% !important;
  min-width: 100% !important;
}

/* النماذج */
.form-container,
.form-section {
  width: 100% !important;
  max-width: none !important;
  margin: 0 0 1rem 0 !important;
  padding: 1rem !important;
}

.form-row {
  width: 100% !important;
  margin: 0 !important;
  gap: 1rem !important;
}

/* الصفحات */
.page,
.page-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.page-header {
  width: 100% !important;
  max-width: none !important;
  margin: 0 0 1rem 0 !important;
  padding: 1rem !important;
}

/* تحسينات خاصة للشاشات الكبيرة */
@media (min-width: 1200px) {
  .main-content {
    padding: 1rem 2rem !important;
  }
  
  .navbar-container,
  .nav-container {
    padding: 0 2rem !important;
  }
  
  .page-header {
    padding: 1rem 2rem !important;
  }
  
  .form-section {
    padding: 1rem 2rem !important;
  }
}

@media (min-width: 1400px) {
  .main-content {
    padding: 1rem 3rem !important;
  }
  
  .navbar-container,
  .nav-container {
    padding: 0 3rem !important;
  }
  
  .page-header {
    padding: 1rem 3rem !important;
  }
  
  .form-section {
    padding: 1rem 3rem !important;
  }
}

@media (min-width: 1600px) {
  .main-content {
    padding: 1rem 4rem !important;
  }
  
  .navbar-container,
  .nav-container {
    padding: 0 4rem !important;
  }
  
  .page-header {
    padding: 1rem 4rem !important;
  }
  
  .form-section {
    padding: 1rem 4rem !important;
  }
}

/* تحسينات للشاشات المتوسطة */
@media (max-width: 992px) {
  .main-content {
    padding: 1rem !important;
  }
  
  .navbar-container,
  .nav-container {
    padding: 0 1rem !important;
  }
  
  .page-header {
    padding: 1rem !important;
  }
  
  .form-section {
    padding: 1rem !important;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .main-content {
    padding: 0.75rem !important;
  }
  
  .navbar-container,
  .nav-container {
    padding: 0 0.75rem !important;
  }
  
  .page-header {
    padding: 0.75rem !important;
  }
  
  .form-section {
    padding: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0.5rem !important;
  }
  
  .navbar-container,
  .nav-container {
    padding: 0 0.5rem !important;
  }
  
  .page-header {
    padding: 0.5rem !important;
  }
  
  .form-section {
    padding: 0.5rem !important;
  }
}

/* إصلاح أي مشاكل في التمرير */
* {
  box-sizing: border-box !important;
}

/* إزالة أي مساحات فارغة غير مرغوب فيها */
.sidebar,
.left-sidebar,
.right-sidebar {
  display: none !important;
}

/* التأكد من عدم وجود هوامش خفية */
.wrapper,
.layout-wrapper,
.app-wrapper {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* تحسين عرض المحتوى في الوسط */
.content-wrapper {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
}

/* إزالة أي قيود على العرض من Bootstrap أو مكتبات أخرى */
.container-xl,
.container-lg,
.container-md,
.container-sm {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}
