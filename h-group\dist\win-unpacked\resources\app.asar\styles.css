/* الخطوط والألوان الأساسية */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  direction: rtl;
  text-align: right;
}

/* التطبيق الرئيسي */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 100vh;
}

/* شريط التنقل العلوي المحدث */
.navbar {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
  margin: 0;
  padding: 0.75rem 1.5rem;
}

.navbar-brand {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar-brand:hover {
  color: #ff6b35;
  text-decoration: none;
}

/* شريط التنقل الرئيسي */
.main-navigation {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
}

.nav-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-menu::-webkit-scrollbar {
  display: none;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  font-weight: 500;
  position: relative;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b35;
  text-decoration: none;
  border-bottom-color: #ff6b35;
}

.nav-item.active {
  background: rgba(255, 107, 53, 0.2);
  color: #ff6b35;
  border-bottom-color: #ff6b35;
}

.nav-item i {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navbar-item {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.navbar-item:hover {
  background-color: rgba(255,255,255,0.15);
  color: #ff6b35;
  text-decoration: none;
}

/* المحتوى الرئيسي */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: calc(100vh - 140px);
}

/* لوحة التحكم */
.dashboard {
  max-width: 100%;
  margin: 0;
}

.dashboard h1 {
  color: #1f2937;
  margin-bottom: 1.5rem;
  font-size: 1.875rem;
  font-weight: 700;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.25rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.25rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-right: 4px solid #3b82f6;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.stat-card h3 {
  margin: 0 0 0.75rem 0;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card .stat-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.stat-card.warning {
  border-right-color: #f59e0b;
}

.stat-card.danger {
  border-right-color: #ef4444;
}

.stat-card.success {
  border-right-color: #10b981;
}

/* الأزرار */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:hover {
  background-color: #059669;
}

.btn-warning {
  background-color: #f59e0b;
  color: white;
}

.btn-warning:hover {
  background-color: #d97706;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

/* النماذج */
.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.form-control {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: #9ca3af;
}

/* الجداول */
.table-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 1.5rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
  font-size: 0.875rem;
}

.table th,
.table td {
  padding: 0.875rem 1rem;
  text-align: right;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.table th {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  font-weight: 600;
  color: white;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* التحميل */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.125rem;
  color: #6b7280;
}

/* التنبيهات */
.alert {
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.alert-warning {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

/* صفحة تسجيل الدخول */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  color: #1e3a8a;
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
}

.login-header h2 {
  color: #6b7280;
  margin: 0;
  font-size: 1rem;
  font-weight: normal;
}

.login-form {
  margin-bottom: 1.5rem;
}

.login-button {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
}

.login-footer {
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
}

/* صفحة 404 */
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
}

.not-found-content {
  text-align: center;
  max-width: 400px;
}

.not-found-content h1 {
  font-size: 6rem;
  color: #3b82f6;
  margin: 0;
}

.not-found-content h2 {
  color: #1f2937;
  margin: 1rem 0;
}

.not-found-content p {
  color: #6b7280;
  margin-bottom: 2rem;
}

/* أنماط إضافية للوحة التحكم */
.dashboard-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.dashboard-actions .btn {
  flex: 1;
}

/* أنماط إضافية للبطاقات */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(229, 231, 235, 0.5);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0.5rem 1rem;
  }

  .navbar-brand {
    font-size: 1.5rem;
  }

  .nav-menu {
    padding: 0 1rem;
  }

  .nav-item {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }

  .main-content {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .dashboard-actions {
    flex-direction: column;
  }

  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .card {
    padding: 1.25rem;
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .nav-item span {
    display: none;
  }

  .nav-item {
    padding: 0.875rem 0.75rem;
  }

  .main-content {
    padding: 0.75rem;
  }

  .stat-card .stat-value {
    font-size: 1.5rem;
  }
}

/* أنماط قائمة المستخدم */
.user-menu {
  position: relative;
}

.user-menu-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.user-menu-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ff6b35;
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
  margin-top: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: right;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
}

.user-menu-item:hover {
  background: rgba(59, 130, 246, 0.05);
  color: #3b82f6;
  text-decoration: none;
}

.user-menu-item i {
  width: 16px;
  text-align: center;
}

.logout-button {
  border-top: 1px solid #e5e7eb;
  color: #ef4444;
}

.logout-button:hover {
  background: rgba(239, 68, 68, 0.05);
  color: #dc2626;
}

/* تحسينات إضافية للتنبيهات */
.alert {
  padding: 1rem 1.25rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  border: 1px solid transparent;
  font-weight: 500;
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: #065f46;
  border-color: rgba(16, 185, 129, 0.2);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #991b1b;
  border-color: rgba(239, 68, 68, 0.2);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
  border-color: rgba(245, 158, 11, 0.2);
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border-color: rgba(59, 130, 246, 0.2);
}
