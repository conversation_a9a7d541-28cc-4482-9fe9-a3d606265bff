const React = require('react');
const { useState, useEffect } = React;
const { formatCurrency, formatDate } = require('../../utils/formatters');
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const DueInstallments = () => {
  const [dueInstallments, setDueInstallments] = useState([]);
  const [lateInstallments, setLateInstallments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { hasPermission } = useAuth();
  const navigate = useNavigate();

  // تحميل الأقساط المستحقة والمتأخرة
  useEffect(() => {
    const fetchInstallments = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل الأقساط المستحقة اليوم
        const dueData = await window.api.installments.getDueInstallments();
        setDueInstallments(dueData);

        // تحميل الأقساط المتأخرة
        const lateData = await window.api.installments.getLateInstallments();
        setLateInstallments(lateData);
      } catch (error) {
        console.error('خطأ في تحميل الأقساط:', error);
        setError('حدث خطأ أثناء تحميل الأقساط. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchInstallments();
  }, []);

  // دفع قسط
  const handlePayInstallment = async (installmentId) => {
    try {
      if (!hasPermission('invoices_edit')) {
        alert('ليس لديك صلاحية لدفع الأقساط');
        return;
      }

      const paymentData = {
        payment_date: new Date().toISOString(),
        payment_method: 'نقدي',
        notes: 'دفع قسط'
      };

      const result = await window.api.installments.pay(installmentId, paymentData);

      if (result.success) {
        alert('تم دفع القسط بنجاح');

        // تحديث قوائم الأقساط
        setDueInstallments(prev => prev.filter(installment => installment.id !== installmentId));
        setLateInstallments(prev => prev.filter(installment => installment.id !== installmentId));

        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'pay_installment',
          action_details: `دفع القسط رقم ${installmentId}`
        });
      }
    } catch (error) {
      console.error('خطأ في دفع القسط:', error);
      alert('حدث خطأ أثناء دفع القسط. يرجى المحاولة مرة أخرى.');
    }
  };

  // الانتقال إلى صفحة الفاتورة
  const handleViewInvoice = (invoiceId) => {
    navigate(`/invoices/${invoiceId}`);
  };

  // الاتصال بالعميل عبر WhatsApp
  const handleContactCustomer = (installment) => {
    if (!installment.customer_phone) {
      alert('لا يوجد رقم هاتف للعميل');
      return;
    }

    // إعداد رسالة WhatsApp
    const message = `مرحباً ${installment.customer_name}،\n\nنود تذكيركم بموعد استحقاق القسط رقم ${installment.installment_number} من الفاتورة رقم ${installment.invoice_number}.\nالمبلغ: ${installment.amount.toLocaleString()} ر.س\nتاريخ الاستحقاق: ${new Date(installment.due_date).toLocaleDateString('ar-SA')}\n\nشكراً لتعاملكم معنا،\nاتش قروب`;

    // تنسيق رقم الهاتف (إزالة الرمز + و00 في البداية)
    let phoneNumber = installment.customer_phone.replace(/^\+|^00/, '');

    // فتح WhatsApp
    window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank');
  };

  if (loading) {
    return <div className="loading">جاري تحميل الأقساط...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="due-installments-page">
      <div className="page-header">
        <h2>الأقساط المستحقة والمتأخرة</h2>
      </div>

      <div className="row">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header bg-warning text-white">
              <h4>الأقساط المستحقة اليوم ({dueInstallments.length})</h4>
            </div>
            <div className="card-body">
              {dueInstallments.length === 0 ? (
                <div className="alert alert-info">لا توجد أقساط مستحقة اليوم</div>
              ) : (
                <div className="installments-list">
                  {dueInstallments.map(installment => (
                    <div key={installment.id} className="installment-card">
                      <div className="installment-info">
                        <div className="installment-customer">{installment.customer_name}</div>
                        <div className="installment-number">
                          القسط رقم {installment.installment_number} - الفاتورة {installment.invoice_number}
                        </div>
                        <div className="installment-amount">{formatCurrency(installment.amount)}</div>
                        <div className="installment-date">
                          تاريخ الاستحقاق: {formatDate(installment.due_date)}
                        </div>
                      </div>

                      <div className="installment-actions">
                        {hasPermission('invoices_edit') && (
                          <button
                            className="btn btn-success"
                            onClick={() => handlePayInstallment(installment.id)}
                          >
                            <i className="fas fa-money-bill-wave"></i> دفع
                          </button>
                        )}

                        <button
                          className="btn btn-primary"
                          onClick={() => handleViewInvoice(installment.invoice_id)}
                        >
                          <i className="fas fa-file-invoice"></i> عرض الفاتورة
                        </button>

                        <button
                          className="btn btn-info"
                          onClick={() => handleContactCustomer(installment)}
                          disabled={!installment.customer_phone}
                        >
                          <i className="fab fa-whatsapp"></i> اتصال
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card">
            <div className="card-header bg-danger text-white">
              <h4>الأقساط المتأخرة ({lateInstallments.length})</h4>
            </div>
            <div className="card-body">
              {lateInstallments.length === 0 ? (
                <div className="alert alert-info">لا توجد أقساط متأخرة</div>
              ) : (
                <div className="installments-list">
                  {lateInstallments.map(installment => (
                    <div key={installment.id} className="installment-card">
                      <div className="installment-info">
                        <div className="installment-customer">{installment.customer_name}</div>
                        <div className="installment-number">
                          القسط رقم {installment.installment_number} - الفاتورة {installment.invoice_number}
                        </div>
                        <div className="installment-amount">{formatCurrency(installment.amount)}</div>
                        <div className="installment-date">
                          تاريخ الاستحقاق: {formatDate(installment.due_date)}
                        </div>
                        <div className="installment-late">
                          متأخر بـ {Math.floor((new Date() - new Date(installment.due_date)) / (1000 * 60 * 60 * 24))} يوم
                        </div>
                      </div>

                      <div className="installment-actions">
                        {hasPermission('invoices_edit') && (
                          <button
                            className="btn btn-success"
                            onClick={() => handlePayInstallment(installment.id)}
                          >
                            <i className="fas fa-money-bill-wave"></i> دفع
                          </button>
                        )}

                        <button
                          className="btn btn-primary"
                          onClick={() => handleViewInvoice(installment.invoice_id)}
                        >
                          <i className="fas fa-file-invoice"></i> عرض الفاتورة
                        </button>

                        <button
                          className="btn btn-info"
                          onClick={() => handleContactCustomer(installment)}
                          disabled={!installment.customer_phone}
                        >
                          <i className="fab fa-whatsapp"></i> اتصال
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

module.exports = DueInstallments;
