const React = require('react');
const { useState, useEffect } = React;
const { formatCurrency, formatDate, formatNumber } = require('../../utils/formatters');
const { Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const SalesReport = () => {
  const { hasPermission } = useAuth();

  // التحقق من الصلاحيات
  useEffect(() => {
    if (!hasPermission('reports_sales')) {
      alert('ليس لديك صلاحية للوصول إلى هذا التقرير');
      navigate('/reports');
    }
  }, [hasPermission]);

  // فلاتر التقرير
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    groupBy: 'month'
  });

  // بيانات التقرير
  const [reportData, setReportData] = useState({
    summary: {
      totalSales: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      highestSale: 0
    },
    salesByPeriod: [],
    topProducts: [],
    topCustomers: []
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // معالجة تغيير الفلاتر
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // توليد التقرير
  const generateReport = async () => {
    try {
      setLoading(true);
      setError(null);

      // التحقق من صحة التواريخ
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);

      if (startDate > endDate) {
        throw new Error('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
      }

      // استدعاء API لجلب بيانات التقرير
      const data = await window.api.reports.getSalesReport(filters);

      // التأكد من وجود البيانات وتعيين القيم الافتراضية
      const safeData = {
        summary: {
          totalSales: data?.summary?.totalSales || 0,
          totalOrders: data?.summary?.totalOrders || 0,
          averageOrderValue: data?.summary?.averageOrderValue || 0,
          highestSale: data?.summary?.highestSale || 0
        },
        salesByPeriod: Array.isArray(data?.salesByPeriod) ? data.salesByPeriod : [],
        topProducts: Array.isArray(data?.topProducts) ? data.topProducts : [],
        topCustomers: Array.isArray(data?.topCustomers) ? data.topCustomers : []
      };

      setReportData(safeData);
    } catch (error) {
      console.error('خطأ في توليد التقرير:', error);
      setError(error.message || 'حدث خطأ أثناء توليد التقرير. يرجى المحاولة مرة أخرى.');

      // تعيين بيانات فارغة في حالة الخطأ
      setReportData({
        summary: {
          totalSales: 0,
          totalOrders: 0,
          averageOrderValue: 0,
          highestSale: 0
        },
        salesByPeriod: [],
        topProducts: [],
        topCustomers: []
      });
    } finally {
      setLoading(false);
    }
  };

  // توليد التقرير عند تحميل الصفحة
  useEffect(() => {
    generateReport();
  }, []);

  // تصدير التقرير إلى Excel
  const exportToExcel = async () => {
    try {
      const result = await window.api.reports.exportSalesReport(filters);

      if (result.success) {
        alert(`تم تصدير التقرير بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير التقرير');
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير. يرجى المحاولة مرة أخرى.');
    }
  };

  // طباعة التقرير
  const printReport = () => {
    window.print();
  };

  return (
    <div className="reports-page sales-report-page">
      <div className="page-header">
        <h2>تقرير المبيعات</h2>
        <div className="page-actions">
          <button className="compact-btn compact-btn-primary" onClick={exportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير Excel
          </button>

          <button className="compact-btn compact-btn-secondary" onClick={printReport}>
            <i className="fas fa-print"></i> طباعة
          </button>

          <Link to="/reports" className="compact-btn compact-btn-outline">
            <i className="fas fa-arrow-right"></i> العودة
          </Link>
        </div>
      </div>

      {/* فلاتر التقرير */}
      <div className="form-section">
        <div className="form-section-title">فلاتر التقرير</div>
        <div className="form-row">
          <div className="compact-form-group">
            <label>من تاريخ</label>
            <input
              type="date"
              className="compact-form-control"
              name="startDate"
              value={filters.startDate}
              onChange={handleFilterChange}
            />
          </div>

          <div className="compact-form-group">
            <label>إلى تاريخ</label>
            <input
              type="date"
              className="compact-form-control"
              name="endDate"
              value={filters.endDate}
              onChange={handleFilterChange}
            />
          </div>

          <div className="compact-form-group">
            <label>تجميع حسب</label>
            <select
              className="compact-form-control"
              name="groupBy"
              value={filters.groupBy}
              onChange={handleFilterChange}
            >
              <option value="day">يوم</option>
              <option value="week">أسبوع</option>
              <option value="month">شهر</option>
              <option value="quarter">ربع سنة</option>
              <option value="year">سنة</option>
            </select>
          </div>
        </div>

        <div className="action-buttons">
          <button
            className="action-btn compact-btn-primary"
            onClick={generateReport}
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span>جاري التحميل...</span>
              </>
            ) : (
              <>
                <i className="fas fa-sync"></i> توليد التقرير
              </>
            )}
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      {/* ملخص المبيعات */}
      <div className="stats-grid">
        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-money-bill-wave"></i>
              إجمالي المبيعات
            </div>
            <div className="compact-card-badge badge-success">
              مبيعات
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{formatCurrency(reportData?.summary?.totalSales || 0)}</div>
          </div>
        </div>

        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-shopping-cart"></i>
              عدد الطلبات
            </div>
            <div className="compact-card-badge badge-primary">
              طلبات
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{reportData?.summary?.totalOrders || 0}</div>
          </div>
        </div>

        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-chart-line"></i>
              متوسط قيمة الطلب
            </div>
            <div className="compact-card-badge badge-info">
              متوسط
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{formatCurrency(reportData?.summary?.averageOrderValue || 0)}</div>
          </div>
        </div>

        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-trophy"></i>
              أعلى قيمة طلب
            </div>
            <div className="compact-card-badge badge-warning">
              أعلى
            </div>
          </div>
          <div className="compact-card-body">
            <div className="compact-card-value">{formatCurrency(reportData?.summary?.highestSale || 0)}</div>
          </div>
        </div>
      </div>

      {/* المبيعات حسب الفترة */}
      <div className="compact-table-container">
        <div className="compact-card-header">
          <div className="compact-card-title">
            <i className="fas fa-calendar-alt"></i>
            المبيعات حسب الفترة
          </div>
        </div>
        {!reportData?.salesByPeriod || reportData.salesByPeriod.length === 0 ? (
          <div className="alert alert-info">لا توجد بيانات للعرض</div>
        ) : (
          <table className="compact-table">
            <thead>
              <tr>
                <th>الفترة</th>
                <th>عدد الطلبات</th>
                <th>إجمالي المبيعات</th>
                <th>متوسط قيمة الطلب</th>
              </tr>
            </thead>
            <tbody>
              {reportData.salesByPeriod.map((period, index) => (
                <tr key={index}>
                  <td>{period?.period || 'غير محدد'}</td>
                  <td>{formatNumber(period?.orderCount || 0)}</td>
                  <td>{formatCurrency(period?.totalSales || 0)}</td>
                  <td>{formatCurrency(period?.averageOrderValue || 0)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      <div className="reports-grid">
        {/* أفضل المنتجات مبيعًا */}
        <div className="compact-table-container">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-star"></i>
              أفضل المنتجات مبيعًا
            </div>
          </div>
          {!reportData?.topProducts || reportData.topProducts.length === 0 ? (
            <div className="alert alert-info">لا توجد بيانات للعرض</div>
          ) : (
            <table className="compact-table">
              <thead>
                <tr>
                  <th>المنتج</th>
                  <th>عدد الطلبات</th>
                  <th>إجمالي المبيعات</th>
                </tr>
              </thead>
              <tbody>
                {reportData.topProducts.map((product, index) => (
                  <tr key={index}>
                    <td>{product?.productName || 'غير محدد'}</td>
                    <td>{formatNumber(product?.orderCount || 0)}</td>
                    <td>{formatCurrency(product?.totalSales || 0)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {/* أفضل العملاء */}
        <div className="compact-table-container">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-users"></i>
              أفضل العملاء
            </div>
          </div>
          {!reportData?.topCustomers || reportData.topCustomers.length === 0 ? (
            <div className="alert alert-info">لا توجد بيانات للعرض</div>
          ) : (
            <table className="compact-table">
              <thead>
                <tr>
                  <th>العميل</th>
                  <th>عدد الطلبات</th>
                  <th>إجمالي المشتريات</th>
                </tr>
              </thead>
              <tbody>
                {reportData.topCustomers.map((customer, index) => (
                  <tr key={index}>
                    <td>{customer?.customerName || 'غير محدد'}</td>
                    <td>{formatNumber(customer?.orderCount || 0)}</td>
                    <td>{formatCurrency(customer?.totalSpent || 0)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = SalesReport;
