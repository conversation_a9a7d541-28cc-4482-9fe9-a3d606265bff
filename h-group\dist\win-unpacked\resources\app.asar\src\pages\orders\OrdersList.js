const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const OrdersList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });

  // تحميل الطلبات
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع الطلبات
        const data = await window.electronAPI.orders.getAll();
        setOrders(data);
        setFilteredOrders(data);
      } catch (error) {
        console.error('خطأ في تحميل الطلبات:', error);
        setError('حدث خطأ أثناء تحميل الطلبات. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...orders];

    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(order =>
        order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.specifications && order.specifications.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // تطبيق فلتر الحالة
    if (statusFilter !== 'all') {
      result = result.filter(order => order.status === statusFilter);
    }

    // تطبيق فلتر التاريخ
    if (dateRange.startDate && dateRange.endDate) {
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      endDate.setHours(23, 59, 59, 999); // نهاية اليوم

      result = result.filter(order => {
        const orderDate = new Date(order.order_date);
        return orderDate >= startDate && orderDate <= endDate;
      });
    }

    setFilteredOrders(result);
  }, [orders, searchTerm, statusFilter, dateRange]);

  // تغيير فلتر التاريخ
  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setDateRange({
      startDate: '',
      endDate: ''
    });
  };

  // الانتقال إلى صفحة تفاصيل الطلب
  const handleViewOrder = (id) => {
    navigate(`/orders/${id}`);
  };

  // إنشاء طلب جديد
  const handleCreateOrder = () => {
    if (!hasPermission('orders_create')) {
      alert('ليس لديك صلاحية لإنشاء طلب جديد');
      return;
    }

    navigate('/orders/new');
  };

  // إنشاء طلب مخصص جديد
  const handleCreateCustomOrder = () => {
    if (!hasPermission('orders_create')) {
      alert('ليس لديك صلاحية لإنشاء طلب جديد');
      return;
    }

    navigate('/orders/custom/new');
  };

  // تعديل الطلب
  const handleEditOrder = (id) => {
    if (!hasPermission('orders_edit')) {
      alert('ليس لديك صلاحية لتعديل الطلب');
      return;
    }

    navigate(`/orders/edit/${id}`);
  };

  // إنشاء فاتورة للطلب
  const handleCreateInvoice = (id) => {
    if (!hasPermission('invoices_create')) {
      alert('ليس لديك صلاحية لإنشاء فاتورة');
      return;
    }

    navigate(`/invoices/create/${id}`);
  };

  // تتبع مراحل الإنتاج
  const handleTrackProduction = (id) => {
    navigate(`/orders/production/${id}`);
  };

  // فحص الجودة
  const handleQualityControl = (id) => {
    navigate(`/quality/${id}`);
  };

  // تصدير الطلبات إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredOrders.map(order => ({
        'رقم الطلب': order.order_number,
        'العميل': order.customer_name,
        'المنتج': order.product_name || '-',
        'تاريخ الطلب': new Date(order.order_date).toLocaleDateString('ar-SA'),
        'تاريخ التسليم': order.delivery_date ? new Date(order.delivery_date).toLocaleDateString('ar-SA') : '-',
        'الحالة': order.status,
        'السعر النهائي': order.final_price
      }));

      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.electronAPI.exportToExcel(dataToExport, 'الطلبات');

      if (result.success) {
        alert(`تم تصدير الطلبات بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير الطلبات');
      }
    } catch (error) {
      console.error('خطأ في تصدير الطلبات:', error);
      alert('حدث خطأ أثناء تصدير الطلبات. يرجى المحاولة مرة أخرى.');
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل الطلبات...');
  }

  if (error) {
    return React.createElement('div', { className: 'alert alert-danger' }, error);
  }

  return React.createElement('div', { className: 'orders-list-page professional-page' },
    React.createElement('div', { className: 'page-header modern-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-hammer' }),
          ' الطلبات المخصصة'
        ),
        React.createElement('p', { className: 'page-subtitle' }, 'إدارة طلبات التصنيع حسب الطلب')
      ),
      React.createElement('div', { className: 'page-actions' },
        hasPermission('orders_create') && React.createElement('div', { className: 'btn-group me-2' },
          React.createElement('button', {
            className: 'btn btn-primary modern-btn',
            onClick: handleCreateCustomOrder
          },
            React.createElement('i', { className: 'fas fa-hammer' }),
            ' طلب مخصص جديد'
          )
        ),
        React.createElement('button', {
          className: 'btn btn-success modern-btn',
          onClick: handleExportToExcel
        },
          React.createElement('i', { className: 'fas fa-file-excel' }),
          ' تصدير إلى Excel'
        )
      )
    ),

    React.createElement('div', { className: 'modern-card glass-card' },
      React.createElement('div', { className: 'card-header modern-card-header' },
        React.createElement('h3', { className: 'card-title' },
          React.createElement('i', { className: 'fas fa-list' }),
          ' قائمة الطلبات المخصصة'
        ),
        React.createElement('div', { className: 'card-stats' },
          React.createElement('span', { className: 'status-badge primary' }, `${filteredOrders.length} طلب`)
        )
      ),
      React.createElement('div', { className: 'card-body' },
        filteredOrders.length === 0
          ? React.createElement('div', { className: 'alert alert-info' }, 'لا توجد طلبات حالياً')
          : React.createElement('div', { className: 'table-responsive' },
              React.createElement('table', { className: 'modern-table professional-table' },
                React.createElement('thead', null,
                  React.createElement('tr', null,
                    React.createElement('th', null, 'رقم الطلب'),
                    React.createElement('th', null, 'العميل'),
                    React.createElement('th', null, 'المنتج'),
                    React.createElement('th', null, 'تاريخ الطلب'),
                    React.createElement('th', null, 'الحالة'),
                    React.createElement('th', null, 'السعر النهائي'),
                    React.createElement('th', null, 'الإجراءات')
                  )
                ),
                React.createElement('tbody', null,
                  filteredOrders.map(order =>
                    React.createElement('tr', { key: order.id },
                      React.createElement('td', null, order.order_number),
                      React.createElement('td', null, order.customer_name),
                      React.createElement('td', null, order.product_name || '-'),
                      React.createElement('td', null, new Date(order.order_date).toLocaleDateString('ar-SA')),
                      React.createElement('td', null,
                        React.createElement('span', { className: `status-badge ${order.status}` }, order.status)
                      ),
                      React.createElement('td', null, `${order.final_price.toLocaleString()} ر.س`),
                      React.createElement('td', null,
                        React.createElement('div', { className: 'action-buttons' },
                          React.createElement('button', {
                            className: 'action-btn info-btn',
                            onClick: () => handleViewOrder(order.id),
                            title: 'عرض التفاصيل'
                          },
                            React.createElement('i', { className: 'fas fa-eye' })
                          ),
                          React.createElement('button', {
                            className: 'action-btn warning-btn',
                            onClick: () => handleTrackProduction(order.id),
                            title: 'تتبع مراحل الإنتاج'
                          },
                            React.createElement('i', { className: 'fas fa-tasks' })
                          ),
                          React.createElement('button', {
                            className: 'action-btn purple-btn',
                            onClick: () => handleQualityControl(order.id),
                            title: 'فحص الجودة'
                          },
                            React.createElement('i', { className: 'fas fa-award' })
                          ),
                          hasPermission('orders_edit') && React.createElement('button', {
                            className: 'action-btn primary-btn',
                            onClick: () => handleEditOrder(order.id),
                            title: 'تعديل الطلب'
                          },
                            React.createElement('i', { className: 'fas fa-edit' })
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
      )
    )
  );
};

module.exports = OrdersList;
