const React = require('react');
const { useState, useEffect } = React;
const { useNavigate, useParams } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { formatCurrency, formatNumber } = require('../../utils/formatters');

// استيراد ملف CSS للتصميم الاحترافي
require('../../styles/CustomOrderForm.css');

const CustomOrderForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [isEditMode, setIsEditMode] = useState(!!id);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);

  // بيانات الطلب المخصص
  const [order, setOrder] = useState({
    customer_id: '',
    order_date: new Date().toISOString().split('T')[0],
    delivery_date: '',

    // معلومات المنتج المخصص
    product_name: '',
    product_category: '',
    dimensions: '',
    wood_type: '',
    finish_type: '',
    color: '',
    hardware_details: '',
    custom_specifications: '',

    // معلومات الإنتاج
    estimated_hours: 0,
    complexity_level: 'متوسط',
    priority_level: 'عادي',

    // التكاليف
    status: 'جديد',
    worker_fee: 0,
    factory_fee: 0,
    designer_fee: 0,
    owner_margin: 0,
    materials_cost: 0,
    total_cost: 0,
    final_price: 0,

    // ملاحظات
    design_notes: '',
    production_notes: '',
    customer_notes: '',
    internal_notes: ''
  });

  // بيانات إضافية
  const [customers, setCustomers] = useState([]);
  const [materials, setMaterials] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [selectedMaterials, setSelectedMaterials] = useState([]);
  const [customSpecs, setCustomSpecs] = useState([]);
  const [productionStages, setProductionStages] = useState([]);

  // خيارات ثابتة
  const productCategories = [
    'غرف نوم', 'غرف معيشة', 'مطابخ', 'مكاتب', 'خزائن', 'طاولات', 'كراسي', 'أخرى'
  ];

  const woodTypes = [
    'خشب الزان', 'خشب البلوط', 'خشب الصنوبر', 'خشب الماهوجني', 'خشب الجوز', 'MDF', 'خشب مضغوط', 'أخرى'
  ];

  const finishTypes = [
    'دهان لامع', 'دهان مطفي', 'ورنيش شفاف', 'صبغة خشب', 'لاكيه', 'طبيعي', 'أخرى'
  ];

  const complexityLevels = [
    { value: 'بسيط', label: 'بسيط', multiplier: 1 },
    { value: 'متوسط', label: 'متوسط', multiplier: 1.3 },
    { value: 'معقد', label: 'معقد', multiplier: 1.7 },
    { value: 'معقد جداً', label: 'معقد جداً', multiplier: 2.2 }
  ];

  const priorityLevels = [
    { value: 'عادي', label: 'عادي', multiplier: 1 },
    { value: 'عالي', label: 'عالي', multiplier: 1.2 },
    { value: 'عاجل', label: 'عاجل', multiplier: 1.5 }
  ];

  // تحميل البيانات الأولية
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);

        // تحميل العملاء والمواد والعمال
        const [customersData, materialsData, workersData] = await Promise.all([
          window.api.customers.getAll(),
          window.api.materials.getAll(),
          window.api.workers.getAll()
        ]);

        setCustomers(customersData);
        setMaterials(materialsData);
        setWorkers(workersData);

        // إذا كان في وضع التعديل، تحميل بيانات الطلب
        if (isEditMode && id) {
          const orderData = await window.api.orders.getById(id);
          if (orderData) {
            setOrder(orderData);

            // تحميل المواد والمواصفات ومراحل الإنتاج
            const [materialsData, specsData, stagesData] = await Promise.all([
              window.api.orderMaterials.getByOrderId(id),
              window.api.customSpecifications.getByOrderId(id),
              window.api.productionStages.getByOrderId(id)
            ]);

            setSelectedMaterials(materialsData);
            setCustomSpecs(specsData);
            setProductionStages(stagesData);
          }
        }
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        setError('حدث خطأ أثناء تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [id, isEditMode]);

  // التحقق من الصلاحيات
  useEffect(() => {
    const permission = isEditMode ? 'orders_edit' : 'orders_create';
    if (!hasPermission(permission)) {
      alert(`ليس لديك صلاحية ${isEditMode ? 'تعديل' : 'إنشاء'} الطلبات`);
      navigate('/orders');
    }
  }, [hasPermission, isEditMode, navigate]);

  // تحديث قيم النموذج
  const handleInputChange = (field, value) => {
    setOrder(prev => ({
      ...prev,
      [field]: value
    }));

    // إعادة حساب التكلفة عند تغيير بعض القيم
    if (['estimated_hours', 'complexity_level', 'priority_level'].includes(field)) {
      calculateCosts();
    }
  };

  // حساب التكاليف
  const calculateCosts = () => {
    const complexity = complexityLevels.find(c => c.value === order.complexity_level);
    const priority = priorityLevels.find(p => p.value === order.priority_level);

    const complexityMultiplier = complexity ? complexity.multiplier : 1;
    const priorityMultiplier = priority ? priority.multiplier : 1;

    // حساب تكلفة المواد
    const materialsCost = selectedMaterials.reduce((sum, material) => {
      return sum + (material.quantity * material.cost_per_unit);
    }, 0);

    // حساب تكلفة العمالة بناءً على الساعات والتعقيد
    const baseHourlyRate = 50; // سعر الساعة الأساسي
    const workerFee = order.estimated_hours * baseHourlyRate * complexityMultiplier;

    // حساب رسوم المصنع (نسبة من تكلفة المواد والعمالة)
    const factoryFee = (materialsCost + workerFee) * 0.15;

    // رسوم التصميم (للطلبات المعقدة)
    const designerFee = order.complexity_level === 'معقد' || order.complexity_level === 'معقد جداً'
      ? workerFee * 0.2 : 0;

    // التكلفة الإجمالية
    const totalCost = materialsCost + workerFee + factoryFee + designerFee;

    // السعر النهائي مع هامش الربح والأولوية
    const finalPrice = (totalCost + order.owner_margin) * priorityMultiplier;

    setOrder(prev => ({
      ...prev,
      materials_cost: materialsCost,
      worker_fee: workerFee,
      factory_fee: factoryFee,
      designer_fee: designerFee,
      total_cost: totalCost,
      final_price: finalPrice
    }));
  };

  // إضافة مادة خام
  const addMaterial = () => {
    setSelectedMaterials(prev => [...prev, {
      material_id: '',
      material_name: '',
      unit: '',
      quantity: 0,
      cost_per_unit: 0,
      total_cost: 0,
      waste_percentage: 5,
      notes: ''
    }]);
  };

  // تحديث مادة خام
  const updateMaterial = (index, field, value) => {
    setSelectedMaterials(prev => {
      const updated = [...prev];
      updated[index][field] = value;

      // تحديث التكلفة الإجمالية للمادة
      if (field === 'quantity' || field === 'cost_per_unit') {
        const quantity = parseFloat(updated[index].quantity) || 0;
        const costPerUnit = parseFloat(updated[index].cost_per_unit) || 0;
        const wastePercentage = parseFloat(updated[index].waste_percentage) || 0;

        // إضافة نسبة الهدر
        const actualQuantity = quantity * (1 + wastePercentage / 100);
        updated[index].total_cost = actualQuantity * costPerUnit;
      }

      // تحديث اسم المادة ووحدة القياس عند اختيار مادة جديدة
      if (field === 'material_id' && value) {
        const material = materials.find(m => m.id === parseInt(value));
        if (material) {
          updated[index].material_name = material.name;
          updated[index].unit = material.unit;
          updated[index].cost_per_unit = material.cost_per_unit;
        }
      }

      return updated;
    });

    // إعادة حساب التكاليف
    setTimeout(calculateCosts, 100);
  };

  // حذف مادة خام
  const removeMaterial = (index) => {
    setSelectedMaterials(prev => {
      const updated = [...prev];
      updated.splice(index, 1);
      return updated;
    });
    setTimeout(calculateCosts, 100);
  };

  // إضافة مواصفة مخصصة
  const addCustomSpec = () => {
    setCustomSpecs(prev => [...prev, {
      spec_category: '',
      spec_name: '',
      spec_value: '',
      spec_unit: '',
      is_critical: false,
      affects_cost: false,
      cost_impact: 0,
      spec_notes: ''
    }]);
  };

  // تحديث مواصفة مخصصة
  const updateCustomSpec = (index, field, value) => {
    setCustomSpecs(prev => {
      const updated = [...prev];
      updated[index][field] = value;
      return updated;
    });
  };

  // حذف مواصفة مخصصة
  const removeCustomSpec = (index) => {
    setCustomSpecs(prev => {
      const updated = [...prev];
      updated.splice(index, 1);
      return updated;
    });
  };

  // حفظ الطلب المخصص
  const handleSaveOrder = async () => {
    try {
      setLoading(true);
      setError(null);

      // التحقق من البيانات المطلوبة
      if (!order.customer_id) {
        setError('يرجى اختيار العميل');
        setCurrentStep(1);
        return;
      }

      if (!order.product_name) {
        setError('يرجى إدخال اسم المنتج');
        setCurrentStep(1);
        return;
      }

      // إعداد بيانات الطلب للحفظ
      const orderData = {
        ...order,
        materials: selectedMaterials.filter(m => m.material_id && m.quantity > 0),
        customSpecs: customSpecs.filter(s => s.spec_name && s.spec_value)
      };

      let savedOrder;
      if (isEditMode) {
        // تحديث الطلب الموجود
        savedOrder = await window.api.orders.update(id, orderData);
      } else {
        // إنشاء طلب جديد
        savedOrder = await window.api.orders.createCustom(orderData);
      }

      // عرض رسالة نجاح
      alert(`تم ${isEditMode ? 'تحديث' : 'حفظ'} الطلب بنجاح!\nرقم الطلب: ${savedOrder.order_number || savedOrder.id}`);

      // الانتقال إلى صفحة الطلبات
      navigate('/orders');

    } catch (error) {
      console.error('خطأ في حفظ الطلب:', error);
      setError(`حدث خطأ أثناء ${isEditMode ? 'تحديث' : 'حفظ'} الطلب: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container-fluid">
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">جاري التحميل...</span>
            </div>
            <p className="mt-3">جاري تحميل البيانات...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-12">
          {/* رأس الصفحة */}
          <div className="page-header">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h1 className="page-title">
                  <i className="fas fa-plus-circle me-2"></i>
                  {isEditMode ? 'تعديل طلب مخصص' : 'إنشاء طلب مخصص جديد'}
                </h1>
                <p className="page-subtitle">
                  {isEditMode ? 'تعديل تفاصيل الطلب المخصص' : 'إنشاء طلب أثاث مخصص حسب مواصفات العميل'}
                </p>
              </div>
              <div>
                <button
                  className="btn btn-secondary me-2"
                  onClick={() => navigate('/orders')}
                >
                  <i className="fas fa-arrow-right me-2"></i>
                  العودة للطلبات
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="alert alert-danger">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </div>
          )}

          {/* شريط التقدم */}
          <div className="card mb-4">
            <div className="card-body">
              <div className="progress-steps">
                <div className={`step ${currentStep >= 1 ? 'active' : ''}`}>
                  <div className="step-number">1</div>
                  <div className="step-label">معلومات أساسية</div>
                </div>
                <div className={`step ${currentStep >= 2 ? 'active' : ''}`}>
                  <div className="step-number">2</div>
                  <div className="step-label">مواصفات المنتج</div>
                </div>
                <div className={`step ${currentStep >= 3 ? 'active' : ''}`}>
                  <div className="step-number">3</div>
                  <div className="step-label">المواد والتكاليف</div>
                </div>
                <div className={`step ${currentStep >= 4 ? 'active' : ''}`}>
                  <div className="step-number">4</div>
                  <div className="step-label">مراجعة وحفظ</div>
                </div>
              </div>
            </div>
          </div>

          {/* محتوى الخطوات */}
          <div className="row">
            <div className="col-12">
              {/* الخطوة الأولى: معلومات أساسية */}
              {currentStep === 1 && (
                <div className="card">
                  <div className="card-header">
                    <h5 className="card-title">
                      <i className="fas fa-info-circle me-2"></i>
                      المعلومات الأساسية للطلب
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label required">العميل</label>
                          <select
                            className="form-select"
                            value={order.customer_id}
                            onChange={(e) => handleInputChange('customer_id', e.target.value)}
                            required
                          >
                            <option value="">اختر العميل</option>
                            {customers.map(customer => (
                              <option key={customer.id} value={customer.id}>
                                {customer.name} - {customer.phone}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label required">اسم المنتج المطلوب</label>
                          <input
                            type="text"
                            className="form-control"
                            value={order.product_name}
                            onChange={(e) => handleInputChange('product_name', e.target.value)}
                            placeholder="مثال: طاولة طعام مخصصة"
                            required
                          />
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">فئة المنتج</label>
                          <select
                            className="form-select"
                            value={order.product_category}
                            onChange={(e) => handleInputChange('product_category', e.target.value)}
                          >
                            <option value="">اختر الفئة</option>
                            {productCategories.map(category => (
                              <option key={category} value={category}>{category}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">تاريخ الطلب</label>
                          <input
                            type="date"
                            className="form-control"
                            value={order.order_date}
                            onChange={(e) => handleInputChange('order_date', e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">تاريخ التسليم المطلوب</label>
                          <input
                            type="date"
                            className="form-control"
                            value={order.delivery_date}
                            onChange={(e) => handleInputChange('delivery_date', e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">مستوى الأولوية</label>
                          <select
                            className="form-select"
                            value={order.priority_level}
                            onChange={(e) => handleInputChange('priority_level', e.target.value)}
                          >
                            {priorityLevels.map(level => (
                              <option key={level.value} value={level.value}>
                                {level.label}
                                {level.multiplier > 1 && ` (+${Math.round((level.multiplier - 1) * 100)}%)`}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="col-12">
                        <div className="form-group mb-3">
                          <label className="form-label">ملاحظات العميل</label>
                          <textarea
                            className="form-control"
                            rows="3"
                            value={order.customer_notes}
                            onChange={(e) => handleInputChange('customer_notes', e.target.value)}
                            placeholder="أي ملاحظات أو طلبات خاصة من العميل..."
                          />
                        </div>
                      </div>
                    </div>

                    <div className="d-flex justify-content-end">
                      <button
                        className="btn btn-primary"
                        onClick={() => setCurrentStep(2)}
                        disabled={!order.customer_id || !order.product_name}
                      >
                        التالي: مواصفات المنتج
                        <i className="fas fa-arrow-left ms-2"></i>
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* الخطوة الثانية: مواصفات المنتج */}
              {currentStep === 2 && (
                <div className="card">
                  <div className="card-header">
                    <h5 className="card-title">
                      <i className="fas fa-ruler-combined me-2"></i>
                      مواصفات المنتج المخصص
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">الأبعاد (طول × عرض × ارتفاع)</label>
                          <input
                            type="text"
                            className="form-control"
                            value={order.dimensions}
                            onChange={(e) => handleInputChange('dimensions', e.target.value)}
                            placeholder="مثال: 200 × 100 × 75 سم"
                          />
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">نوع الخشب</label>
                          <select
                            className="form-select"
                            value={order.wood_type}
                            onChange={(e) => handleInputChange('wood_type', e.target.value)}
                          >
                            <option value="">اختر نوع الخشب</option>
                            {woodTypes.map(wood => (
                              <option key={wood} value={wood}>{wood}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">نوع التشطيب</label>
                          <select
                            className="form-select"
                            value={order.finish_type}
                            onChange={(e) => handleInputChange('finish_type', e.target.value)}
                          >
                            <option value="">اختر نوع التشطيب</option>
                            {finishTypes.map(finish => (
                              <option key={finish} value={finish}>{finish}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">اللون</label>
                          <input
                            type="text"
                            className="form-control"
                            value={order.color}
                            onChange={(e) => handleInputChange('color', e.target.value)}
                            placeholder="مثال: بني غامق، أبيض، طبيعي"
                          />
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">مستوى التعقيد</label>
                          <select
                            className="form-select"
                            value={order.complexity_level}
                            onChange={(e) => handleInputChange('complexity_level', e.target.value)}
                          >
                            {complexityLevels.map(level => (
                              <option key={level.value} value={level.value}>
                                {level.label}
                                {level.multiplier > 1 && ` (+${Math.round((level.multiplier - 1) * 100)}%)`}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">الساعات المقدرة للإنتاج</label>
                          <input
                            type="number"
                            className="form-control"
                            value={order.estimated_hours}
                            onChange={(e) => handleInputChange('estimated_hours', parseFloat(e.target.value) || 0)}
                            min="0"
                            step="0.5"
                          />
                        </div>
                      </div>

                      <div className="col-12">
                        <div className="form-group mb-3">
                          <label className="form-label">تفاصيل الإكسسوارات والمفصلات</label>
                          <textarea
                            className="form-control"
                            rows="3"
                            value={order.hardware_details}
                            onChange={(e) => handleInputChange('hardware_details', e.target.value)}
                            placeholder="مثال: مفصلات خفية، مقابض ذهبية، أدراج بسكك انزلاق ناعمة..."
                          />
                        </div>
                      </div>

                      <div className="col-12">
                        <div className="form-group mb-3">
                          <label className="form-label">مواصفات إضافية مخصصة</label>
                          <textarea
                            className="form-control"
                            rows="4"
                            value={order.custom_specifications}
                            onChange={(e) => handleInputChange('custom_specifications', e.target.value)}
                            placeholder="أي مواصفات أو تفاصيل إضافية خاصة بهذا الطلب..."
                          />
                        </div>
                      </div>
                    </div>

                    <div className="d-flex justify-content-between">
                      <button
                        className="btn btn-secondary"
                        onClick={() => setCurrentStep(1)}
                      >
                        <i className="fas fa-arrow-right me-2"></i>
                        السابق
                      </button>
                      <button
                        className="btn btn-primary"
                        onClick={() => setCurrentStep(3)}
                      >
                        التالي: المواد والتكاليف
                        <i className="fas fa-arrow-left ms-2"></i>
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* الخطوة الثالثة: المواد والتكاليف */}
              {currentStep === 3 && (
                <div className="card">
                  <div className="card-header">
                    <h5 className="card-title">
                      <i className="fas fa-calculator me-2"></i>
                      المواد الخام والتكاليف
                    </h5>
                  </div>
                  <div className="card-body">
                    {/* قسم المواد الخام */}
                    <div className="mb-4">
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <h6 className="mb-0">
                          <i className="fas fa-boxes me-2"></i>
                          المواد الخام المطلوبة
                        </h6>
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={addMaterial}
                        >
                          <i className="fas fa-plus me-2"></i>
                          إضافة مادة
                        </button>
                      </div>

                      {selectedMaterials.length > 0 ? (
                        <div className="materials-table">
                          <table className="table">
                            <thead>
                              <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>السعر/الوحدة</th>
                                <th>نسبة الهدر %</th>
                                <th>الإجمالي</th>
                                <th>إجراءات</th>
                              </tr>
                            </thead>
                            <tbody>
                              {selectedMaterials.map((material, index) => (
                                <tr key={index}>
                                  <td>
                                    <select
                                      className="form-select form-select-sm"
                                      value={material.material_id}
                                      onChange={(e) => updateMaterial(index, 'material_id', e.target.value)}
                                    >
                                      <option value="">اختر المادة</option>
                                      {materials.map(mat => (
                                        <option key={mat.id} value={mat.id}>
                                          {mat.name}
                                        </option>
                                      ))}
                                    </select>
                                  </td>
                                  <td>
                                    <input
                                      type="number"
                                      className="form-control form-control-sm"
                                      value={material.quantity}
                                      onChange={(e) => updateMaterial(index, 'quantity', e.target.value)}
                                      min="0"
                                      step="0.1"
                                    />
                                  </td>
                                  <td>{material.unit || '-'}</td>
                                  <td>
                                    <input
                                      type="number"
                                      className="form-control form-control-sm"
                                      value={material.cost_per_unit}
                                      onChange={(e) => updateMaterial(index, 'cost_per_unit', e.target.value)}
                                      min="0"
                                      step="0.01"
                                    />
                                  </td>
                                  <td>
                                    <input
                                      type="number"
                                      className="form-control form-control-sm"
                                      value={material.waste_percentage}
                                      onChange={(e) => updateMaterial(index, 'waste_percentage', e.target.value)}
                                      min="0"
                                      max="50"
                                      step="1"
                                    />
                                  </td>
                                  <td>
                                    <strong>{formatCurrency(material.total_cost || 0)}</strong>
                                  </td>
                                  <td>
                                    <button
                                      className="btn btn-sm btn-danger"
                                      onClick={() => removeMaterial(index)}
                                    >
                                      <i className="fas fa-trash"></i>
                                    </button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="alert alert-info">
                          <i className="fas fa-info-circle me-2"></i>
                          لم يتم إضافة أي مواد خام بعد. اضغط على "إضافة مادة" لبدء إضافة المواد المطلوبة.
                        </div>
                      )}
                    </div>

                    {/* قسم التكاليف */}
                    <div className="row">
                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">هامش الربح المطلوب</label>
                          <input
                            type="number"
                            className="form-control"
                            value={order.owner_margin}
                            onChange={(e) => handleInputChange('owner_margin', parseFloat(e.target.value) || 0)}
                            min="0"
                            step="100"
                          />
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="form-group mb-3">
                          <label className="form-label">ملاحظات التصميم</label>
                          <textarea
                            className="form-control"
                            rows="3"
                            value={order.design_notes}
                            onChange={(e) => handleInputChange('design_notes', e.target.value)}
                            placeholder="ملاحظات خاصة بالتصميم..."
                          />
                        </div>
                      </div>

                      <div className="col-12">
                        <div className="form-group mb-3">
                          <label className="form-label">ملاحظات الإنتاج</label>
                          <textarea
                            className="form-control"
                            rows="3"
                            value={order.production_notes}
                            onChange={(e) => handleInputChange('production_notes', e.target.value)}
                            placeholder="ملاحظات خاصة بعملية الإنتاج..."
                          />
                        </div>
                      </div>
                    </div>

                    {/* ملخص التكاليف */}
                    <div className="cost-summary">
                      <h6 className="mb-3">
                        <i className="fas fa-chart-line me-2"></i>
                        ملخص التكاليف
                      </h6>
                      <div className="cost-item">
                        <span className="cost-label">تكلفة المواد الخام:</span>
                        <span className="cost-value">{formatCurrency(order.materials_cost)}</span>
                      </div>
                      <div className="cost-item">
                        <span className="cost-label">تكلفة العمالة:</span>
                        <span className="cost-value">{formatCurrency(order.worker_fee)}</span>
                      </div>
                      <div className="cost-item">
                        <span className="cost-label">رسوم المصنع:</span>
                        <span className="cost-value">{formatCurrency(order.factory_fee)}</span>
                      </div>
                      <div className="cost-item">
                        <span className="cost-label">رسوم التصميم:</span>
                        <span className="cost-value">{formatCurrency(order.designer_fee)}</span>
                      </div>
                      <div className="cost-item">
                        <span className="cost-label">هامش الربح:</span>
                        <span className="cost-value">{formatCurrency(order.owner_margin)}</span>
                      </div>
                      <div className="cost-item">
                        <span className="cost-label">السعر النهائي:</span>
                        <span className="cost-value">{formatCurrency(order.final_price)}</span>
                      </div>
                    </div>

                    <div className="d-flex justify-content-between mt-4">
                      <button
                        className="btn btn-secondary"
                        onClick={() => setCurrentStep(2)}
                      >
                        <i className="fas fa-arrow-right me-2"></i>
                        السابق
                      </button>
                      <button
                        className="btn btn-primary"
                        onClick={() => setCurrentStep(4)}
                      >
                        التالي: مراجعة وحفظ
                        <i className="fas fa-arrow-left ms-2"></i>
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* الخطوة الرابعة: مراجعة وحفظ */}
              {currentStep === 4 && (
                <div className="card">
                  <div className="card-header">
                    <h5 className="card-title">
                      <i className="fas fa-check-circle me-2"></i>
                      مراجعة الطلب وحفظ
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="alert alert-info">
                      <i className="fas fa-info-circle me-2"></i>
                      يرجى مراجعة جميع التفاصيل قبل حفظ الطلب. يمكنك العودة لأي خطوة سابقة لتعديل البيانات.
                    </div>

                    {/* ملخص الطلب */}
                    <div className="row">
                      <div className="col-md-6">
                        <div className="card mb-3">
                          <div className="card-header">
                            <h6 className="mb-0">معلومات أساسية</h6>
                          </div>
                          <div className="card-body">
                            <p><strong>العميل:</strong> {customers.find(c => c.id == order.customer_id)?.name || '-'}</p>
                            <p><strong>المنتج:</strong> {order.product_name}</p>
                            <p><strong>الفئة:</strong> {order.product_category || '-'}</p>
                            <p><strong>تاريخ التسليم:</strong> {order.delivery_date || '-'}</p>
                            <p><strong>الأولوية:</strong> {order.priority_level}</p>
                          </div>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="card mb-3">
                          <div className="card-header">
                            <h6 className="mb-0">مواصفات المنتج</h6>
                          </div>
                          <div className="card-body">
                            <p><strong>الأبعاد:</strong> {order.dimensions || '-'}</p>
                            <p><strong>نوع الخشب:</strong> {order.wood_type || '-'}</p>
                            <p><strong>التشطيب:</strong> {order.finish_type || '-'}</p>
                            <p><strong>اللون:</strong> {order.color || '-'}</p>
                            <p><strong>التعقيد:</strong> {order.complexity_level}</p>
                            <p><strong>الساعات المقدرة:</strong> {order.estimated_hours} ساعة</p>
                          </div>
                        </div>
                      </div>

                      <div className="col-12">
                        <div className="card mb-3">
                          <div className="card-header">
                            <h6 className="mb-0">التكاليف النهائية</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>تكلفة المواد:</strong> {formatCurrency(order.materials_cost)}</p>
                                <p><strong>تكلفة العمالة:</strong> {formatCurrency(order.worker_fee)}</p>
                                <p><strong>رسوم المصنع:</strong> {formatCurrency(order.factory_fee)}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>رسوم التصميم:</strong> {formatCurrency(order.designer_fee)}</p>
                                <p><strong>هامش الربح:</strong> {formatCurrency(order.owner_margin)}</p>
                                <p className="h5 text-primary"><strong>السعر النهائي:</strong> {formatCurrency(order.final_price)}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* ملاحظات نهائية */}
                    <div className="form-group mb-4">
                      <label className="form-label">ملاحظات داخلية</label>
                      <textarea
                        className="form-control"
                        rows="3"
                        value={order.internal_notes}
                        onChange={(e) => handleInputChange('internal_notes', e.target.value)}
                        placeholder="أي ملاحظات داخلية للفريق..."
                      />
                    </div>

                    <div className="d-flex justify-content-between">
                      <button
                        className="btn btn-secondary"
                        onClick={() => setCurrentStep(3)}
                      >
                        <i className="fas fa-arrow-right me-2"></i>
                        السابق
                      </button>
                      <button
                        className="btn btn-success btn-lg"
                        onClick={handleSaveOrder}
                        disabled={loading || !order.customer_id || !order.product_name}
                      >
                        {loading ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                            جاري الحفظ...
                          </>
                        ) : (
                          <>
                            <i className="fas fa-save me-2"></i>
                            {isEditMode ? 'تحديث الطلب' : 'حفظ الطلب'}
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

module.exports = CustomOrderForm;
