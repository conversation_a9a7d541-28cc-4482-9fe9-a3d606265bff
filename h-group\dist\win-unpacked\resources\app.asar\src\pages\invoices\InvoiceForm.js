const React = require('react');
const { useState, useEffect } = React;

const InvoiceForm = () => {
  const [formData, setFormData] = useState({
    customer_id: '',
    order_id: '',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    items: [],
    subtotal: 0,
    tax_amount: 0,
    discount_amount: 0,
    total_amount: 0,
    notes: ''
  });
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCustomers();
    loadOrders();
  }, []);

  const loadCustomers = async () => {
    try {
      // محاكاة تحميل العملاء
      const mockCustomers = [
        { id: 1, name: 'أحمد محمد', phone: '0501234567' },
        { id: 2, name: 'فاطمة علي', phone: '0509876543' }
      ];
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  };

  const loadOrders = async () => {
    try {
      // محاكاة تحميل الطلبات
      const mockOrders = [
        { id: 1, order_number: 'ORD-001', customer_name: 'أحمد محمد', total: 5000 },
        { id: 2, order_number: 'ORD-002', customer_name: 'فاطمة علي', total: 3000 }
      ];
      setOrders(mockOrders);
    } catch (error) {
      console.error('خطأ في تحميل الطلبات:', error);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculateTotal = () => {
    const subtotal = formData.subtotal || 0;
    const tax = formData.tax_amount || 0;
    const discount = formData.discount_amount || 0;
    const total = subtotal + tax - discount;
    
    setFormData(prev => ({
      ...prev,
      total_amount: total
    }));
  };

  useEffect(() => {
    calculateTotal();
  }, [formData.subtotal, formData.tax_amount, formData.discount_amount]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      // محاكاة إنشاء الفاتورة
      console.log('إنشاء فاتورة جديدة:', formData);
      alert('تم إنشاء الفاتورة بنجاح');
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة:', error);
      alert('حدث خطأ أثناء إنشاء الفاتورة');
    } finally {
      setLoading(false);
    }
  };

  return React.createElement('div', { className: 'invoice-form' },
    React.createElement('h1', null, 'إنشاء فاتورة جديدة'),

    React.createElement('form', { onSubmit: handleSubmit },
      React.createElement('div', { className: 'form-row' },
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'العميل *'),
          React.createElement('select', {
            value: formData.customer_id,
            onChange: (e) => handleChange('customer_id', e.target.value),
            required: true
          },
            React.createElement('option', { value: '' }, 'اختر العميل'),
            customers.map(customer =>
              React.createElement('option', { key: customer.id, value: customer.id }, customer.name)
            )
          )
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'الطلب'),
          React.createElement('select', {
            value: formData.order_id,
            onChange: (e) => handleChange('order_id', e.target.value)
          },
            React.createElement('option', { value: '' }, 'اختر الطلب (اختياري)'),
            orders.map(order =>
              React.createElement('option', { key: order.id, value: order.id }, 
                `${order.order_number} - ${order.customer_name}`
              )
            )
          )
        )
      ),

      React.createElement('div', { className: 'form-row' },
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'تاريخ الفاتورة *'),
          React.createElement('input', {
            type: 'date',
            value: formData.invoice_date,
            onChange: (e) => handleChange('invoice_date', e.target.value),
            required: true
          })
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'تاريخ الاستحقاق'),
          React.createElement('input', {
            type: 'date',
            value: formData.due_date,
            onChange: (e) => handleChange('due_date', e.target.value)
          })
        )
      ),

      React.createElement('div', { className: 'invoice-amounts' },
        React.createElement('h3', null, 'المبالغ'),
        
        React.createElement('div', { className: 'form-row' },
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'المبلغ الفرعي (ر.س) *'),
            React.createElement('input', {
              type: 'number',
              step: '0.01',
              value: formData.subtotal,
              onChange: (e) => handleChange('subtotal', parseFloat(e.target.value) || 0),
              required: true
            })
          ),

          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'الضريبة (ر.س)'),
            React.createElement('input', {
              type: 'number',
              step: '0.01',
              value: formData.tax_amount,
              onChange: (e) => handleChange('tax_amount', parseFloat(e.target.value) || 0)
            })
          )
        ),

        React.createElement('div', { className: 'form-row' },
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'الخصم (ر.س)'),
            React.createElement('input', {
              type: 'number',
              step: '0.01',
              value: formData.discount_amount,
              onChange: (e) => handleChange('discount_amount', parseFloat(e.target.value) || 0)
            })
          ),

          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'المبلغ الإجمالي (ر.س)'),
            React.createElement('input', {
              type: 'number',
              step: '0.01',
              value: formData.total_amount,
              readOnly: true,
              className: 'readonly'
            })
          )
        )
      ),

      React.createElement('div', { className: 'form-group' },
        React.createElement('label', null, 'ملاحظات'),
        React.createElement('textarea', {
          value: formData.notes,
          onChange: (e) => handleChange('notes', e.target.value),
          rows: 4,
          placeholder: 'أي ملاحظات إضافية...'
        })
      ),

      React.createElement('div', { className: 'form-actions' },
        React.createElement('button', {
          type: 'submit',
          className: 'btn btn-primary',
          disabled: loading
        }, loading ? 'جاري الإنشاء...' : 'إنشاء الفاتورة'),
        React.createElement('button', {
          type: 'button',
          className: 'btn btn-secondary'
        }, 'إلغاء')
      )
    )
  );
};

module.exports = InvoiceForm;
