const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const { login, currentUser } = useAuth();
  const navigate = useNavigate();

  // إذا كان المستخدم مسجل الدخول بالفعل، انتقل إلى الصفحة الرئيسية
  useEffect(() => {
    if (currentUser) {
      navigate('/');
    }
  }, [currentUser, navigate]);

  // معالجة تسجيل الدخول
  const handleSubmit = async (e) => {
    e.preventDefault();

    // التحقق من إدخال اسم المستخدم وكلمة المرور
    if (!username || !password) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    try {
      setError('');
      setLoading(true);

      // محاولة تسجيل الدخول
      await login(username, password);

      // إذا نجح تسجيل الدخول، انتقل إلى الصفحة الرئيسية
      navigate('/');
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      setError(error.message || 'فشل تسجيل الدخول. يرجى التحقق من اسم المستخدم وكلمة المرور.');
    } finally {
      setLoading(false);
    }
  };

  return React.createElement('div', { className: 'login-container' },
    React.createElement('div', { className: 'login-card' },
      React.createElement('div', { className: 'login-header' },
        React.createElement('h1', null, 'اتش قروب'),
        React.createElement('h2', null, 'نظام إدارة ورشة النجارة')
      ),
      React.createElement('form', { className: 'login-form', onSubmit: handleSubmit },
        error && React.createElement('div', { className: 'alert alert-danger' }, error),
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', { htmlFor: 'username', className: 'form-label' }, 'اسم المستخدم'),
          React.createElement('input', {
            type: 'text',
            id: 'username',
            className: 'form-control',
            value: username,
            onChange: (e) => setUsername(e.target.value),
            disabled: loading,
            autoFocus: true
          })
        ),
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', { htmlFor: 'password', className: 'form-label' }, 'كلمة المرور'),
          React.createElement('input', {
            type: 'password',
            id: 'password',
            className: 'form-control',
            value: password,
            onChange: (e) => setPassword(e.target.value),
            disabled: loading
          })
        ),
        React.createElement('button', {
          type: 'submit',
          className: 'btn btn-primary login-button',
          disabled: loading
        }, loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول')
      ),
      React.createElement('div', { className: 'login-footer' },
        React.createElement('p', null, `© ${new Date().getFullYear()} اتش قروب - جميع الحقوق محفوظة`)
      )
    )
  );
};

module.exports = Login;
