/* تصميم احترافي للوحة تتبع مراحل الإنتاج */

.production-tracker {
  padding: 1rem;
}

/* رأس الصفحة */
.page-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(30, 60, 114, 0.3);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

/* بطاقة معلومات الطلب */
.order-info-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.order-info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.order-info-card .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
  padding: 1.5rem;
}

.order-info-card .card-body {
  padding: 2rem;
}

.order-info-card p {
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.order-info-card strong {
  color: #495057;
  font-weight: 600;
}

/* بطاقة نسبة الإنجاز */
.progress-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.progress-card .card-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 1.5rem;
}

.progress-card .card-body {
  padding: 2rem;
}

/* دائرة التقدم */
.progress-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(
    #28a745 0deg,
    #28a745 calc(var(--progress, 0) * 3.6deg),
    #e9ecef calc(var(--progress, 0) * 3.6deg),
    #e9ecef 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.progress-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #28a745;
  z-index: 1;
}

.progress-bar-container {
  width: 100%;
}

.progress {
  height: 8px;
  border-radius: 4px;
  background-color: #e9ecef;
}

.progress-bar {
  border-radius: 4px;
  transition: width 0.6s ease;
}

/* بطاقة المراحل */
.stages-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stages-card .card-header {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  padding: 1.5rem;
}

.stages-card .card-body {
  padding: 2rem;
}

/* خط زمني للمراحل */
.stages-timeline {
  position: relative;
  padding: 1rem 0;
}

.stage-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  position: relative;
}

.stage-item:last-child {
  margin-bottom: 0;
}

/* موصل المراحل */
.stage-connector {
  position: relative;
  margin-left: 2rem;
  margin-right: 1rem;
}

.connector-line {
  position: absolute;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 4rem;
  background: #dee2e6;
}

.stage-item.مكتملة .connector-line {
  background: #28a745;
}

.stage-item.قيد_التنفيذ .connector-line {
  background: linear-gradient(to bottom, #007bff 0%, #dee2e6 100%);
}

/* أيقونة المرحلة */
.stage-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  background: #6c757d;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 2;
  position: relative;
}

.stage-item.لم_تبدأ .stage-icon {
  background: #6c757d;
}

.stage-item.قيد_التنفيذ .stage-icon {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  animation: pulse 2s infinite;
}

.stage-item.مكتملة .stage-icon {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.stage-item.متأخرة .stage-icon {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.stage-item.معلقة .stage-icon {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
  }
  50% {
    box-shadow: 0 4px 25px rgba(0, 123, 255, 0.8);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
  }
}

/* محتوى المرحلة */
.stage-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #dee2e6;
  transition: all 0.3s ease;
}

.stage-item.مكتملة .stage-content {
  border-left-color: #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
}

.stage-item.قيد_التنفيذ .stage-content {
  border-left-color: #007bff;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
}

.stage-item.متأخرة .stage-content {
  border-left-color: #dc3545;
  background: linear-gradient(135deg, #fff8f8 0%, #ffffff 100%);
}

.stage-item.معلقة .stage-content {
  border-left-color: #ffc107;
  background: linear-gradient(135deg, #fffdf8 0%, #ffffff 100%);
}

.stage-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* رأس المرحلة */
.stage-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
}

.stage-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #495057;
  margin: 0;
  flex: 1;
}

.badge {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-weight: 600;
}

/* وصف المرحلة */
.stage-description {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

/* تفاصيل المرحلة */
.stage-details {
  margin-bottom: 1rem;
}

.stage-details small {
  display: block;
  margin-bottom: 0.5rem;
  color: #6c757d;
}

/* شريط تقدم المرحلة */
.stage-progress {
  margin-top: 1rem;
}

.progress-sm {
  height: 6px;
}

/* إجراءات المرحلة */
.stage-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.stage-actions .btn {
  border-radius: 6px;
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.stage-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* الأزرار */
.btn {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.5);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.5);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.btn-success:hover {
  background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.5);
  color: white;
}

/* التنبيهات */
.alert {
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

.alert-info {
  background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
  color: #004085;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .page-header {
    padding: 1.5rem;
    text-align: center;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .stage-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stage-connector {
    margin: 0 0 1rem 0;
    align-self: center;
  }
  
  .connector-line {
    width: 4rem;
    height: 2px;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  
  .stage-content {
    margin-top: 1rem;
  }
  
  .stage-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .stage-actions {
    justify-content: center;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}
