const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const InventoryList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [inventory, setInventory] = useState([]);
  const [filteredInventory, setFilteredInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');
  const [showLowStock, setShowLowStock] = useState(false);
  
  // تحميل بيانات المخزون
  useEffect(() => {
    const fetchInventory = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل جميع المواد الخام مع معلومات المخزون
        const data = await window.api.inventory.getAll();
        setInventory(data);
        setFilteredInventory(data);
      } catch (error) {
        console.error('خطأ في تحميل بيانات المخزون:', error);
        setError('حدث خطأ أثناء تحميل بيانات المخزون. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchInventory();
  }, []);
  
  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...inventory];
    
    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(item => 
        item.material_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // تطبيق فلتر المخزون المنخفض
    if (showLowStock) {
      result = result.filter(item => item.quantity < item.min_quantity);
    }
    
    setFilteredInventory(result);
  }, [inventory, searchTerm, showLowStock]);
  
  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
    setShowLowStock(false);
  };
  
  // تحديث المخزون
  const handleUpdateInventory = (materialId) => {
    if (!hasPermission('inventory_edit')) {
      alert('ليس لديك صلاحية لتحديث المخزون');
      return;
    }
    
    navigate(`/inventory/update/${materialId}`);
  };
  
  // عرض تفاصيل المادة الخام
  const handleViewMaterial = (materialId) => {
    navigate(`/materials/${materialId}`);
  };
  
  // تصدير بيانات المخزون إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredInventory.map(item => ({
        'اسم المادة': item.material_name,
        'وحدة القياس': item.unit || '-',
        'الكمية المتوفرة': item.quantity || 0,
        'الحد الأدنى': item.min_quantity || 0,
        'حالة المخزون': item.quantity < item.min_quantity ? 'منخفض' : 'متوفر',
        'آخر تحديث': item.last_updated ? new Date(item.last_updated).toLocaleDateString('ar-SA') : '-'
      }));
      
      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.api.exportToExcel(dataToExport, 'المخزون');
      
      if (result.success) {
        alert(`تم تصدير بيانات المخزون بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير بيانات المخزون');
      }
    } catch (error) {
      console.error('خطأ في تصدير بيانات المخزون:', error);
      alert('حدث خطأ أثناء تصدير بيانات المخزون. يرجى المحاولة مرة أخرى.');
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل بيانات المخزون...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  return (
    <div className="inventory-list-page">
      <div className="page-header">
        <h2>المخزون</h2>
        <div className="page-actions">
          <button className="btn btn-success" onClick={handleExportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>
        </div>
      </div>
      
      {/* فلاتر البحث */}
      <div className="card mb-4">
        <div className="card-header">فلاتر البحث</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">بحث</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="اسم المادة"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">خيارات العرض</label>
                <div className="form-check">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="showLowStock"
                    checked={showLowStock}
                    onChange={(e) => setShowLowStock(e.target.checked)}
                  />
                  <label className="form-check-label" htmlFor="showLowStock">
                    عرض المواد منخفضة المخزون فقط
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <div className="filter-actions">
            <button className="btn btn-secondary" onClick={handleResetFilters}>
              <i className="fas fa-redo"></i> إعادة تعيين الفلاتر
            </button>
          </div>
        </div>
      </div>
      
      {/* قائمة المخزون */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>قائمة المخزون</h5>
            <span className="badge bg-primary">{filteredInventory.length} مادة</span>
          </div>
        </div>
        <div className="card-body">
          {filteredInventory.length === 0 ? (
            <div className="alert alert-info">لا توجد مواد مطابقة للفلاتر المحددة</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>اسم المادة</th>
                    <th>وحدة القياس</th>
                    <th>الكمية المتوفرة</th>
                    <th>الحد الأدنى</th>
                    <th>حالة المخزون</th>
                    <th>آخر تحديث</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredInventory.map(item => (
                    <tr key={item.id} className={item.quantity < item.min_quantity ? 'table-danger' : ''}>
                      <td>{item.material_name}</td>
                      <td>{item.unit || '-'}</td>
                      <td>{item.quantity || 0}</td>
                      <td>{item.min_quantity || 0}</td>
                      <td>
                        <span className={`badge ${item.quantity < item.min_quantity ? 'bg-danger' : 'bg-success'}`}>
                          {item.quantity < item.min_quantity ? 'منخفض' : 'متوفر'}
                        </span>
                      </td>
                      <td>{item.last_updated ? new Date(item.last_updated).toLocaleString('ar-SA') : '-'}</td>
                      <td>
                        <div className="btn-group">
                          <button
                            className="btn btn-sm btn-info"
                            onClick={() => handleViewMaterial(item.material_id)}
                            title="عرض تفاصيل المادة"
                          >
                            <i className="fas fa-eye"></i>
                          </button>
                          
                          {hasPermission('inventory_edit') && (
                            <button
                              className="btn btn-sm btn-warning"
                              onClick={() => handleUpdateInventory(item.material_id)}
                              title="تحديث المخزون"
                            >
                              <i className="fas fa-boxes"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
      
      {/* ملخص المخزون */}
      <div className="card mt-4">
        <div className="card-header">ملخص المخزون</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-4">
              <div className="inventory-summary-item">
                <h5>إجمالي المواد</h5>
                <div className="summary-value">{inventory.length}</div>
              </div>
            </div>
            
            <div className="col-md-4">
              <div className="inventory-summary-item">
                <h5>المواد منخفضة المخزون</h5>
                <div className="summary-value text-danger">
                  {inventory.filter(item => item.quantity < item.min_quantity).length}
                </div>
              </div>
            </div>
            
            <div className="col-md-4">
              <div className="inventory-summary-item">
                <h5>المواد المتوفرة</h5>
                <div className="summary-value text-success">
                  {inventory.filter(item => item.quantity >= item.min_quantity).length}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

module.exports = InventoryList;
