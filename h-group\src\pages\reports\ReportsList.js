const React = require('react');
const { Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const ReportsList = () => {
  const { hasPermission } = useAuth();

  // قائمة التقارير المتاحة
  const reports = [
    {
      id: 'sales',
      title: 'تقرير المبيعات',
      description: 'عرض تقرير مفصل عن المبيعات والإيرادات حسب الفترة الزمنية',
      icon: 'fas fa-chart-line',
      color: 'primary',
      permission: 'reports_sales',
      path: '/reports/sales'
    },
    {
      id: 'expenses',
      title: 'تقرير المصروفات',
      description: 'عرض تقرير مفصل عن مصروفات المصنع حسب الفترة والفئة',
      icon: 'fas fa-money-bill-wave',
      color: 'danger',
      permission: 'reports_expenses',
      path: '/reports/expenses'
    },
    {
      id: 'profit',
      title: 'تقرير الأرباح',
      description: 'عرض تقرير مفصل عن الأرباح والخسائر حسب الفترة الزمنية',
      icon: 'fas fa-chart-pie',
      color: 'success',
      permission: 'reports_profit',
      path: '/reports/profit'
    },
    {
      id: 'inventory',
      title: 'تقرير المخزون',
      description: 'عرض تقرير مفصل عن حالة المخزون واستهلاك المواد الخام',
      icon: 'fas fa-boxes',
      color: 'warning',
      permission: 'reports_inventory',
      path: '/reports/inventory'
    },
    {
      id: 'workers',
      title: 'تقرير العمال',
      description: 'عرض تقرير مفصل عن أداء العمال والمصممين والمدفوعات',
      icon: 'fas fa-users',
      color: 'info',
      permission: 'reports_workers',
      path: '/reports/workers'
    },
    {
      id: 'customers',
      title: 'تقرير العملاء',
      description: 'عرض تقرير مفصل عن العملاء وطلباتهم ومدفوعاتهم',
      icon: 'fas fa-user-tie',
      color: 'secondary',
      permission: 'reports_customers',
      path: '/reports/customers'
    },
    {
      id: 'orders',
      title: 'تقرير الطلبات',
      description: 'عرض تقرير مفصل عن حالة الطلبات ومواعيد التسليم',
      icon: 'fas fa-clipboard-list',
      color: 'dark',
      permission: 'reports_orders',
      path: '/reports/orders'
    },
    {
      id: 'invoices',
      title: 'تقرير الفواتير',
      description: 'عرض تقرير مفصل عن الفواتير والمدفوعات والمستحقات',
      icon: 'fas fa-file-invoice-dollar',
      color: 'primary',
      permission: 'reports_invoices',
      path: '/reports/invoices'
    }
  ];

  // تصفية التقارير حسب الصلاحيات
  const filteredReports = reports.filter(report => hasPermission(report.permission));

  return (
    <div className="reports-page reports-list-page">
      <div className="page-header">
        <h2>التقارير</h2>
      </div>

      {filteredReports.length === 0 ? (
        <div className="alert alert-warning">
          ليس لديك صلاحية للوصول إلى أي تقارير. يرجى التواصل مع مدير النظام.
        </div>
      ) : (
        <div className="reports-grid">
          {filteredReports.map(report => (
            <div key={report.id} className="compact-card report-card">
              <div className="compact-card-header">
                <div className="compact-card-title">
                  <i className={report.icon}></i>
                  {report.title}
                </div>
                <div className={`compact-card-badge badge-${report.color}`}>
                  {report.color === 'primary' ? 'أساسي' :
                   report.color === 'success' ? 'مالي' :
                   report.color === 'warning' ? 'مخزون' :
                   report.color === 'danger' ? 'مصروفات' :
                   report.color === 'info' ? 'موارد' :
                   report.color === 'secondary' ? 'عملاء' : 'عام'}
                </div>
              </div>
              <div className="compact-card-body">
                <p className="compact-card-text">{report.description}</p>
              </div>
              <div className="compact-card-footer">
                <div className="compact-info">
                  <div className="compact-info-item">
                    <i className="fas fa-chart-bar"></i>
                    <span>تقرير تفصيلي</span>
                  </div>
                </div>
                <div className="compact-card-actions">
                  <Link to={report.path} className={`compact-btn compact-btn-primary`}>
                    <i className="fas fa-external-link-alt"></i>
                    عرض
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* قسم التصدير الشامل */}
      {hasPermission('reports_export') && (
        <div className="compact-card">
          <div className="compact-card-header">
            <div className="compact-card-title">
              <i className="fas fa-download"></i>
              تصدير البيانات
            </div>
          </div>
          <div className="compact-card-body">
            <div className="reports-grid">
              <div className="compact-card">
                <div className="compact-card-header">
                  <div className="compact-card-title">
                    <i className="fas fa-file-export"></i>
                    تصدير شامل
                  </div>
                  <div className="compact-card-badge badge-success">
                    Excel
                  </div>
                </div>
                <div className="compact-card-body">
                  <p className="compact-card-text">تصدير جميع بيانات النظام إلى ملفات Excel منفصلة مضغوطة في ملف ZIP واحد.</p>
                </div>
                <div className="compact-card-footer">
                  <div className="compact-card-actions">
                    <button className="compact-btn compact-btn-primary">
                      <i className="fas fa-file-export"></i>
                      تصدير الكل
                    </button>
                  </div>
                </div>
              </div>

              <div className="compact-card">
                <div className="compact-card-header">
                  <div className="compact-card-title">
                    <i className="fas fa-database"></i>
                    نسخة احتياطية
                  </div>
                  <div className="compact-card-badge badge-info">
                    قاعدة بيانات
                  </div>
                </div>
                <div className="compact-card-body">
                  <p className="compact-card-text">إنشاء نسخة احتياطية كاملة لقاعدة البيانات يمكن استعادتها لاحقًا.</p>
                </div>
                <div className="compact-card-footer">
                  <div className="compact-card-actions">
                    <button className="compact-btn compact-btn-primary">
                      <i className="fas fa-database"></i>
                      إنشاء نسخة
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

module.exports = ReportsList;
