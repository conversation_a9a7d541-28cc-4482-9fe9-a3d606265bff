// هذا الملف هو نقطة الدخول لتطبيق React
console.log('تم تحميل ملف renderer.js');

// تحميل bundle.js الذي يحتوي على التطبيق المترجم
document.addEventListener('DOMContentLoaded', () => {
  console.log('بدء تحميل التطبيق...');

  try {
    // إنشاء script tag لتحميل bundle.js
    const script = document.createElement('script');
    script.src = './dist/bundle.js';
    script.onload = () => {
      console.log('تم تحميل bundle.js بنجاح');
    };
    script.onerror = (error) => {
      console.error('خطأ في تحميل bundle.js:', error);
      document.getElementById('app').innerHTML = `
        <div class="alert alert-danger">
          <h2>خطأ في تحميل التطبيق</h2>
          <p>فشل في تحميل ملف bundle.js</p>
        </div>
      `;
    };

    document.head.appendChild(script);

  } catch (error) {
    console.error('خطأ في تحميل التطبيق:', error);
    document.getElementById('app').innerHTML = `
      <div class="alert alert-danger">
        <h2>خطأ في تحميل التطبيق</h2>
        <p>حدث خطأ أثناء تحميل التطبيق: ${error.message}</p>
      </div>
    `;
  }
});
