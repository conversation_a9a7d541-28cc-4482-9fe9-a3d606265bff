{"version": 3, "file": "stream-converter.js", "names": ["jconv", "StreamConverter", "constructor", "inner", "options", "innerEncoding", "toUpperCase", "outerEncoding", "innerBOM", "outerBOM", "writeStarted", "convertInwards", "data", "<PERSON><PERSON><PERSON>", "from", "convert", "convertOutwards", "addListener", "event", "handler", "removeListener", "write", "encoding", "callback", "Function", "undefined", "length", "bomless", "alloc", "copy", "read", "pipe", "destination", "reverseConverter", "close", "on", "type", "chunk", "once", "end", "emit", "value", "module", "exports"], "sources": ["../../../lib/csv/stream-converter.js"], "sourcesContent": ["// =======================================================================================================\n// StreamConverter\n//\n// convert between encoding schemes in a stream\n// Work in Progress - Will complete this at some point\nlet jconv;\n\nclass StreamConverter {\n  constructor(inner, options) {\n    this.inner = inner;\n\n    options = options || {};\n    this.innerEncoding = (options.innerEncoding || 'UTF8').toUpperCase();\n    this.outerEncoding = (options.outerEncoding || 'UTF8').toUpperCase();\n\n    this.innerBOM = options.innerBOM || null;\n    this.outerBOM = options.outerBOM || null;\n\n    this.writeStarted = false;\n  }\n\n  convertInwards(data) {\n    if (data) {\n      if (typeof data === 'string') {\n        data = Buffer.from(data, this.outerEncoding);\n      }\n\n      if (this.innerEncoding !== this.outerEncoding) {\n        data = jconv.convert(data, this.outerEncoding, this.innerEncoding);\n      }\n    }\n\n    return data;\n  }\n\n  convertOutwards(data) {\n    if (typeof data === 'string') {\n      data = Buffer.from(data, this.innerEncoding);\n    }\n\n    if (this.innerEncoding !== this.outerEncoding) {\n      data = jconv.convert(data, this.innerEncoding, this.outerEncoding);\n    }\n    return data;\n  }\n\n  addListener(event, handler) {\n    this.inner.addListener(event, handler);\n  }\n\n  removeListener(event, handler) {\n    this.inner.removeListener(event, handler);\n  }\n\n  write(data, encoding, callback) {\n    if (encoding instanceof Function) {\n      callback = encoding;\n      encoding = undefined;\n    }\n\n    if (!this.writeStarted) {\n      // if inner encoding has BOM, write it now\n      if (this.innerBOM) {\n        this.inner.write(this.innerBOM);\n      }\n\n      // if outer encoding has BOM, delete it now\n      if (this.outerBOM) {\n        if (data.length <= this.outerBOM.length) {\n          if (callback) {\n            callback();\n          }\n          return;\n        }\n        const bomless = Buffer.alloc(data.length - this.outerBOM.length);\n        data.copy(bomless, 0, this.outerBOM.length, data.length);\n        data = bomless;\n      }\n\n      this.writeStarted = true;\n    }\n\n    this.inner.write(\n      this.convertInwards(data),\n      encoding ? this.innerEncoding : undefined,\n      callback\n    );\n  }\n\n  read() {\n    // TBD\n  }\n\n  pipe(destination, options) {\n    const reverseConverter = new StreamConverter(destination, {\n      innerEncoding: this.outerEncoding,\n      outerEncoding: this.innerEncoding,\n      innerBOM: this.outerBOM,\n      outerBOM: this.innerBOM,\n    });\n\n    this.inner.pipe(reverseConverter, options);\n  }\n\n  close() {\n    this.inner.close();\n  }\n\n  on(type, callback) {\n    switch (type) {\n      case 'data':\n        this.inner.on('data', chunk => {\n          callback(this.convertOutwards(chunk));\n        });\n        return this;\n      default:\n        this.inner.on(type, callback);\n        return this;\n    }\n  }\n\n  once(type, callback) {\n    this.inner.once(type, callback);\n  }\n\n  end(chunk, encoding, callback) {\n    this.inner.end(this.convertInwards(chunk), this.innerEncoding, callback);\n  }\n\n  emit(type, value) {\n    this.inner.emit(type, value);\n  }\n}\n\nmodule.exports = StreamConverter;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,KAAK;AAET,MAAMC,eAAe,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAElBC,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI,CAACC,aAAa,GAAG,CAACD,OAAO,CAACC,aAAa,IAAI,MAAM,EAAEC,WAAW,CAAC,CAAC;IACpE,IAAI,CAACC,aAAa,GAAG,CAACH,OAAO,CAACG,aAAa,IAAI,MAAM,EAAED,WAAW,CAAC,CAAC;IAEpE,IAAI,CAACE,QAAQ,GAAGJ,OAAO,CAACI,QAAQ,IAAI,IAAI;IACxC,IAAI,CAACC,QAAQ,GAAGL,OAAO,CAACK,QAAQ,IAAI,IAAI;IAExC,IAAI,CAACC,YAAY,GAAG,KAAK;EAC3B;EAEAC,cAAcA,CAACC,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACR,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5BA,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACF,IAAI,EAAE,IAAI,CAACL,aAAa,CAAC;MAC9C;MAEA,IAAI,IAAI,CAACF,aAAa,KAAK,IAAI,CAACE,aAAa,EAAE;QAC7CK,IAAI,GAAGZ,KAAK,CAACe,OAAO,CAACH,IAAI,EAAE,IAAI,CAACL,aAAa,EAAE,IAAI,CAACF,aAAa,CAAC;MACpE;IACF;IAEA,OAAOO,IAAI;EACb;EAEAI,eAAeA,CAACJ,IAAI,EAAE;IACpB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5BA,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACF,IAAI,EAAE,IAAI,CAACP,aAAa,CAAC;IAC9C;IAEA,IAAI,IAAI,CAACA,aAAa,KAAK,IAAI,CAACE,aAAa,EAAE;MAC7CK,IAAI,GAAGZ,KAAK,CAACe,OAAO,CAACH,IAAI,EAAE,IAAI,CAACP,aAAa,EAAE,IAAI,CAACE,aAAa,CAAC;IACpE;IACA,OAAOK,IAAI;EACb;EAEAK,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAChB,KAAK,CAACc,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;EACxC;EAEAC,cAAcA,CAACF,KAAK,EAAEC,OAAO,EAAE;IAC7B,IAAI,CAAChB,KAAK,CAACiB,cAAc,CAACF,KAAK,EAAEC,OAAO,CAAC;EAC3C;EAEAE,KAAKA,CAACT,IAAI,EAAEU,QAAQ,EAAEC,QAAQ,EAAE;IAC9B,IAAID,QAAQ,YAAYE,QAAQ,EAAE;MAChCD,QAAQ,GAAGD,QAAQ;MACnBA,QAAQ,GAAGG,SAAS;IACtB;IAEA,IAAI,CAAC,IAAI,CAACf,YAAY,EAAE;MACtB;MACA,IAAI,IAAI,CAACF,QAAQ,EAAE;QACjB,IAAI,CAACL,KAAK,CAACkB,KAAK,CAAC,IAAI,CAACb,QAAQ,CAAC;MACjC;;MAEA;MACA,IAAI,IAAI,CAACC,QAAQ,EAAE;QACjB,IAAIG,IAAI,CAACc,MAAM,IAAI,IAAI,CAACjB,QAAQ,CAACiB,MAAM,EAAE;UACvC,IAAIH,QAAQ,EAAE;YACZA,QAAQ,CAAC,CAAC;UACZ;UACA;QACF;QACA,MAAMI,OAAO,GAAGd,MAAM,CAACe,KAAK,CAAChB,IAAI,CAACc,MAAM,GAAG,IAAI,CAACjB,QAAQ,CAACiB,MAAM,CAAC;QAChEd,IAAI,CAACiB,IAAI,CAACF,OAAO,EAAE,CAAC,EAAE,IAAI,CAAClB,QAAQ,CAACiB,MAAM,EAAEd,IAAI,CAACc,MAAM,CAAC;QACxDd,IAAI,GAAGe,OAAO;MAChB;MAEA,IAAI,CAACjB,YAAY,GAAG,IAAI;IAC1B;IAEA,IAAI,CAACP,KAAK,CAACkB,KAAK,CACd,IAAI,CAACV,cAAc,CAACC,IAAI,CAAC,EACzBU,QAAQ,GAAG,IAAI,CAACjB,aAAa,GAAGoB,SAAS,EACzCF,QACF,CAAC;EACH;EAEAO,IAAIA,CAAA,EAAG;IACL;EAAA;EAGFC,IAAIA,CAACC,WAAW,EAAE5B,OAAO,EAAE;IACzB,MAAM6B,gBAAgB,GAAG,IAAIhC,eAAe,CAAC+B,WAAW,EAAE;MACxD3B,aAAa,EAAE,IAAI,CAACE,aAAa;MACjCA,aAAa,EAAE,IAAI,CAACF,aAAa;MACjCG,QAAQ,EAAE,IAAI,CAACC,QAAQ;MACvBA,QAAQ,EAAE,IAAI,CAACD;IACjB,CAAC,CAAC;IAEF,IAAI,CAACL,KAAK,CAAC4B,IAAI,CAACE,gBAAgB,EAAE7B,OAAO,CAAC;EAC5C;EAEA8B,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC/B,KAAK,CAAC+B,KAAK,CAAC,CAAC;EACpB;EAEAC,EAAEA,CAACC,IAAI,EAAEb,QAAQ,EAAE;IACjB,QAAQa,IAAI;MACV,KAAK,MAAM;QACT,IAAI,CAACjC,KAAK,CAACgC,EAAE,CAAC,MAAM,EAAEE,KAAK,IAAI;UAC7Bd,QAAQ,CAAC,IAAI,CAACP,eAAe,CAACqB,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC;QACF,OAAO,IAAI;MACb;QACE,IAAI,CAAClC,KAAK,CAACgC,EAAE,CAACC,IAAI,EAAEb,QAAQ,CAAC;QAC7B,OAAO,IAAI;IACf;EACF;EAEAe,IAAIA,CAACF,IAAI,EAAEb,QAAQ,EAAE;IACnB,IAAI,CAACpB,KAAK,CAACmC,IAAI,CAACF,IAAI,EAAEb,QAAQ,CAAC;EACjC;EAEAgB,GAAGA,CAACF,KAAK,EAAEf,QAAQ,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACpB,KAAK,CAACoC,GAAG,CAAC,IAAI,CAAC5B,cAAc,CAAC0B,KAAK,CAAC,EAAE,IAAI,CAAChC,aAAa,EAAEkB,QAAQ,CAAC;EAC1E;EAEAiB,IAAIA,CAACJ,IAAI,EAAEK,KAAK,EAAE;IAChB,IAAI,CAACtC,KAAK,CAACqC,IAAI,CAACJ,IAAI,EAAEK,KAAK,CAAC;EAC9B;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG1C,eAAe"}