{"version": 3, "file": "index.js", "sources": ["../dom.ts", "../index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_logV6DeprecationWarnings as logV6DeprecationWarnings,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [future, router.future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n"], "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "env", "NODE_ENV", "warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "REACT_ROUTER_VERSION", "window", "__reactRouterVersion", "createBrowserRouter", "routes", "opts", "createRouter", "future", "_extends", "v7_prependBasename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "mapRouteProperties", "dataStrategy", "patchRoutesOnNavigation", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "React", "createContext", "isTransitioning", "displayName", "FetchersContext", "Map", "START_TRANSITION", "startTransitionImpl", "FLUSH_SYNC", "flushSyncImpl", "ReactDOM", "USE_ID", "useIdImpl", "startTransitionSafe", "cb", "flushSyncSafe", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "_ref", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "_ref2", "deletedFetchers", "flushSync", "viewTransitionOpts", "fetchers", "fetcher", "current", "set", "delete", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "location", "v7_partialHydration", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "logV6DeprecationWarnings", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "MemoizedDataRoutes", "DataRoutes", "_ref3", "useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "children", "historyRef", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref5", "HistoryRouter", "_ref6", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref7", "ref", "onClick", "relative", "reloadDocument", "viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "_ref9", "forwardedRef", "fetcher<PERSON>ey", "onSubmit", "props", "_excluded3", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref10", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "fetcherId", "getUniqueFetcherId", "String", "UseSubmit", "currentRouteId", "useRouteId", "options", "fetch", "formEncType", "fromRouteId", "_temp2", "routeContext", "RouteContext", "match", "matches", "slice", "params", "indexValues", "hasNakedIndexParam", "some", "qs", "toString", "route", "index", "joinPaths", "useFetcher", "_temp3", "_route$matches", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "get", "IDLE_FETCHER", "fetcherWithComponents", "useFetchers", "UseFetchers", "from", "_ref11", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp4", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref12", "when", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "currentPath", "nextPath", "matchPath"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAMA,aAAa,GAAmB,KAAK,CAAA;AAClD,MAAMC,cAAc,GAAgB,mCAAmC,CAAA;AAEjE,SAAUC,aAAaA,CAACC,MAAW,EAAA;EACvC,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,CAAA;AAC7D,CAAA;AAEM,SAAUC,eAAeA,CAACF,MAAW,EAAA;AACzC,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,QAAQ,CAAA;AAC3E,CAAA;AAEM,SAAUC,aAAaA,CAACJ,MAAW,EAAA;AACvC,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,MAAM,CAAA;AACzE,CAAA;AAEM,SAAUE,cAAcA,CAACL,MAAW,EAAA;AACxC,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,OAAO,CAAA;AAC1E,CAAA;AAOA,SAASG,eAAeA,CAACC,KAAwB,EAAA;AAC/C,EAAA,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,QAAQ,CAAC,CAAA;AAC7E,CAAA;AAEgB,SAAAC,sBAAsBA,CACpCL,KAAwB,EACxBM,MAAe,EAAA;AAEf,EAAA,OACEN,KAAK,CAACO,MAAM,KAAK,CAAC;AAAI;AACrB,EAAA,CAACD,MAAM,IAAIA,MAAM,KAAK,OAAO,CAAC;AAAI;AACnC,EAAA,CAACP,eAAe,CAACC,KAAK,CAAC;AAAC,GAAA;AAE5B,CAAA;AAUA;;;;;;;;;;;;;;;;;;;;AAoBG;AACa,SAAAQ,kBAAkBA,CAChCC,IAAA,EAA8B;AAAA,EAAA,IAA9BA,IAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,IAAA,GAA4B,EAAE,CAAA;AAAA,GAAA;AAE9B,EAAA,OAAO,IAAIC,eAAe,CACxB,OAAOD,IAAI,KAAK,QAAQ,IACxBE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IACnBA,IAAI,YAAYC,eAAe,GAC3BD,IAAI,GACJI,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAI;AACrC,IAAA,IAAIC,KAAK,GAAGT,IAAI,CAACQ,GAAG,CAAC,CAAA;AACrB,IAAA,OAAOD,IAAI,CAACG,MAAM,CAChBR,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,CAAEC,CAAC,IAAK,CAACJ,GAAG,EAAEI,CAAC,CAAC,CAAC,GAAG,CAAC,CAACJ,GAAG,EAAEC,KAAK,CAAC,CAAC,CACnE,CAAA;GACF,EAAE,EAAyB,CAAC,CAClC,CAAA;AACH,CAAA;AAEgB,SAAAI,0BAA0BA,CACxCC,cAAsB,EACtBC,mBAA2C,EAAA;AAE3C,EAAA,IAAIC,YAAY,GAAGjB,kBAAkB,CAACe,cAAc,CAAC,CAAA;AAErD,EAAA,IAAIC,mBAAmB,EAAE;AACvB;AACA;AACA;AACA;AACA;AACAA,IAAAA,mBAAmB,CAACE,OAAO,CAAC,CAACC,CAAC,EAAEV,GAAG,KAAI;AACrC,MAAA,IAAI,CAACQ,YAAY,CAACG,GAAG,CAACX,GAAG,CAAC,EAAE;QAC1BO,mBAAmB,CAACK,MAAM,CAACZ,GAAG,CAAC,CAACS,OAAO,CAAER,KAAK,IAAI;AAChDO,UAAAA,YAAY,CAACK,MAAM,CAACb,GAAG,EAAEC,KAAK,CAAC,CAAA;AACjC,SAAC,CAAC,CAAA;AACH,OAAA;AACH,KAAC,CAAC,CAAA;AACH,GAAA;AAED,EAAA,OAAOO,YAAY,CAAA;AACrB,CAAA;AAmBA;AACA,IAAIM,0BAA0B,GAAmB,IAAI,CAAA;AAErD,SAASC,4BAA4BA,GAAA;EACnC,IAAID,0BAA0B,KAAK,IAAI,EAAE;IACvC,IAAI;AACF,MAAA,IAAIE,QAAQ,CACVC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;AAC9B;AACA,MAAA,CAAC,CACF,CAAA;AACDJ,MAAAA,0BAA0B,GAAG,KAAK,CAAA;KACnC,CAAC,OAAOK,CAAC,EAAE;AACVL,MAAAA,0BAA0B,GAAG,IAAI,CAAA;AAClC,KAAA;AACF,GAAA;AACD,EAAA,OAAOA,0BAA0B,CAAA;AACnC,CAAA;AAgFA,MAAMM,qBAAqB,GAAqB,IAAIC,GAAG,CAAC,CACtD,mCAAmC,EACnC,qBAAqB,EACrB,YAAY,CACb,CAAC,CAAA;AAEF,SAASC,cAAcA,CAACC,OAAsB,EAAA;EAC5C,IAAIA,OAAO,IAAI,IAAI,IAAI,CAACH,qBAAqB,CAACT,GAAG,CAACY,OAAsB,CAAC,EAAE;AACzEC,IAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,KAAK,EACL,IAAIJ,GAAAA,OAAO,GACejD,4DAAAA,IAAAA,wBAAAA,GAAAA,cAAc,QAAG,CAC5C,GAAA,KAAA,CAAA,CAAA;AAED,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AACD,EAAA,OAAOiD,OAAO,CAAA;AAChB,CAAA;AAEgB,SAAAK,qBAAqBA,CACnCvC,MAAoB,EACpBwC,QAAgB,EAAA;AAQhB,EAAA,IAAIC,MAAc,CAAA;AAClB,EAAA,IAAIC,MAAqB,CAAA;AACzB,EAAA,IAAIR,OAAe,CAAA;AACnB,EAAA,IAAIS,QAA8B,CAAA;AAClC,EAAA,IAAIC,IAAS,CAAA;AAEb,EAAA,IAAIrD,aAAa,CAACS,MAAM,CAAC,EAAE;AACzB;AACA;AACA;AACA,IAAA,IAAI6C,IAAI,GAAG7C,MAAM,CAAC8C,YAAY,CAAC,QAAQ,CAAC,CAAA;IACxCJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI,CAAA;IACpDC,MAAM,GAAGzC,MAAM,CAAC8C,YAAY,CAAC,QAAQ,CAAC,IAAI9D,aAAa,CAAA;IACvDkD,OAAO,GAAGD,cAAc,CAACjC,MAAM,CAAC8C,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI7D,cAAc,CAAA;AAE1E0D,IAAAA,QAAQ,GAAG,IAAIhB,QAAQ,CAAC3B,MAAM,CAAC,CAAA;GAChC,MAAM,IACLX,eAAe,CAACW,MAAM,CAAC,IACtBR,cAAc,CAACQ,MAAM,CAAC,KACpBA,MAAM,CAACgD,IAAI,KAAK,QAAQ,IAAIhD,MAAM,CAACgD,IAAI,KAAK,OAAO,CAAE,EACxD;AACA,IAAA,IAAIC,IAAI,GAAGjD,MAAM,CAACiD,IAAI,CAAA;IAEtB,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAA,sEACuD,CACrE,CAAA;AACF,KAAA;AAED;AAEA;AACA;AACA;AACA,IAAA,IAAIL,IAAI,GAAG7C,MAAM,CAAC8C,YAAY,CAAC,YAAY,CAAC,IAAIG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,CAAA;IAC3EJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI,CAAA;AAEpDC,IAAAA,MAAM,GACJzC,MAAM,CAAC8C,YAAY,CAAC,YAAY,CAAC,IACjCG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,IAC3B9D,aAAa,CAAA;IACfkD,OAAO,GACLD,cAAc,CAACjC,MAAM,CAAC8C,YAAY,CAAC,aAAa,CAAC,CAAC,IAClDb,cAAc,CAACgB,IAAI,CAACH,YAAY,CAAC,SAAS,CAAC,CAAC,IAC5C7D,cAAc,CAAA;AAEhB;AACA0D,IAAAA,QAAQ,GAAG,IAAIhB,QAAQ,CAACsB,IAAI,EAAEjD,MAAM,CAAC,CAAA;AAErC;AACA;AACA;AACA;AACA,IAAA,IAAI,CAAC0B,4BAA4B,EAAE,EAAE;MACnC,IAAI;QAAEyB,IAAI;QAAEH,IAAI;AAAEpC,QAAAA,KAAAA;AAAK,OAAE,GAAGZ,MAAM,CAAA;MAClC,IAAIgD,IAAI,KAAK,OAAO,EAAE;AACpB,QAAA,IAAII,MAAM,GAAGD,IAAI,GAAMA,IAAI,SAAM,EAAE,CAAA;AACnCR,QAAAA,QAAQ,CAACnB,MAAM,CAAI4B,MAAM,GAAA,GAAA,EAAK,GAAG,CAAC,CAAA;AAClCT,QAAAA,QAAQ,CAACnB,MAAM,CAAI4B,MAAM,GAAA,GAAA,EAAK,GAAG,CAAC,CAAA;OACnC,MAAM,IAAID,IAAI,EAAE;AACfR,QAAAA,QAAQ,CAACnB,MAAM,CAAC2B,IAAI,EAAEvC,KAAK,CAAC,CAAA;AAC7B,OAAA;AACF,KAAA;AACF,GAAA,MAAM,IAAI1B,aAAa,CAACc,MAAM,CAAC,EAAE;AAChC,IAAA,MAAM,IAAIkD,KAAK,CACb,yDAAA,GAAA,+BAC+B,CAChC,CAAA;AACF,GAAA,MAAM;AACLT,IAAAA,MAAM,GAAGzD,aAAa,CAAA;AACtB0D,IAAAA,MAAM,GAAG,IAAI,CAAA;AACbR,IAAAA,OAAO,GAAGjD,cAAc,CAAA;AACxB2D,IAAAA,IAAI,GAAG5C,MAAM,CAAA;AACd,GAAA;AAED;AACA,EAAA,IAAI2C,QAAQ,IAAIT,OAAO,KAAK,YAAY,EAAE;AACxCU,IAAAA,IAAI,GAAGD,QAAQ,CAAA;AACfA,IAAAA,QAAQ,GAAGU,SAAS,CAAA;AACrB,GAAA;EAED,OAAO;IAAEX,MAAM;AAAED,IAAAA,MAAM,EAAEA,MAAM,CAACnD,WAAW,EAAE;IAAE4C,OAAO;IAAES,QAAQ;AAAEC,IAAAA,IAAAA;GAAM,CAAA;AAC1E;;;;;ACvGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,MAAAU,oBAAA,GAAA,GAAA,CAAA;AAEA,IAAI;EACFC,MAAM,CAACC,oBAAoB,GAAGF,oBAAoB,CAAA;AACnD,CAAA,CAAC,OAAOxB,CAAC,EAAE;AACV;AAAA,CAAA;AAgBc,SAAA2B,mBAAmBA,CACjCC,MAAqB,EACrBC,IAAoB,EAAA;AAEpB,EAAA,OAAOC,YAAY,CAAC;AAClBpB,IAAAA,QAAQ,EAAEmB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEnB,QAAQ;AACxBqB,IAAAA,MAAM,EAAAC,QAAA,CAAA,EAAA,EACDH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEE,MAAM,EAAA;AACfE,MAAAA,kBAAkB,EAAE,IAAA;KACrB,CAAA;IACDC,OAAO,EAAEC,oBAAoB,CAAC;AAAEV,MAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;AAAM,KAAE,CAAC;IACvDW,aAAa,EAAE,CAAAP,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEO,aAAa,KAAIC,kBAAkB,EAAE;IAC1DT,MAAM;wBACNU,yBAAkB;AAClBC,IAAAA,YAAY,EAAEV,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEU,YAAY;AAChCC,IAAAA,uBAAuB,EAAEX,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEW,uBAAuB;AACtDf,IAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;GACf,CAAC,CAACgB,UAAU,EAAE,CAAA;AACjB,CAAA;AAEgB,SAAAC,gBAAgBA,CAC9Bd,MAAqB,EACrBC,IAAoB,EAAA;AAEpB,EAAA,OAAOC,YAAY,CAAC;AAClBpB,IAAAA,QAAQ,EAAEmB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEnB,QAAQ;AACxBqB,IAAAA,MAAM,EAAAC,QAAA,CAAA,EAAA,EACDH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEE,MAAM,EAAA;AACfE,MAAAA,kBAAkB,EAAE,IAAA;KACrB,CAAA;IACDC,OAAO,EAAES,iBAAiB,CAAC;AAAElB,MAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;AAAM,KAAE,CAAC;IACpDW,aAAa,EAAE,CAAAP,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEO,aAAa,KAAIC,kBAAkB,EAAE;IAC1DT,MAAM;wBACNU,yBAAkB;AAClBC,IAAAA,YAAY,EAAEV,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEU,YAAY;AAChCC,IAAAA,uBAAuB,EAAEX,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEW,uBAAuB;AACtDf,IAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;GACf,CAAC,CAACgB,UAAU,EAAE,CAAA;AACjB,CAAA;AAEA,SAASJ,kBAAkBA,GAAA;AAAA,EAAA,IAAAO,OAAA,CAAA;EACzB,IAAIC,KAAK,IAAAD,OAAA,GAAGnB,MAAM,KAANmB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAQE,2BAA2B,CAAA;AAC/C,EAAA,IAAID,KAAK,IAAIA,KAAK,CAACE,MAAM,EAAE;IACzBF,KAAK,GAAAb,QAAA,CAAA,EAAA,EACAa,KAAK,EAAA;AACRE,MAAAA,MAAM,EAAEC,iBAAiB,CAACH,KAAK,CAACE,MAAM,CAAA;KACvC,CAAA,CAAA;AACF,GAAA;AACD,EAAA,OAAOF,KAAK,CAAA;AACd,CAAA;AAEA,SAASG,iBAAiBA,CACxBD,MAAsC,EAAA;AAEtC,EAAA,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI,CAAA;AACxB,EAAA,IAAIE,OAAO,GAAGxE,MAAM,CAACwE,OAAO,CAACF,MAAM,CAAC,CAAA;EACpC,IAAIG,UAAU,GAAmC,EAAE,CAAA;EACnD,KAAK,IAAI,CAACrE,GAAG,EAAEsE,GAAG,CAAC,IAAIF,OAAO,EAAE;AAC9B;AACA;AACA,IAAA,IAAIE,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAACrE,GAAG,CAAC,GAAG,IAAIwE,wBAAiB,CACrCF,GAAG,CAACG,MAAM,EACVH,GAAG,CAACI,UAAU,EACdJ,GAAG,CAACK,IAAI,EACRL,GAAG,CAACM,QAAQ,KAAK,IAAI,CACtB,CAAA;KACF,MAAM,IAAIN,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;AACxC;MACA,IAAID,GAAG,CAACO,SAAS,EAAE;AACjB,QAAA,IAAIC,gBAAgB,GAAGlC,MAAM,CAAC0B,GAAG,CAACO,SAAS,CAAC,CAAA;AAC5C,QAAA,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;AACF;YACA,IAAIC,KAAK,GAAG,IAAID,gBAAgB,CAACR,GAAG,CAACU,OAAO,CAAC,CAAA;AAC7C;AACA;YACAD,KAAK,CAACE,KAAK,GAAG,EAAE,CAAA;AAChBZ,YAAAA,UAAU,CAACrE,GAAG,CAAC,GAAG+E,KAAK,CAAA;WACxB,CAAC,OAAO5D,CAAC,EAAE;AACV;AAAA,WAAA;AAEH,SAAA;AACF,OAAA;AAED,MAAA,IAAIkD,UAAU,CAACrE,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAI+E,KAAK,GAAG,IAAIxC,KAAK,CAAC+B,GAAG,CAACU,OAAO,CAAC,CAAA;AAClC;AACA;QACAD,KAAK,CAACE,KAAK,GAAG,EAAE,CAAA;AAChBZ,QAAAA,UAAU,CAACrE,GAAG,CAAC,GAAG+E,KAAK,CAAA;AACxB,OAAA;AACF,KAAA,MAAM;AACLV,MAAAA,UAAU,CAACrE,GAAG,CAAC,GAAGsE,GAAG,CAAA;AACtB,KAAA;AACF,GAAA;AACD,EAAA,OAAOD,UAAU,CAAA;AACnB,CAAA;AAmBA,MAAMa,qBAAqB,gBAAGC,KAAK,CAACC,aAAa,CAA8B;AAC7EC,EAAAA,eAAe,EAAE,KAAA;AAClB,CAAA,EAAC;AACF,IAAA7D,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXwD,qBAAqB,CAACI,WAAW,GAAG,gBAAgB,CAAA;AACrD,CAAA;AAOKC,MAAAA,eAAe,gBAAGJ,KAAK,CAACC,aAAa,CAAwB,IAAII,GAAG,EAAE,EAAC;AAC7E,IAAAhE,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACX6D,eAAe,CAACD,WAAW,GAAG,UAAU,CAAA;AACzC,CAAA;AAID;AAEA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;AAoBE;AACF,MAAMG,gBAAgB,GAAG,iBAAiB,CAAA;AAC1C,MAAMC,mBAAmB,GAAGP,KAAK,CAACM,gBAAgB,CAAC,CAAA;AACnD,MAAME,UAAU,GAAG,WAAW,CAAA;AAC9B,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,CAAC,CAAA;AAC1C,MAAMG,MAAM,GAAG,OAAO,CAAA;AACtB,MAAMC,SAAS,GAAGZ,KAAK,CAACW,MAAM,CAAC,CAAA;AAE/B,SAASE,mBAAmBA,CAACC,EAAc,EAAA;AACzC,EAAA,IAAIP,mBAAmB,EAAE;IACvBA,mBAAmB,CAACO,EAAE,CAAC,CAAA;AACxB,GAAA,MAAM;AACLA,IAAAA,EAAE,EAAE,CAAA;AACL,GAAA;AACH,CAAA;AAEA,SAASC,aAAaA,CAACD,EAAc,EAAA;AACnC,EAAA,IAAIL,aAAa,EAAE;IACjBA,aAAa,CAACK,EAAE,CAAC,CAAA;AAClB,GAAA,MAAM;AACLA,IAAAA,EAAE,EAAE,CAAA;AACL,GAAA;AACH,CAAA;AASA,MAAME,QAAQ,CAAA;AAOZC,EAAAA,WAAAA,GAAA;IANA,IAAM,CAAA3B,MAAA,GAAwC,SAAS,CAAA;IAOrD,IAAI,CAAC4B,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;AAC7C,MAAA,IAAI,CAACD,OAAO,GAAItG,KAAK,IAAI;AACvB,QAAA,IAAI,IAAI,CAACwE,MAAM,KAAK,SAAS,EAAE;UAC7B,IAAI,CAACA,MAAM,GAAG,UAAU,CAAA;UACxB8B,OAAO,CAACtG,KAAK,CAAC,CAAA;AACf,SAAA;OACF,CAAA;AACD,MAAA,IAAI,CAACuG,MAAM,GAAIC,MAAM,IAAI;AACvB,QAAA,IAAI,IAAI,CAAChC,MAAM,KAAK,SAAS,EAAE;UAC7B,IAAI,CAACA,MAAM,GAAG,UAAU,CAAA;UACxB+B,MAAM,CAACC,MAAM,CAAC,CAAA;AACf,SAAA;OACF,CAAA;AACH,KAAC,CAAC,CAAA;AACJ,GAAA;AACD,CAAA;AAED;;AAEG;AACG,SAAUC,cAAcA,CAAAC,IAAA,EAIR;EAAA,IAJS;IAC7BC,eAAe;IACfC,MAAM;AACN3D,IAAAA,MAAAA;AACoB,GAAA,GAAAyD,IAAA,CAAA;AACpB,EAAA,IAAI,CAAC3C,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAACF,MAAM,CAAC7C,KAAK,CAAC,CAAA;EACxD,IAAI,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAG9B,KAAK,CAAC4B,QAAQ,EAAe,CAAA;EACnE,IAAI,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGhC,KAAK,CAAC4B,QAAQ,CAA8B;AAC1E1B,IAAAA,eAAe,EAAE,KAAA;AAClB,GAAA,CAAC,CAAA;EACF,IAAI,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGlC,KAAK,CAAC4B,QAAQ,EAAkB,CAAA;EAChE,IAAI,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGpC,KAAK,CAAC4B,QAAQ,EAAkB,CAAA;EAClE,IAAI,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGtC,KAAK,CAAC4B,QAAQ,EAIhD,CAAA;EACJ,IAAIW,WAAW,GAAGvC,KAAK,CAACwC,MAAM,CAAmB,IAAInC,GAAG,EAAE,CAAC,CAAA;EAC3D,IAAI;AAAEoC,IAAAA,kBAAAA;AAAkB,GAAE,GAAG1E,MAAM,IAAI,EAAE,CAAA;AAEzC,EAAA,IAAI2E,oBAAoB,GAAG1C,KAAK,CAAC2C,WAAW,CACzC7B,EAAc,IAAI;AACjB,IAAA,IAAI2B,kBAAkB,EAAE;MACtB5B,mBAAmB,CAACC,EAAE,CAAC,CAAA;AACxB,KAAA,MAAM;AACLA,MAAAA,EAAE,EAAE,CAAA;AACL,KAAA;AACH,GAAC,EACD,CAAC2B,kBAAkB,CAAC,CACrB,CAAA;EAED,IAAIG,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC9B,CACEE,QAAqB,EAAAC,KAAA,KAMnB;IAAA,IALF;MACEC,eAAe;AACfC,MAAAA,SAAS,EAAEA,SAAS;AACpBC,MAAAA,kBAAkB,EAAEA,kBAAAA;AACrB,KAAA,GAAAH,KAAA,CAAA;IAEDD,QAAQ,CAACK,QAAQ,CAAC5H,OAAO,CAAC,CAAC6H,OAAO,EAAEtI,GAAG,KAAI;AACzC,MAAA,IAAIsI,OAAO,CAAC3D,IAAI,KAAKjC,SAAS,EAAE;QAC9BgF,WAAW,CAACa,OAAO,CAACC,GAAG,CAACxI,GAAG,EAAEsI,OAAO,CAAC3D,IAAI,CAAC,CAAA;AAC3C,OAAA;AACH,KAAC,CAAC,CAAA;AACFuD,IAAAA,eAAe,CAACzH,OAAO,CAAET,GAAG,IAAK0H,WAAW,CAACa,OAAO,CAACE,MAAM,CAACzI,GAAG,CAAC,CAAC,CAAA;IAEjE,IAAI0I,2BAA2B,GAC7B7B,MAAM,CAACjE,MAAM,IAAI,IAAI,IACrBiE,MAAM,CAACjE,MAAM,CAAC3B,QAAQ,IAAI,IAAI,IAC9B,OAAO4F,MAAM,CAACjE,MAAM,CAAC3B,QAAQ,CAAC0H,mBAAmB,KAAK,UAAU,CAAA;AAElE;AACA;AACA,IAAA,IAAI,CAACP,kBAAkB,IAAIM,2BAA2B,EAAE;AACtD,MAAA,IAAIP,SAAS,EAAE;AACbjC,QAAAA,aAAa,CAAC,MAAMY,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AAC5C,OAAA,MAAM;AACLH,QAAAA,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AACnD,OAAA;AACD,MAAA,OAAA;AACD,KAAA;AAED;AACA,IAAA,IAAIG,SAAS,EAAE;AACb;AACAjC,MAAAA,aAAa,CAAC,MAAK;AACjB;AACA,QAAA,IAAIoB,UAAU,EAAE;AACdF,UAAAA,SAAS,IAAIA,SAAS,CAACb,OAAO,EAAE,CAAA;UAChCe,UAAU,CAACsB,cAAc,EAAE,CAAA;AAC5B,SAAA;AACDzB,QAAAA,YAAY,CAAC;AACX9B,UAAAA,eAAe,EAAE,IAAI;AACrB8C,UAAAA,SAAS,EAAE,IAAI;UACfU,eAAe,EAAET,kBAAkB,CAACS,eAAe;UACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;AAClC,SAAA,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AAEF;MACA,IAAIC,CAAC,GAAGlC,MAAM,CAACjE,MAAO,CAAC3B,QAAQ,CAAC0H,mBAAmB,CAAC,MAAK;AACvDzC,QAAAA,aAAa,CAAC,MAAMY,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AAC7C,OAAC,CAAC,CAAA;AAEF;AACAe,MAAAA,CAAC,CAACC,QAAQ,CAACC,OAAO,CAAC,MAAK;AACtB/C,QAAAA,aAAa,CAAC,MAAK;UACjBmB,YAAY,CAAC3E,SAAS,CAAC,CAAA;UACvB6E,aAAa,CAAC7E,SAAS,CAAC,CAAA;UACxBuE,eAAe,CAACvE,SAAS,CAAC,CAAA;AAC1ByE,UAAAA,YAAY,CAAC;AAAE9B,YAAAA,eAAe,EAAE,KAAA;AAAK,WAAE,CAAC,CAAA;AAC1C,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AAEFa,MAAAA,aAAa,CAAC,MAAMqB,aAAa,CAACwB,CAAC,CAAC,CAAC,CAAA;AACrC,MAAA,OAAA;AACD,KAAA;AAED;AACA,IAAA,IAAIzB,UAAU,EAAE;AACd;AACA;AACAF,MAAAA,SAAS,IAAIA,SAAS,CAACb,OAAO,EAAE,CAAA;MAChCe,UAAU,CAACsB,cAAc,EAAE,CAAA;AAC3BnB,MAAAA,eAAe,CAAC;AACdzD,QAAAA,KAAK,EAAEgE,QAAQ;QACfa,eAAe,EAAET,kBAAkB,CAACS,eAAe;QACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;AAClC,OAAA,CAAC,CAAA;AACH,KAAA,MAAM;AACL;MACA7B,eAAe,CAACe,QAAQ,CAAC,CAAA;AACzBb,MAAAA,YAAY,CAAC;AACX9B,QAAAA,eAAe,EAAE,IAAI;AACrB8C,QAAAA,SAAS,EAAE,KAAK;QAChBU,eAAe,EAAET,kBAAkB,CAACS,eAAe;QACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;AAClC,OAAA,CAAC,CAAA;AACH,KAAA;AACH,GAAC,EACD,CAACjC,MAAM,CAACjE,MAAM,EAAE0E,UAAU,EAAEF,SAAS,EAAEM,WAAW,EAAEG,oBAAoB,CAAC,CAC1E,CAAA;AAED;AACA;AACA1C,EAAAA,KAAK,CAAC+D,eAAe,CAAC,MAAMrC,MAAM,CAACsC,SAAS,CAACpB,QAAQ,CAAC,EAAE,CAAClB,MAAM,EAAEkB,QAAQ,CAAC,CAAC,CAAA;AAE3E;AACA;EACA5C,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIlC,SAAS,CAAC7B,eAAe,IAAI,CAAC6B,SAAS,CAACiB,SAAS,EAAE;AACrDd,MAAAA,YAAY,CAAC,IAAIlB,QAAQ,EAAQ,CAAC,CAAA;AACnC,KAAA;AACH,GAAC,EAAE,CAACe,SAAS,CAAC,CAAC,CAAA;AAEf;AACA;AACA;EACA/B,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnB,IAAA,IAAIhC,SAAS,IAAIJ,YAAY,IAAIH,MAAM,CAACjE,MAAM,EAAE;MAC9C,IAAIoF,QAAQ,GAAGhB,YAAY,CAAA;AAC3B,MAAA,IAAIqC,aAAa,GAAGjC,SAAS,CAACf,OAAO,CAAA;MACrC,IAAIiB,UAAU,GAAGT,MAAM,CAACjE,MAAM,CAAC3B,QAAQ,CAAC0H,mBAAmB,CAAC,YAAW;AACrEd,QAAAA,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;AAClD,QAAA,MAAMqB,aAAa,CAAA;AACrB,OAAC,CAAC,CAAA;AACF/B,MAAAA,UAAU,CAAC0B,QAAQ,CAACC,OAAO,CAAC,MAAK;QAC/B5B,YAAY,CAAC3E,SAAS,CAAC,CAAA;QACvB6E,aAAa,CAAC7E,SAAS,CAAC,CAAA;QACxBuE,eAAe,CAACvE,SAAS,CAAC,CAAA;AAC1ByE,QAAAA,YAAY,CAAC;AAAE9B,UAAAA,eAAe,EAAE,KAAA;AAAK,SAAE,CAAC,CAAA;AAC1C,OAAC,CAAC,CAAA;MACFkC,aAAa,CAACD,UAAU,CAAC,CAAA;AAC1B,KAAA;AACH,GAAC,EAAE,CAACO,oBAAoB,EAAEb,YAAY,EAAEI,SAAS,EAAEP,MAAM,CAACjE,MAAM,CAAC,CAAC,CAAA;AAElE;AACA;EACAuC,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnB,IAAA,IACEhC,SAAS,IACTJ,YAAY,IACZhD,KAAK,CAACsF,QAAQ,CAACtJ,GAAG,KAAKgH,YAAY,CAACsC,QAAQ,CAACtJ,GAAG,EAChD;MACAoH,SAAS,CAACb,OAAO,EAAE,CAAA;AACpB,KAAA;AACH,GAAC,EAAE,CAACa,SAAS,EAAEE,UAAU,EAAEtD,KAAK,CAACsF,QAAQ,EAAEtC,YAAY,CAAC,CAAC,CAAA;AAEzD;AACA;EACA7B,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnB,IAAA,IAAI,CAAClC,SAAS,CAAC7B,eAAe,IAAImC,YAAY,EAAE;AAC9CP,MAAAA,eAAe,CAACO,YAAY,CAACxD,KAAK,CAAC,CAAA;AACnCmD,MAAAA,YAAY,CAAC;AACX9B,QAAAA,eAAe,EAAE,IAAI;AACrB8C,QAAAA,SAAS,EAAE,KAAK;QAChBU,eAAe,EAAErB,YAAY,CAACqB,eAAe;QAC7CC,YAAY,EAAEtB,YAAY,CAACsB,YAAAA;AAC5B,OAAA,CAAC,CAAA;MACFrB,eAAe,CAAC/E,SAAS,CAAC,CAAA;AAC3B,KAAA;GACF,EAAE,CAACwE,SAAS,CAAC7B,eAAe,EAAEmC,YAAY,CAAC,CAAC,CAAA;EAE7CrC,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB5H,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACLiF,eAAe,IAAI,IAAI,IAAI,CAACC,MAAM,CAAC3D,MAAM,CAACqG,mBAAmB,EAC7D,8DAA8D,GAC5D,kEAAkE,CACrE,GAAA,KAAA,CAAA,CAAA;AACD;AACA;GACD,EAAE,EAAE,CAAC,CAAA;AAEN,EAAA,IAAIC,SAAS,GAAGrE,KAAK,CAACsE,OAAO,CAAC,MAAgB;IAC5C,OAAO;MACLC,UAAU,EAAE7C,MAAM,CAAC6C,UAAU;MAC7BC,cAAc,EAAE9C,MAAM,CAAC8C,cAAc;MACrCC,EAAE,EAAGC,CAAC,IAAKhD,MAAM,CAACiD,QAAQ,CAACD,CAAC,CAAC;AAC7BE,MAAAA,IAAI,EAAEA,CAACC,EAAE,EAAEhG,KAAK,EAAEhB,IAAI,KACpB6D,MAAM,CAACiD,QAAQ,CAACE,EAAE,EAAE;QAClBhG,KAAK;AACLiG,QAAAA,kBAAkB,EAAEjH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEiH,kBAAAA;OAC3B,CAAC;AACJC,MAAAA,OAAO,EAAEA,CAACF,EAAE,EAAEhG,KAAK,EAAEhB,IAAI,KACvB6D,MAAM,CAACiD,QAAQ,CAACE,EAAE,EAAE;AAClBE,QAAAA,OAAO,EAAE,IAAI;QACblG,KAAK;AACLiG,QAAAA,kBAAkB,EAAEjH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEiH,kBAAAA;OAC3B,CAAA;KACJ,CAAA;AACH,GAAC,EAAE,CAACpD,MAAM,CAAC,CAAC,CAAA;AAEZ,EAAA,IAAIhF,QAAQ,GAAGgF,MAAM,CAAChF,QAAQ,IAAI,GAAG,CAAA;AAErC,EAAA,IAAIsI,iBAAiB,GAAGhF,KAAK,CAACsE,OAAO,CACnC,OAAO;IACL5C,MAAM;IACN2C,SAAS;AACTY,IAAAA,MAAM,EAAE,KAAK;AACbvI,IAAAA,QAAAA;GACD,CAAC,EACF,CAACgF,MAAM,EAAE2C,SAAS,EAAE3H,QAAQ,CAAC,CAC9B,CAAA;AAED,EAAA,IAAIwI,YAAY,GAAGlF,KAAK,CAACsE,OAAO,CAC9B,OAAO;AACLa,IAAAA,oBAAoB,EAAEzD,MAAM,CAAC3D,MAAM,CAACoH,oBAAAA;GACrC,CAAC,EACF,CAACzD,MAAM,CAAC3D,MAAM,CAACoH,oBAAoB,CAAC,CACrC,CAAA;EAEDnF,KAAK,CAACiE,SAAS,CACb,MAAMmB,+BAAwB,CAACrH,MAAM,EAAE2D,MAAM,CAAC3D,MAAM,CAAC,EACrD,CAACA,MAAM,EAAE2D,MAAM,CAAC3D,MAAM,CAAC,CACxB,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,oBACEiC,KAAA,CAAAjE,aAAA,CAAAiE,KAAA,CAAAqF,QAAA,EAAA,IAAA,eACErF,KAAA,CAAAjE,aAAA,CAACuJ,wBAAiB,CAACC,QAAQ,EAAC;AAAAzK,IAAAA,KAAK,EAAEkK,iBAAAA;GAAiB,eAClDhF,KAAA,CAAAjE,aAAA,CAACyJ,6BAAsB,CAACD,QAAQ,EAAC;AAAAzK,IAAAA,KAAK,EAAE+D,KAAAA;GAAK,eAC3CmB,KAAC,CAAAjE,aAAA,CAAAqE,eAAe,CAACmF,QAAQ,EAAA;IAACzK,KAAK,EAAEyH,WAAW,CAACa,OAAAA;GAAO,eAClDpD,KAAA,CAAAjE,aAAA,CAACgE,qBAAqB,CAACwF,QAAQ,EAAC;AAAAzK,IAAAA,KAAK,EAAEiH,SAAAA;AAAS,GAAA,eAC9C/B,KAAA,CAAAjE,aAAA,CAAC0J,MAAM,EAAA;AACL/I,IAAAA,QAAQ,EAAEA,QAAQ;IAClByH,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAAC8G,aAAa;AACnCtB,IAAAA,SAAS,EAAEA,SAAS;AACpBtG,IAAAA,MAAM,EAAEmH,YAAAA;AAEP,GAAA,EAAArG,KAAK,CAAC+G,WAAW,IAAIlE,MAAM,CAAC3D,MAAM,CAACqG,mBAAmB,gBACrDpE,KAAA,CAAAjE,aAAA,CAAC8J,kBAAkB,EACjB;IAAAjI,MAAM,EAAE8D,MAAM,CAAC9D,MAAM;IACrBG,MAAM,EAAE2D,MAAM,CAAC3D,MAAM;AACrBc,IAAAA,KAAK,EAAEA,KAAAA;GAAK,CACZ,GAEF4C,eACD,CACM,CACsB,CACR,CACK,CACP,EAC5B,IAAI,CACJ,CAAA;AAEP,CAAA;AAEA;AACA,MAAMoE,kBAAkB,gBAAG7F,KAAK,CAACpF,IAAI,CAACkL,UAAU,CAAC,CAAA;AAEjD,SAASA,UAAUA,CAAAC,KAAA,EAQlB;EAAA,IARmB;IAClBnI,MAAM;IACNG,MAAM;AACNc,IAAAA,KAAAA;AAKD,GAAA,GAAAkH,KAAA,CAAA;EACC,OAAOC,oBAAa,CAACpI,MAAM,EAAEL,SAAS,EAAEsB,KAAK,EAAEd,MAAM,CAAC,CAAA;AACxD,CAAA;AASA;;AAEG;AACG,SAAUkI,aAAaA,CAAAC,KAAA,EAKR;EAAA,IALS;IAC5BxJ,QAAQ;IACRyJ,QAAQ;IACRpI,MAAM;AACNN,IAAAA,MAAAA;AACmB,GAAA,GAAAyI,KAAA,CAAA;AACnB,EAAA,IAAIE,UAAU,GAAGpG,KAAK,CAACwC,MAAM,EAAkB,CAAA;AAC/C,EAAA,IAAI4D,UAAU,CAAChD,OAAO,IAAI,IAAI,EAAE;AAC9BgD,IAAAA,UAAU,CAAChD,OAAO,GAAGjF,oBAAoB,CAAC;MAAEV,MAAM;AAAE4I,MAAAA,QAAQ,EAAE,IAAA;AAAI,KAAE,CAAC,CAAA;AACtE,GAAA;AAED,EAAA,IAAInI,OAAO,GAAGkI,UAAU,CAAChD,OAAO,CAAA;EAChC,IAAI,CAACvE,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC;IACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;IACtBuH,QAAQ,EAAEjG,OAAO,CAACiG,QAAAA;AACnB,GAAA,CAAC,CAAA;EACF,IAAI;AAAE1B,IAAAA,kBAAAA;AAAkB,GAAE,GAAG1E,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAI6E,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAI;AAC3DJ,IAAAA,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CAAC,CACnC,CAAA;AAEDzC,EAAAA,KAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC,CAAA;AAE1E5C,EAAAA,KAAK,CAACiE,SAAS,CAAC,MAAMmB,+BAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;AAEjE,EAAA,oBACEiC,KAAA,CAAAjE,aAAA,CAAC0J,MAAM,EAAA;AACL/I,IAAAA,QAAQ,EAAEA,QAAQ;AAClByJ,IAAAA,QAAQ,EAAEA,QAAQ;IAClBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAM;AAC5ByH,IAAAA,SAAS,EAAEnG,OAAO;AAClBH,IAAAA,MAAM,EAAEA,MAAAA;AAAM,GAAA,CACd,CAAA;AAEN,CAAA;AASA;;;AAGG;AACG,SAAUwI,UAAUA,CAAAC,KAAA,EAKR;EAAA,IALS;IACzB9J,QAAQ;IACRyJ,QAAQ;IACRpI,MAAM;AACNN,IAAAA,MAAAA;AACgB,GAAA,GAAA+I,KAAA,CAAA;AAChB,EAAA,IAAIJ,UAAU,GAAGpG,KAAK,CAACwC,MAAM,EAAe,CAAA;AAC5C,EAAA,IAAI4D,UAAU,CAAChD,OAAO,IAAI,IAAI,EAAE;AAC9BgD,IAAAA,UAAU,CAAChD,OAAO,GAAGzE,iBAAiB,CAAC;MAAElB,MAAM;AAAE4I,MAAAA,QAAQ,EAAE,IAAA;AAAI,KAAE,CAAC,CAAA;AACnE,GAAA;AAED,EAAA,IAAInI,OAAO,GAAGkI,UAAU,CAAChD,OAAO,CAAA;EAChC,IAAI,CAACvE,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC;IACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;IACtBuH,QAAQ,EAAEjG,OAAO,CAACiG,QAAAA;AACnB,GAAA,CAAC,CAAA;EACF,IAAI;AAAE1B,IAAAA,kBAAAA;AAAkB,GAAE,GAAG1E,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAI6E,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAI;AAC3DJ,IAAAA,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CAAC,CACnC,CAAA;AAEDzC,EAAAA,KAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC,CAAA;AAE1E5C,EAAAA,KAAK,CAACiE,SAAS,CAAC,MAAMmB,+BAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;AAEjE,EAAA,oBACEiC,KAAA,CAAAjE,aAAA,CAAC0J,MAAM,EAAA;AACL/I,IAAAA,QAAQ,EAAEA,QAAQ;AAClByJ,IAAAA,QAAQ,EAAEA,QAAQ;IAClBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAM;AAC5ByH,IAAAA,SAAS,EAAEnG,OAAO;AAClBH,IAAAA,MAAM,EAAEA,MAAAA;AAAM,GAAA,CACd,CAAA;AAEN,CAAA;AASA;;;;;AAKG;AACH,SAAS0I,aAAaA,CAAAC,KAAA,EAKD;EAAA,IALE;IACrBhK,QAAQ;IACRyJ,QAAQ;IACRpI,MAAM;AACNG,IAAAA,OAAAA;AACmB,GAAA,GAAAwI,KAAA,CAAA;EACnB,IAAI,CAAC7H,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC;IACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;IACtBuH,QAAQ,EAAEjG,OAAO,CAACiG,QAAAA;AACnB,GAAA,CAAC,CAAA;EACF,IAAI;AAAE1B,IAAAA,kBAAAA;AAAkB,GAAE,GAAG1E,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAI6E,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAI;AAC3DJ,IAAAA,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CAAC,CACnC,CAAA;AAEDzC,EAAAA,KAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC,CAAA;AAE1E5C,EAAAA,KAAK,CAACiE,SAAS,CAAC,MAAMmB,+BAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;AAEjE,EAAA,oBACEiC,KAAA,CAAAjE,aAAA,CAAC0J,MAAM,EAAA;AACL/I,IAAAA,QAAQ,EAAEA,QAAQ;AAClByJ,IAAAA,QAAQ,EAAEA,QAAQ;IAClBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAM;AAC5ByH,IAAAA,SAAS,EAAEnG,OAAO;AAClBH,IAAAA,MAAM,EAAEA,MAAAA;AAAM,GAAA,CACd,CAAA;AAEN,CAAA;AAEA,IAAA1B,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXkK,aAAa,CAACtG,WAAW,GAAG,wBAAwB,CAAA;AACrD,CAAA;AAeD,MAAMwG,SAAS,GACb,OAAOlJ,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAAC3B,QAAQ,KAAK,WAAW,IACtC,OAAO2B,MAAM,CAAC3B,QAAQ,CAACC,aAAa,KAAK,WAAW,CAAA;AAEtD,MAAM6K,kBAAkB,GAAG,+BAA+B,CAAA;AAE1D;;AAEG;AACUC,MAAAA,IAAI,gBAAG7G,KAAK,CAAC8G,UAAU,CAClC,SAASC,WAAWA,CAAAC,KAAA,EAalBC,GAAG,EAAA;EAAA,IAZH;MACEC,OAAO;MACPC,QAAQ;MACRC,cAAc;MACdrC,OAAO;MACPlG,KAAK;MACL3E,MAAM;MACN2K,EAAE;MACFC,kBAAkB;AAClBuC,MAAAA,cAAAA;AACO,KACR,GAAAL,KAAA;AADIM,IAAAA,IAAI,GAAAC,6BAAA,CAAAP,KAAA,EAAAQ,SAAA,CAAA,CAAA;EAIT,IAAI;AAAE9K,IAAAA,QAAAA;AAAQ,GAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AAEtD;AACA,EAAA,IAAIC,YAAY,CAAA;EAChB,IAAIC,UAAU,GAAG,KAAK,CAAA;EAEtB,IAAI,OAAO/C,EAAE,KAAK,QAAQ,IAAI+B,kBAAkB,CAACiB,IAAI,CAAChD,EAAE,CAAC,EAAE;AACzD;AACA8C,IAAAA,YAAY,GAAG9C,EAAE,CAAA;AAEjB;AACA,IAAA,IAAI8B,SAAS,EAAE;MACb,IAAI;QACF,IAAImB,UAAU,GAAG,IAAIC,GAAG,CAACtK,MAAM,CAAC0G,QAAQ,CAAC6D,IAAI,CAAC,CAAA;QAC9C,IAAIC,SAAS,GAAGpD,EAAE,CAACqD,UAAU,CAAC,IAAI,CAAC,GAC/B,IAAIH,GAAG,CAACD,UAAU,CAACK,QAAQ,GAAGtD,EAAE,CAAC,GACjC,IAAIkD,GAAG,CAAClD,EAAE,CAAC,CAAA;QACf,IAAIuD,IAAI,GAAGnL,aAAa,CAACgL,SAAS,CAACI,QAAQ,EAAE3L,QAAQ,CAAC,CAAA;QAEtD,IAAIuL,SAAS,CAACK,MAAM,KAAKR,UAAU,CAACQ,MAAM,IAAIF,IAAI,IAAI,IAAI,EAAE;AAC1D;UACAvD,EAAE,GAAGuD,IAAI,GAAGH,SAAS,CAACM,MAAM,GAAGN,SAAS,CAACO,IAAI,CAAA;AAC9C,SAAA,MAAM;AACLZ,UAAAA,UAAU,GAAG,IAAI,CAAA;AAClB,SAAA;OACF,CAAC,OAAO5L,CAAC,EAAE;AACV;AACAK,QAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAAC,YAAAA,GAAAA,cAAO,CACL,KAAK,EACL,aAAA,GAAaqI,EAAE,GAAA,wDAAA,GAAA,mDACsC,CACtD,GAAA,KAAA,CAAA,CAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACA,EAAA,IAAImD,IAAI,GAAGS,OAAO,CAAC5D,EAAE,EAAE;AAAEsC,IAAAA,QAAAA;AAAU,GAAA,CAAC,CAAA;AAEpC,EAAA,IAAIuB,eAAe,GAAGC,mBAAmB,CAAC9D,EAAE,EAAE;IAC5CE,OAAO;IACPlG,KAAK;IACL3E,MAAM;IACN4K,kBAAkB;IAClBqC,QAAQ;AACRE,IAAAA,cAAAA;AACD,GAAA,CAAC,CAAA;EACF,SAASuB,WAAWA,CAClBhP,KAAsD,EAAA;AAEtD,IAAA,IAAIsN,OAAO,EAAEA,OAAO,CAACtN,KAAK,CAAC,CAAA;AAC3B,IAAA,IAAI,CAACA,KAAK,CAACiP,gBAAgB,EAAE;MAC3BH,eAAe,CAAC9O,KAAK,CAAC,CAAA;AACvB,KAAA;AACH,GAAA;AAEA,EAAA;AAAA;AACE;AACAoG,IAAAA,KAAA,CAAAjE,aAAA,CAAA,GAAA,EAAAiC,QAAA,KACMsJ,IAAI,EAAA;MACRU,IAAI,EAAEL,YAAY,IAAIK,IAAI;AAC1Bd,MAAAA,OAAO,EAAEU,UAAU,IAAIR,cAAc,GAAGF,OAAO,GAAG0B,WAAW;AAC7D3B,MAAAA,GAAG,EAAEA,GAAG;AACR/M,MAAAA,MAAM,EAAEA,MAAAA;KAAM,CAAA,CAAA;AACd,IAAA;AAEN,CAAC,EACF;AAED,IAAAmC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXsK,IAAI,CAAC1G,WAAW,GAAG,MAAM,CAAA;AAC1B,CAAA;AAmBD;;AAEG;AACU2I,MAAAA,OAAO,gBAAG9I,KAAK,CAAC8G,UAAU,CACrC,SAASiC,cAAcA,CAAAC,KAAA,EAYrB/B,GAAG,EAAA;EAAA,IAXH;MACE,cAAc,EAAEgC,eAAe,GAAG,MAAM;AACxCC,MAAAA,aAAa,GAAG,KAAK;MACrBC,SAAS,EAAEC,aAAa,GAAG,EAAE;AAC7BC,MAAAA,GAAG,GAAG,KAAK;AACXC,MAAAA,KAAK,EAAEC,SAAS;MAChB1E,EAAE;MACFwC,cAAc;AACdlB,MAAAA,QAAAA;AAED,KAAA,GAAA6C,KAAA;AADI1B,IAAAA,IAAI,GAAAC,6BAAA,CAAAyB,KAAA,EAAAQ,UAAA,CAAA,CAAA;AAIT,EAAA,IAAIpB,IAAI,GAAGqB,eAAe,CAAC5E,EAAE,EAAE;IAAEsC,QAAQ,EAAEG,IAAI,CAACH,QAAAA;AAAQ,GAAE,CAAC,CAAA;AAC3D,EAAA,IAAIhD,QAAQ,GAAGuF,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAIC,WAAW,GAAG3J,KAAK,CAACyH,UAAU,CAACjC,6BAAsB,CAAC,CAAA;EAC1D,IAAI;IAAEnB,SAAS;AAAE3H,IAAAA,QAAAA;AAAU,GAAA,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACjE,EAAA,IAAIxH,eAAe,GACjByJ,WAAW,IAAI,IAAI;AACnB;AACA;AACAC,EAAAA,sBAAsB,CAACxB,IAAI,CAAC,IAC5Bf,cAAc,KAAK,IAAI,CAAA;AAEzB,EAAA,IAAIwC,UAAU,GAAGxF,SAAS,CAACG,cAAc,GACrCH,SAAS,CAACG,cAAc,CAAC4D,IAAI,CAAC,CAACC,QAAQ,GACvCD,IAAI,CAACC,QAAQ,CAAA;AACjB,EAAA,IAAIyB,gBAAgB,GAAG3F,QAAQ,CAACkE,QAAQ,CAAA;EACxC,IAAI0B,oBAAoB,GACtBJ,WAAW,IAAIA,WAAW,CAACK,UAAU,IAAIL,WAAW,CAACK,UAAU,CAAC7F,QAAQ,GACpEwF,WAAW,CAACK,UAAU,CAAC7F,QAAQ,CAACkE,QAAQ,GACxC,IAAI,CAAA;EAEV,IAAI,CAACa,aAAa,EAAE;AAClBY,IAAAA,gBAAgB,GAAGA,gBAAgB,CAACtQ,WAAW,EAAE,CAAA;IACjDuQ,oBAAoB,GAAGA,oBAAoB,GACvCA,oBAAoB,CAACvQ,WAAW,EAAE,GAClC,IAAI,CAAA;AACRqQ,IAAAA,UAAU,GAAGA,UAAU,CAACrQ,WAAW,EAAE,CAAA;AACtC,GAAA;EAED,IAAIuQ,oBAAoB,IAAIrN,QAAQ,EAAE;IACpCqN,oBAAoB,GAClB9M,aAAa,CAAC8M,oBAAoB,EAAErN,QAAQ,CAAC,IAAIqN,oBAAoB,CAAA;AACxE,GAAA;AAED;AACA;AACA;AACA;AACA;EACA,MAAME,gBAAgB,GACpBJ,UAAU,KAAK,GAAG,IAAIA,UAAU,CAACK,QAAQ,CAAC,GAAG,CAAC,GAC1CL,UAAU,CAACM,MAAM,GAAG,CAAC,GACrBN,UAAU,CAACM,MAAM,CAAA;EACvB,IAAIC,QAAQ,GACVN,gBAAgB,KAAKD,UAAU,IAC9B,CAACR,GAAG,IACHS,gBAAgB,CAAC5B,UAAU,CAAC2B,UAAU,CAAC,IACvCC,gBAAgB,CAACO,MAAM,CAACJ,gBAAgB,CAAC,KAAK,GAAI,CAAA;AAEtD,EAAA,IAAIK,SAAS,GACXP,oBAAoB,IAAI,IAAI,KAC3BA,oBAAoB,KAAKF,UAAU,IACjC,CAACR,GAAG,IACHU,oBAAoB,CAAC7B,UAAU,CAAC2B,UAAU,CAAC,IAC3CE,oBAAoB,CAACM,MAAM,CAACR,UAAU,CAACM,MAAM,CAAC,KAAK,GAAI,CAAC,CAAA;AAE9D,EAAA,IAAII,WAAW,GAAG;IAChBH,QAAQ;IACRE,SAAS;AACTpK,IAAAA,eAAAA;GACD,CAAA;AAED,EAAA,IAAIsK,WAAW,GAAGJ,QAAQ,GAAGnB,eAAe,GAAG1L,SAAS,CAAA;AAExD,EAAA,IAAI4L,SAA6B,CAAA;AACjC,EAAA,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;AACvCD,IAAAA,SAAS,GAAGC,aAAa,CAACmB,WAAW,CAAC,CAAA;AACvC,GAAA,MAAM;AACL;AACA;AACA;AACA;AACA;AACApB,IAAAA,SAAS,GAAG,CACVC,aAAa,EACbgB,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAC1BE,SAAS,GAAG,SAAS,GAAG,IAAI,EAC5BpK,eAAe,GAAG,eAAe,GAAG,IAAI,CACzC,CACEuK,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC,CAAA;AACb,GAAA;AAED,EAAA,IAAIrB,KAAK,GACP,OAAOC,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACgB,WAAW,CAAC,GAAGhB,SAAS,CAAA;EAEtE,oBACEvJ,oBAAC6G,IAAI,EAAA7I,QAAA,KACCsJ,IAAI,EAAA;AACM,IAAA,cAAA,EAAAkD,WAAW;AACzBrB,IAAAA,SAAS,EAAEA,SAAS;AACpBlC,IAAAA,GAAG,EAAEA,GAAG;AACRqC,IAAAA,KAAK,EAAEA,KAAK;AACZzE,IAAAA,EAAE,EAAEA,EAAE;AACNwC,IAAAA,cAAc,EAAEA,cAAAA;GAEf,CAAA,EAAA,OAAOlB,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACoE,WAAW,CAAC,GAAGpE,QAAQ,CAC7D,CAAA;AAEX,CAAC,EACF;AAED,IAAA9J,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXuM,OAAO,CAAC3I,WAAW,GAAG,SAAS,CAAA;AAChC,CAAA;AAgGD;;;;;AAKG;AACI,MAAMyK,IAAI,gBAAG5K,KAAK,CAAC8G,UAAU,CAClC,CAAA+D,KAAA,EAeEC,YAAY,KACV;EAAA,IAfF;MACEC,UAAU;MACVpG,QAAQ;MACRyC,cAAc;MACdrC,OAAO;MACPlG,KAAK;AACLlC,MAAAA,MAAM,GAAGzD,aAAa;MACtB0D,MAAM;MACNoO,QAAQ;MACR7D,QAAQ;MACRrC,kBAAkB;AAClBuC,MAAAA,cAAAA;KAED,GAAAwD,KAAA;AADII,IAAAA,KAAK,GAAA1D,6BAAA,CAAAsD,KAAA,EAAAK,UAAA,CAAA,CAAA;AAIV,EAAA,IAAIC,MAAM,GAAGC,SAAS,EAAE,CAAA;AACxB,EAAA,IAAIC,UAAU,GAAGC,aAAa,CAAC1O,MAAM,EAAE;AAAEuK,IAAAA,QAAAA;AAAU,GAAA,CAAC,CAAA;AACpD,EAAA,IAAIoE,UAAU,GACZ5O,MAAM,CAACnD,WAAW,EAAE,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM,CAAA;EAEjD,IAAIgS,aAAa,GAA6C5R,KAAK,IAAI;AACrEoR,IAAAA,QAAQ,IAAIA,QAAQ,CAACpR,KAAK,CAAC,CAAA;IAC3B,IAAIA,KAAK,CAACiP,gBAAgB,EAAE,OAAA;IAC5BjP,KAAK,CAAC6R,cAAc,EAAE,CAAA;AAEtB,IAAA,IAAIC,SAAS,GAAI9R,KAAoC,CAAC+R,WAAW,CAC9DD,SAAqC,CAAA;AAExC,IAAA,IAAIE,YAAY,GACb,CAAAF,SAAS,IAATA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAE1O,YAAY,CAAC,YAAY,CAAgC,KACrEL,MAAM,CAAA;AAERwO,IAAAA,MAAM,CAACO,SAAS,IAAI9R,KAAK,CAACiS,aAAa,EAAE;MACvCd,UAAU;AACVpO,MAAAA,MAAM,EAAEiP,YAAY;MACpBjH,QAAQ;MACRI,OAAO;MACPlG,KAAK;MACLsI,QAAQ;MACRrC,kBAAkB;AAClBuC,MAAAA,cAAAA;AACD,KAAA,CAAC,CAAA;GACH,CAAA;AAED,EAAA,oBACErH,KAAA,CAAAjE,aAAA,CAAA,MAAA,EAAAiC,QAAA,CAAA;AACEiJ,IAAAA,GAAG,EAAE6D,YAAY;AACjBnO,IAAAA,MAAM,EAAE4O,UAAU;AAClB3O,IAAAA,MAAM,EAAEyO,UAAU;AAClBL,IAAAA,QAAQ,EAAE5D,cAAc,GAAG4D,QAAQ,GAAGQ,aAAAA;GAClCP,EAAAA,KAAK,CAAA,CACT,CAAA;AAEN,CAAC,EACF;AAED,IAAA5O,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXqO,IAAI,CAACzK,WAAW,GAAG,MAAM,CAAA;AAC1B,CAAA;AAOD;;;AAGG;SACa2L,iBAAiBA,CAAAC,MAAA,EAGR;EAAA,IAHS;IAChCC,MAAM;AACNC,IAAAA,UAAAA;AACuB,GAAA,GAAAF,MAAA,CAAA;AACvBG,EAAAA,oBAAoB,CAAC;IAAEF,MAAM;AAAEC,IAAAA,UAAAA;AAAU,GAAE,CAAC,CAAA;AAC5C,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA,IAAA5P,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXuP,iBAAiB,CAAC3L,WAAW,GAAG,mBAAmB,CAAA;AACpD,CAAA;AACD;AAEA;AACA;AACA;AAEA,IAAKgM,cAMJ,CAAA;AAND,CAAA,UAAKA,cAAc,EAAA;AACjBA,EAAAA,cAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC7CA,EAAAA,cAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvBA,EAAAA,cAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrCA,EAAAA,cAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzBA,EAAAA,cAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACnD,CAAC,EANIA,cAAc,KAAdA,cAAc,GAMlB,EAAA,CAAA,CAAA,CAAA;AAED,IAAKC,mBAIJ,CAAA;AAJD,CAAA,UAAKA,mBAAmB,EAAA;AACtBA,EAAAA,mBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzBA,EAAAA,mBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3BA,EAAAA,mBAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC/C,CAAC,EAJIA,mBAAmB,KAAnBA,mBAAmB,GAIvB,EAAA,CAAA,CAAA,CAAA;AAED;AAEA,SAASC,yBAAyBA,CAChCC,QAA8C,EAAA;AAE9C,EAAA,OAAUA,QAAQ,GAAA,+FAAA,CAAA;AACpB,CAAA;AAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAA;AACpD,EAAA,IAAIE,GAAG,GAAGxM,KAAK,CAACyH,UAAU,CAACnC,wBAAiB,CAAC,CAAA;AAC7C,EAAA,CAAUkH,GAAG,GAAAnQ,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAbkQ,YAAAA,GAAAA,gBAAS,QAAMJ,yBAAyB,CAACC,QAAQ,CAAC,IAAlDG,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAOD,GAAG,CAAA;AACZ,CAAA;AAEA,SAASE,kBAAkBA,CAACJ,QAA6B,EAAA;AACvD,EAAA,IAAIzN,KAAK,GAAGmB,KAAK,CAACyH,UAAU,CAACjC,6BAAsB,CAAC,CAAA;AACpD,EAAA,CAAU3G,KAAK,GAAAxC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAfkQ,YAAAA,GAAAA,gBAAS,QAAQJ,yBAAyB,CAACC,QAAQ,CAAC,IAApDG,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAO5N,KAAK,CAAA;AACd,CAAA;AAEA;AAEA;;;;AAIG;AACG,SAAU8J,mBAAmBA,CACjC9D,EAAM,EAAA8H,KAAA,EAeA;EAAA,IAdN;IACEzS,MAAM;AACN6K,IAAAA,OAAO,EAAE6H,WAAW;IACpB/N,KAAK;IACLiG,kBAAkB;IAClBqC,QAAQ;AACRE,IAAAA,cAAAA;yBAQE,EAAE,GAAAsF,KAAA,CAAA;AAEN,EAAA,IAAIhI,QAAQ,GAAGkI,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAI1I,QAAQ,GAAGuF,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAItB,IAAI,GAAGqB,eAAe,CAAC5E,EAAE,EAAE;AAAEsC,IAAAA,QAAAA;AAAU,GAAA,CAAC,CAAA;AAE5C,EAAA,OAAOnH,KAAK,CAAC2C,WAAW,CACrB/I,KAAsC,IAAI;AACzC,IAAA,IAAIK,sBAAsB,CAACL,KAAK,EAAEM,MAAM,CAAC,EAAE;MACzCN,KAAK,CAAC6R,cAAc,EAAE,CAAA;AAEtB;AACA;AACA,MAAA,IAAI1G,OAAO,GACT6H,WAAW,KAAKrP,SAAS,GACrBqP,WAAW,GACXE,UAAU,CAAC3I,QAAQ,CAAC,KAAK2I,UAAU,CAAC1E,IAAI,CAAC,CAAA;MAE/CzD,QAAQ,CAACE,EAAE,EAAE;QACXE,OAAO;QACPlG,KAAK;QACLiG,kBAAkB;QAClBqC,QAAQ;AACRE,QAAAA,cAAAA;AACD,OAAA,CAAC,CAAA;AACH,KAAA;GACF,EACD,CACElD,QAAQ,EACRQ,QAAQ,EACRyD,IAAI,EACJwE,WAAW,EACX/N,KAAK,EACL3E,MAAM,EACN2K,EAAE,EACFC,kBAAkB,EAClBqC,QAAQ,EACRE,cAAc,CACf,CACF,CAAA;AACH,CAAA;AAEA;;;AAGG;AACG,SAAU0F,eAAeA,CAC7BC,WAAiC,EAAA;AAEjC3Q,EAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,OAAOlC,eAAe,KAAK,WAAW,EACtC,uEACqE,GAAA,mEAAA,GAAA,wDACX,gDACX,CAChD,GAAA,KAAA,CAAA,CAAA;EAED,IAAI2S,sBAAsB,GAAGjN,KAAK,CAACwC,MAAM,CAACpI,kBAAkB,CAAC4S,WAAW,CAAC,CAAC,CAAA;AAC1E,EAAA,IAAIE,qBAAqB,GAAGlN,KAAK,CAACwC,MAAM,CAAC,KAAK,CAAC,CAAA;AAE/C,EAAA,IAAI2B,QAAQ,GAAGuF,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAIrO,YAAY,GAAG2E,KAAK,CAACsE,OAAO,CAC9B;AACE;AACA;AACA;EACApJ,0BAA0B,CACxBiJ,QAAQ,CAACoE,MAAM,EACf2E,qBAAqB,CAAC9J,OAAO,GAAG,IAAI,GAAG6J,sBAAsB,CAAC7J,OAAO,CACtE,EACH,CAACe,QAAQ,CAACoE,MAAM,CAAC,CAClB,CAAA;AAED,EAAA,IAAI5D,QAAQ,GAAGkI,WAAW,EAAE,CAAA;EAC5B,IAAIM,eAAe,GAAGnN,KAAK,CAAC2C,WAAW,CACrC,CAACyK,QAAQ,EAAEC,eAAe,KAAI;AAC5B,IAAA,MAAMC,eAAe,GAAGlT,kBAAkB,CACxC,OAAOgT,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC/R,YAAY,CAAC,GAAG+R,QAAQ,CACnE,CAAA;IACDF,qBAAqB,CAAC9J,OAAO,GAAG,IAAI,CAAA;AACpCuB,IAAAA,QAAQ,CAAC,GAAG,GAAG2I,eAAe,EAAED,eAAe,CAAC,CAAA;AAClD,GAAC,EACD,CAAC1I,QAAQ,EAAEtJ,YAAY,CAAC,CACzB,CAAA;AAED,EAAA,OAAO,CAACA,YAAY,EAAE8R,eAAe,CAAC,CAAA;AACxC,CAAA;AA2CA,SAASI,4BAA4BA,GAAA;AACnC,EAAA,IAAI,OAAOzR,QAAQ,KAAK,WAAW,EAAE;AACnC,IAAA,MAAM,IAAIsB,KAAK,CACb,mDAAmD,GACjD,8DAA8D,CACjE,CAAA;AACF,GAAA;AACH,CAAA;AAEA,IAAIoQ,SAAS,GAAG,CAAC,CAAA;AACjB,IAAIC,kBAAkB,GAAGA,MAAA,IAAA,GAAWC,MAAM,CAAC,EAAEF,SAAS,CAAC,GAAI,IAAA,CAAA;AAE3D;;;AAGG;SACapC,SAASA,GAAA;EACvB,IAAI;AAAE1J,IAAAA,MAAAA;AAAM,GAAE,GAAG6K,oBAAoB,CAACJ,cAAc,CAACwB,SAAS,CAAC,CAAA;EAC/D,IAAI;AAAEjR,IAAAA,QAAAA;AAAQ,GAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACtD,EAAA,IAAIkG,cAAc,GAAGC,iBAAU,EAAE,CAAA;EAEjC,OAAO7N,KAAK,CAAC2C,WAAW,CACtB,UAACzI,MAAM,EAAE4T,OAAO,EAAS;AAAA,IAAA,IAAhBA,OAAO,KAAA,KAAA,CAAA,EAAA;MAAPA,OAAO,GAAG,EAAE,CAAA;AAAA,KAAA;AACnBP,IAAAA,4BAA4B,EAAE,CAAA;IAE9B,IAAI;MAAE3Q,MAAM;MAAED,MAAM;MAAEP,OAAO;MAAES,QAAQ;AAAEC,MAAAA,IAAAA;AAAI,KAAE,GAAGL,qBAAqB,CACrEvC,MAAM,EACNwC,QAAQ,CACT,CAAA;AAED,IAAA,IAAIoR,OAAO,CAACnJ,QAAQ,KAAK,KAAK,EAAE;MAC9B,IAAI9J,GAAG,GAAGiT,OAAO,CAAC/C,UAAU,IAAI0C,kBAAkB,EAAE,CAAA;AACpD/L,MAAAA,MAAM,CAACqM,KAAK,CAAClT,GAAG,EAAE+S,cAAc,EAAEE,OAAO,CAAClR,MAAM,IAAIA,MAAM,EAAE;QAC1DkI,kBAAkB,EAAEgJ,OAAO,CAAChJ,kBAAkB;QAC9CjI,QAAQ;QACRC,IAAI;AACJyO,QAAAA,UAAU,EAAEuC,OAAO,CAACnR,MAAM,IAAKA,MAAyB;AACxDqR,QAAAA,WAAW,EAAEF,OAAO,CAAC1R,OAAO,IAAKA,OAAuB;QACxD4G,SAAS,EAAE8K,OAAO,CAAC9K,SAAAA;AACpB,OAAA,CAAC,CAAA;AACH,KAAA,MAAM;MACLtB,MAAM,CAACiD,QAAQ,CAACmJ,OAAO,CAAClR,MAAM,IAAIA,MAAM,EAAE;QACxCkI,kBAAkB,EAAEgJ,OAAO,CAAChJ,kBAAkB;QAC9CjI,QAAQ;QACRC,IAAI;AACJyO,QAAAA,UAAU,EAAEuC,OAAO,CAACnR,MAAM,IAAKA,MAAyB;AACxDqR,QAAAA,WAAW,EAAEF,OAAO,CAAC1R,OAAO,IAAKA,OAAuB;QACxD2I,OAAO,EAAE+I,OAAO,CAAC/I,OAAO;QACxBlG,KAAK,EAAEiP,OAAO,CAACjP,KAAK;AACpBoP,QAAAA,WAAW,EAAEL,cAAc;QAC3B5K,SAAS,EAAE8K,OAAO,CAAC9K,SAAS;QAC5BqE,cAAc,EAAEyG,OAAO,CAACzG,cAAAA;AACzB,OAAA,CAAC,CAAA;AACH,KAAA;GACF,EACD,CAAC3F,MAAM,EAAEhF,QAAQ,EAAEkR,cAAc,CAAC,CACnC,CAAA;AACH,CAAA;AAEA;AACA;AACM,SAAUtC,aAAaA,CAC3B1O,MAAe,EAAAsR,MAAA,EACsC;EAAA,IAArD;AAAE/G,IAAAA,QAAAA;0BAAiD,EAAE,GAAA+G,MAAA,CAAA;EAErD,IAAI;AAAExR,IAAAA,QAAAA;AAAQ,GAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACtD,EAAA,IAAIyG,YAAY,GAAGnO,KAAK,CAACyH,UAAU,CAAC2G,mBAAY,CAAC,CAAA;AACjD,EAAA,CAAUD,YAAY,GAAA9R,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAtBkQ,gBAAS,CAAA,KAAA,EAAe,kDAAkD,CAAA,GAA1EA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAET,EAAA,IAAI,CAAC4B,KAAK,CAAC,GAAGF,YAAY,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC5C;AACA;EACA,IAAInG,IAAI,GAAApK,QAAA,CAAQyL,EAAAA,EAAAA,eAAe,CAAC7M,MAAM,GAAGA,MAAM,GAAG,GAAG,EAAE;AAAEuK,IAAAA,QAAAA;AAAQ,GAAE,CAAC,CAAE,CAAA;AAEtE;AACA;AACA;AACA,EAAA,IAAIhD,QAAQ,GAAGuF,WAAW,EAAE,CAAA;EAC5B,IAAI9M,MAAM,IAAI,IAAI,EAAE;AAClB;AACA;AACAwL,IAAAA,IAAI,CAACG,MAAM,GAAGpE,QAAQ,CAACoE,MAAM,CAAA;AAE7B;AACA;AACA;IACA,IAAIiG,MAAM,GAAG,IAAIlU,eAAe,CAAC8N,IAAI,CAACG,MAAM,CAAC,CAAA;AAC7C,IAAA,IAAIkG,WAAW,GAAGD,MAAM,CAAC/S,MAAM,CAAC,OAAO,CAAC,CAAA;IACxC,IAAIiT,kBAAkB,GAAGD,WAAW,CAACE,IAAI,CAAE1T,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC,CAAA;AAC1D,IAAA,IAAIyT,kBAAkB,EAAE;AACtBF,MAAAA,MAAM,CAAClL,MAAM,CAAC,OAAO,CAAC,CAAA;MACtBmL,WAAW,CAAChE,MAAM,CAAExP,CAAC,IAAKA,CAAC,CAAC,CAACK,OAAO,CAAEL,CAAC,IAAKuT,MAAM,CAAC9S,MAAM,CAAC,OAAO,EAAET,CAAC,CAAC,CAAC,CAAA;AACtE,MAAA,IAAI2T,EAAE,GAAGJ,MAAM,CAACK,QAAQ,EAAE,CAAA;AAC1BzG,MAAAA,IAAI,CAACG,MAAM,GAAGqG,EAAE,GAAOA,GAAAA,GAAAA,EAAE,GAAK,EAAE,CAAA;AACjC,KAAA;AACF,GAAA;AAED,EAAA,IAAI,CAAC,CAAChS,MAAM,IAAIA,MAAM,KAAK,GAAG,KAAKyR,KAAK,CAACS,KAAK,CAACC,KAAK,EAAE;AACpD3G,IAAAA,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACG,MAAM,GACrBH,IAAI,CAACG,MAAM,CAACxD,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ,CAAA;AACb,GAAA;AAED;AACA;AACA;AACA;EACA,IAAIrI,QAAQ,KAAK,GAAG,EAAE;IACpB0L,IAAI,CAACC,QAAQ,GACXD,IAAI,CAACC,QAAQ,KAAK,GAAG,GAAG3L,QAAQ,GAAGsS,SAAS,CAAC,CAACtS,QAAQ,EAAE0L,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAA;AAC1E,GAAA;EAED,OAAOyE,UAAU,CAAC1E,IAAI,CAAC,CAAA;AACzB,CAAA;AAUA;AAEA;;;AAGG;SACa6G,UAAUA,CAAAC,MAAA,EAEF;AAAA,EAAA,IAAAC,cAAA,CAAA;EAAA,IAFgB;AACtCtU,IAAAA,GAAAA;0BACoB,EAAE,GAAAqU,MAAA,CAAA;EACtB,IAAI;AAAExN,IAAAA,MAAAA;AAAM,GAAE,GAAG6K,oBAAoB,CAACJ,cAAc,CAACiD,UAAU,CAAC,CAAA;AAChE,EAAA,IAAIvQ,KAAK,GAAG6N,kBAAkB,CAACN,mBAAmB,CAACgD,UAAU,CAAC,CAAA;AAC9D,EAAA,IAAI7M,WAAW,GAAGvC,KAAK,CAACyH,UAAU,CAACrH,eAAe,CAAC,CAAA;AACnD,EAAA,IAAI0O,KAAK,GAAG9O,KAAK,CAACyH,UAAU,CAAC2G,mBAAY,CAAC,CAAA;EAC1C,IAAIiB,OAAO,IAAAF,cAAA,GAAGL,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAACnE,MAAM,GAAG,CAAC,CAAC,qBAAvCgF,cAAA,CAAyCL,KAAK,CAACQ,EAAE,CAAA;EAE/D,CAAU/M,WAAW,GAAAlG,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAArBkQ,gBAAS,CAAA,KAAA,EAAA,kDAAA,CAAA,GAATA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EACT,CAAUqC,KAAK,GAAAzS,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAfkQ,gBAAS,CAAA,KAAA,EAAA,+CAAA,CAAA,GAATA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,EACE4C,OAAO,IAAI,IAAI,CAAA,GAAAhT,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GADjBkQ,gBAAS,CAAA,KAAA,EAAA,oEAAA,CAAA,GAATA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAKT;AACA;AACA;EACA,IAAI8C,UAAU,GAAG3O,SAAS,GAAGA,SAAS,EAAE,GAAG,EAAE,CAAA;AAC7C,EAAA,IAAI,CAACmK,UAAU,EAAEyE,aAAa,CAAC,GAAGxP,KAAK,CAAC4B,QAAQ,CAAS/G,GAAG,IAAI0U,UAAU,CAAC,CAAA;AAC3E,EAAA,IAAI1U,GAAG,IAAIA,GAAG,KAAKkQ,UAAU,EAAE;IAC7ByE,aAAa,CAAC3U,GAAG,CAAC,CAAA;AACnB,GAAA,MAAM,IAAI,CAACkQ,UAAU,EAAE;AACtB;AACAyE,IAAAA,aAAa,CAAC/B,kBAAkB,EAAE,CAAC,CAAA;AACpC,GAAA;AAED;EACAzN,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnBvC,IAAAA,MAAM,CAAC+N,UAAU,CAAC1E,UAAU,CAAC,CAAA;AAC7B,IAAA,OAAO,MAAK;AACV;AACA;AACA;AACArJ,MAAAA,MAAM,CAACgO,aAAa,CAAC3E,UAAU,CAAC,CAAA;KACjC,CAAA;AACH,GAAC,EAAE,CAACrJ,MAAM,EAAEqJ,UAAU,CAAC,CAAC,CAAA;AAExB;EACA,IAAI4E,IAAI,GAAG3P,KAAK,CAAC2C,WAAW,CAC1B,CAACqF,IAAY,EAAEnK,IAA8B,KAAI;AAC/C,IAAA,CAAUwR,OAAO,GAAAhT,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAjBkQ,gBAAS,CAAA,KAAA,EAAU,yCAAyC,CAAA,GAA5DA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;IACT/K,MAAM,CAACqM,KAAK,CAAChD,UAAU,EAAEsE,OAAO,EAAErH,IAAI,EAAEnK,IAAI,CAAC,CAAA;GAC9C,EACD,CAACkN,UAAU,EAAEsE,OAAO,EAAE3N,MAAM,CAAC,CAC9B,CAAA;AAED,EAAA,IAAIkO,UAAU,GAAGxE,SAAS,EAAE,CAAA;EAC5B,IAAID,MAAM,GAAGnL,KAAK,CAAC2C,WAAW,CAC5B,CAACzI,MAAM,EAAE2D,IAAI,KAAI;AACf+R,IAAAA,UAAU,CAAC1V,MAAM,EAAA8D,QAAA,KACZH,IAAI,EAAA;AACP8G,MAAAA,QAAQ,EAAE,KAAK;AACfoG,MAAAA,UAAAA;AAAU,KAAA,CACX,CAAC,CAAA;AACJ,GAAC,EACD,CAACA,UAAU,EAAE6E,UAAU,CAAC,CACzB,CAAA;AAED,EAAA,IAAIC,WAAW,GAAG7P,KAAK,CAACsE,OAAO,CAAC,MAAK;IACnC,IAAIuL,WAAW,gBAAG7P,KAAK,CAAC8G,UAAU,CAChC,CAACmE,KAAK,EAAEhE,GAAG,KAAI;MACb,oBACEjH,KAAC,CAAAjE,aAAA,CAAA6O,IAAI,EAAA5M,QAAA,KAAKiN,KAAK,EAAA;AAAEtG,QAAAA,QAAQ,EAAE,KAAK;AAAEoG,QAAAA,UAAU,EAAEA,UAAU;AAAE9D,QAAAA,GAAG,EAAEA,GAAAA;AAAG,OAAA,CAAA,CAAI,CAAA;AAE1E,KAAC,CACF,CAAA;AACD,IAAA,IAAA5K,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;MACXsT,WAAW,CAAC1P,WAAW,GAAG,cAAc,CAAA;AACzC,KAAA;AACD,IAAA,OAAO0P,WAAW,CAAA;AACpB,GAAC,EAAE,CAAC9E,UAAU,CAAC,CAAC,CAAA;AAEhB;EACA,IAAI5H,OAAO,GAAGtE,KAAK,CAACqE,QAAQ,CAAC4M,GAAG,CAAC/E,UAAU,CAAC,IAAIgF,YAAY,CAAA;AAC5D,EAAA,IAAIvQ,IAAI,GAAG+C,WAAW,CAACuN,GAAG,CAAC/E,UAAU,CAAC,CAAA;AACtC,EAAA,IAAIiF,qBAAqB,GAAGhQ,KAAK,CAACsE,OAAO,CACvC,MAAAtG,QAAA,CAAA;AACE4M,IAAAA,IAAI,EAAEiF,WAAW;IACjB1E,MAAM;AACNwE,IAAAA,IAAAA;AAAI,GAAA,EACDxM,OAAO,EAAA;AACV3D,IAAAA,IAAAA;AAAI,GAAA,CACJ,EACF,CAACqQ,WAAW,EAAE1E,MAAM,EAAEwE,IAAI,EAAExM,OAAO,EAAE3D,IAAI,CAAC,CAC3C,CAAA;AAED,EAAA,OAAOwQ,qBAAqB,CAAA;AAC9B,CAAA;AAEA;;;AAGG;SACaC,WAAWA,GAAA;AACzB,EAAA,IAAIpR,KAAK,GAAG6N,kBAAkB,CAACN,mBAAmB,CAAC8D,WAAW,CAAC,CAAA;AAC/D,EAAA,OAAO3V,KAAK,CAAC4V,IAAI,CAACtR,KAAK,CAACqE,QAAQ,CAACjE,OAAO,EAAE,CAAC,CAACjE,GAAG,CAACoV,MAAA,IAAA;AAAA,IAAA,IAAC,CAACvV,GAAG,EAAEsI,OAAO,CAAC,GAAAiN,MAAA,CAAA;IAAA,OAAApS,QAAA,KAC1DmF,OAAO,EAAA;AACVtI,MAAAA,GAAAA;AAAG,KAAA,CAAA,CAAA;AAAA,GACH,CAAC,CAAA;AACL,CAAA;AAEA,MAAMwV,8BAA8B,GAAG,+BAA+B,CAAA;AACtE,IAAIC,oBAAoB,GAA2B,EAAE,CAAA;AAErD;;AAEG;AACH,SAASpE,oBAAoBA,CAAAqE,MAAA,EAMvB;EAAA,IANwB;IAC5BvE,MAAM;AACNC,IAAAA,UAAAA;0BAIE,EAAE,GAAAsE,MAAA,CAAA;EACJ,IAAI;AAAE7O,IAAAA,MAAAA;AAAM,GAAE,GAAG6K,oBAAoB,CAACJ,cAAc,CAACqE,oBAAoB,CAAC,CAAA;EAC1E,IAAI;IAAEC,qBAAqB;AAAE3L,IAAAA,kBAAAA;AAAoB,GAAA,GAAG4H,kBAAkB,CACpEN,mBAAmB,CAACoE,oBAAoB,CACzC,CAAA;EACD,IAAI;AAAE9T,IAAAA,QAAAA;AAAQ,GAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC,CAAA;AACtD,EAAA,IAAIvD,QAAQ,GAAGuF,WAAW,EAAE,CAAA;AAC5B,EAAA,IAAI4E,OAAO,GAAGoC,UAAU,EAAE,CAAA;AAC1B,EAAA,IAAI1G,UAAU,GAAG2G,aAAa,EAAE,CAAA;AAEhC;EACA3Q,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnBxG,IAAAA,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,QAAQ,CAAA;AAC3C,IAAA,OAAO,MAAK;AACVnT,MAAAA,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,MAAM,CAAA;KAC1C,CAAA;GACF,EAAE,EAAE,CAAC,CAAA;AAEN;AACAC,EAAAA,WAAW,CACT7Q,KAAK,CAAC2C,WAAW,CAAC,MAAK;AACrB,IAAA,IAAIqH,UAAU,CAACnL,KAAK,KAAK,MAAM,EAAE;AAC/B,MAAA,IAAIhE,GAAG,GAAG,CAACmR,MAAM,GAAGA,MAAM,CAAC7H,QAAQ,EAAEmK,OAAO,CAAC,GAAG,IAAI,KAAKnK,QAAQ,CAACtJ,GAAG,CAAA;AACrEyV,MAAAA,oBAAoB,CAACzV,GAAG,CAAC,GAAG4C,MAAM,CAACqT,OAAO,CAAA;AAC3C,KAAA;IACD,IAAI;AACFC,MAAAA,cAAc,CAACC,OAAO,CACpB/E,UAAU,IAAIoE,8BAA8B,EAC5CY,IAAI,CAACC,SAAS,CAACZ,oBAAoB,CAAC,CACrC,CAAA;KACF,CAAC,OAAO1Q,KAAK,EAAE;MACdvD,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAAC,cAAO,CACL,KAAK,EAAA,mGAAA,GAC+FoD,KAAK,GAAA,IAAI,CAC9G,GAAA,KAAA,CAAA,CAAA;AACF,KAAA;AACDnC,IAAAA,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,MAAM,CAAA;AAC3C,GAAC,EAAE,CAAC3E,UAAU,EAAED,MAAM,EAAEhC,UAAU,CAACnL,KAAK,EAAEsF,QAAQ,EAAEmK,OAAO,CAAC,CAAC,CAC9D,CAAA;AAED;AACA,EAAA,IAAI,OAAOxS,QAAQ,KAAK,WAAW,EAAE;AACnC;IACAkE,KAAK,CAAC+D,eAAe,CAAC,MAAK;MACzB,IAAI;QACF,IAAIoN,gBAAgB,GAAGJ,cAAc,CAACK,OAAO,CAC3CnF,UAAU,IAAIoE,8BAA8B,CAC7C,CAAA;AACD,QAAA,IAAIc,gBAAgB,EAAE;AACpBb,UAAAA,oBAAoB,GAAGW,IAAI,CAACI,KAAK,CAACF,gBAAgB,CAAC,CAAA;AACpD,SAAA;OACF,CAAC,OAAOnV,CAAC,EAAE;AACV;AAAA,OAAA;AAEJ,KAAC,EAAE,CAACiQ,UAAU,CAAC,CAAC,CAAA;AAEhB;AACA;IACAjM,KAAK,CAAC+D,eAAe,CAAC,MAAK;AACzB,MAAA,IAAIuN,qBAAqB,GACvBtF,MAAM,IAAItP,QAAQ,KAAK,GAAG,GACtB,CAACyH,QAAQ,EAAEmK,OAAO,KAChBtC,MAAM;AACJhO,MAAAA,QAAA,KAEKmG,QAAQ,EAAA;QACXkE,QAAQ,EACNpL,aAAa,CAACkH,QAAQ,CAACkE,QAAQ,EAAE3L,QAAQ,CAAC,IAC1CyH,QAAQ,CAACkE,QAAAA;OAEbiG,CAAAA,EAAAA,OAAO,CACR,GACHtC,MAAM,CAAA;AACZ,MAAA,IAAIuF,wBAAwB,GAAG7P,MAAM,IAANA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAAE8P,uBAAuB,CAC5DlB,oBAAoB,EACpB,MAAM7S,MAAM,CAACqT,OAAO,EACpBQ,qBAAqB,CACtB,CAAA;AACD,MAAA,OAAO,MAAMC,wBAAwB,IAAIA,wBAAwB,EAAE,CAAA;KACpE,EAAE,CAAC7P,MAAM,EAAEhF,QAAQ,EAAEsP,MAAM,CAAC,CAAC,CAAA;AAE9B;AACA;IACAhM,KAAK,CAAC+D,eAAe,CAAC,MAAK;AACzB;MACA,IAAI0M,qBAAqB,KAAK,KAAK,EAAE;AACnC,QAAA,OAAA;AACD,OAAA;AAED;AACA,MAAA,IAAI,OAAOA,qBAAqB,KAAK,QAAQ,EAAE;AAC7ChT,QAAAA,MAAM,CAACgU,QAAQ,CAAC,CAAC,EAAEhB,qBAAqB,CAAC,CAAA;AACzC,QAAA,OAAA;AACD,OAAA;AAED;MACA,IAAItM,QAAQ,CAACqE,IAAI,EAAE;AACjB,QAAA,IAAIkJ,EAAE,GAAG5V,QAAQ,CAAC6V,cAAc,CAC9BC,kBAAkB,CAACzN,QAAQ,CAACqE,IAAI,CAAC+F,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3C,CAAA;AACD,QAAA,IAAImD,EAAE,EAAE;UACNA,EAAE,CAACG,cAAc,EAAE,CAAA;AACnB,UAAA,OAAA;AACD,SAAA;AACF,OAAA;AAED;MACA,IAAI/M,kBAAkB,KAAK,IAAI,EAAE;AAC/B,QAAA,OAAA;AACD,OAAA;AAED;AACArH,MAAAA,MAAM,CAACgU,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;KACtB,EAAE,CAACtN,QAAQ,EAAEsM,qBAAqB,EAAE3L,kBAAkB,CAAC,CAAC,CAAA;AAC1D,GAAA;AACH,CAAA;AAIA;;;;;;;AAOG;AACa,SAAAgN,eAAeA,CAC7BC,QAA2C,EAC3CjE,OAA+B,EAAA;EAE/B,IAAI;AAAEkE,IAAAA,OAAAA;AAAO,GAAE,GAAGlE,OAAO,IAAI,EAAE,CAAA;EAC/B9N,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnB,IAAA,IAAIpG,IAAI,GAAGmU,OAAO,IAAI,IAAI,GAAG;AAAEA,MAAAA,OAAAA;AAAS,KAAA,GAAGzU,SAAS,CAAA;IACpDE,MAAM,CAACwU,gBAAgB,CAAC,cAAc,EAAEF,QAAQ,EAAElU,IAAI,CAAC,CAAA;AACvD,IAAA,OAAO,MAAK;MACVJ,MAAM,CAACyU,mBAAmB,CAAC,cAAc,EAAEH,QAAQ,EAAElU,IAAI,CAAC,CAAA;KAC3D,CAAA;AACH,GAAC,EAAE,CAACkU,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAA;AACzB,CAAA;AAEA;;;;;;;AAOG;AACH,SAASnB,WAAWA,CAClBkB,QAA6C,EAC7CjE,OAA+B,EAAA;EAE/B,IAAI;AAAEkE,IAAAA,OAAAA;AAAO,GAAE,GAAGlE,OAAO,IAAI,EAAE,CAAA;EAC/B9N,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnB,IAAA,IAAIpG,IAAI,GAAGmU,OAAO,IAAI,IAAI,GAAG;AAAEA,MAAAA,OAAAA;AAAS,KAAA,GAAGzU,SAAS,CAAA;IACpDE,MAAM,CAACwU,gBAAgB,CAAC,UAAU,EAAEF,QAAQ,EAAElU,IAAI,CAAC,CAAA;AACnD,IAAA,OAAO,MAAK;MACVJ,MAAM,CAACyU,mBAAmB,CAAC,UAAU,EAAEH,QAAQ,EAAElU,IAAI,CAAC,CAAA;KACvD,CAAA;AACH,GAAC,EAAE,CAACkU,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAA;AACzB,CAAA;AAEA;;;;;;;AAOG;AACH,SAASG,SAASA,CAAAC,MAAA,EAMjB;EAAA,IANkB;IACjBC,IAAI;AACJxS,IAAAA,OAAAA;AAID,GAAA,GAAAuS,MAAA,CAAA;AACC,EAAA,IAAIE,OAAO,GAAGC,UAAU,CAACF,IAAI,CAAC,CAAA;EAE9BrS,KAAK,CAACiE,SAAS,CAAC,MAAK;AACnB,IAAA,IAAIqO,OAAO,CAACzT,KAAK,KAAK,SAAS,EAAE;AAC/B,MAAA,IAAI2T,OAAO,GAAG/U,MAAM,CAACgV,OAAO,CAAC5S,OAAO,CAAC,CAAA;AACrC,MAAA,IAAI2S,OAAO,EAAE;AACX;AACA;AACA;AACAE,QAAAA,UAAU,CAACJ,OAAO,CAACE,OAAO,EAAE,CAAC,CAAC,CAAA;AAC/B,OAAA,MAAM;QACLF,OAAO,CAACK,KAAK,EAAE,CAAA;AAChB,OAAA;AACF,KAAA;AACH,GAAC,EAAE,CAACL,OAAO,EAAEzS,OAAO,CAAC,CAAC,CAAA;EAEtBG,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIqO,OAAO,CAACzT,KAAK,KAAK,SAAS,IAAI,CAACwT,IAAI,EAAE;MACxCC,OAAO,CAACK,KAAK,EAAE,CAAA;AAChB,KAAA;AACH,GAAC,EAAE,CAACL,OAAO,EAAED,IAAI,CAAC,CAAC,CAAA;AACrB,CAAA;AAIA;;;;;;;AAOG;AACH,SAASzI,sBAAsBA,CAC7B/E,EAAM,EACNhH,MAA6C;AAAA,EAAA,IAA7CA;IAAAA,OAA2C,EAAE,CAAA;AAAA,GAAA;AAE7C,EAAA,IAAIkE,SAAS,GAAG/B,KAAK,CAACyH,UAAU,CAAC1H,qBAAqB,CAAC,CAAA;AAEvD,EAAA,EACEgC,SAAS,IAAI,IAAI,CAAA1F,GAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GADnBkQ,gBAAS,CAEP,KAAA,EAAA,uFAAuF,GACrF,mEAAmE,IAHvEA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAMT,IAAI;AAAE/P,IAAAA,QAAAA;AAAQ,GAAE,GAAG6P,oBAAoB,CACrCJ,cAAc,CAACvC,sBAAsB,CACtC,CAAA;AACD,EAAA,IAAIxB,IAAI,GAAGqB,eAAe,CAAC5E,EAAE,EAAE;IAAEsC,QAAQ,EAAEtJ,IAAI,CAACsJ,QAAAA;AAAQ,GAAE,CAAC,CAAA;AAC3D,EAAA,IAAI,CAACpF,SAAS,CAAC7B,eAAe,EAAE;AAC9B,IAAA,OAAO,KAAK,CAAA;AACb,GAAA;AAED,EAAA,IAAI0S,WAAW,GACb3V,aAAa,CAAC8E,SAAS,CAAC2B,eAAe,CAAC2E,QAAQ,EAAE3L,QAAQ,CAAC,IAC3DqF,SAAS,CAAC2B,eAAe,CAAC2E,QAAQ,CAAA;AACpC,EAAA,IAAIwK,QAAQ,GACV5V,aAAa,CAAC8E,SAAS,CAAC4B,YAAY,CAAC0E,QAAQ,EAAE3L,QAAQ,CAAC,IACxDqF,SAAS,CAAC4B,YAAY,CAAC0E,QAAQ,CAAA;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,OACEyK,SAAS,CAAC1K,IAAI,CAACC,QAAQ,EAAEwK,QAAQ,CAAC,IAAI,IAAI,IAC1CC,SAAS,CAAC1K,IAAI,CAACC,QAAQ,EAAEuK,WAAW,CAAC,IAAI,IAAI,CAAA;AAEjD,CAAA;AAIA;;;;"}