{"version": 3, "file": "table-column-xform.js", "names": ["BaseXform", "require", "TableColumnXform", "tag", "prepare", "model", "options", "id", "index", "render", "xmlStream", "leafNode", "toString", "name", "totalsRowLabel", "totalsRowFunction", "dxfId", "parseOpen", "node", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/table/table-column-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass TableColumnXform extends BaseXform {\n  get tag() {\n    return 'tableColumn';\n  }\n\n  prepare(model, options) {\n    model.id = options.index + 1;\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      id: model.id.toString(),\n      name: model.name,\n      totalsRowLabel: model.totalsRowLabel,\n      totalsRowFunction: model.totalsRowFunction,\n      dxfId: model.dxfId,\n    });\n    return true;\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      const {attributes} = node;\n      this.model = {\n        name: attributes.name,\n        totalsRowLabel: attributes.totalsRowLabel,\n        totalsRowFunction: attributes.totalsRowFunction,\n        dxfId: attributes.dxfId,\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = TableColumnXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,gBAAgB,SAASF,SAAS,CAAC;EACvC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,aAAa;EACtB;EAEAC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtBD,KAAK,CAACE,EAAE,GAAGD,OAAO,CAACE,KAAK,GAAG,CAAC;EAC9B;EAEAC,MAAMA,CAACC,SAAS,EAAEL,KAAK,EAAE;IACvBK,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACR,GAAG,EAAE;MAC3BI,EAAE,EAAEF,KAAK,CAACE,EAAE,CAACK,QAAQ,CAAC,CAAC;MACvBC,IAAI,EAAER,KAAK,CAACQ,IAAI;MAChBC,cAAc,EAAET,KAAK,CAACS,cAAc;MACpCC,iBAAiB,EAAEV,KAAK,CAACU,iBAAiB;MAC1CC,KAAK,EAAEX,KAAK,CAACW;IACf,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACL,IAAI,KAAK,IAAI,CAACV,GAAG,EAAE;MAC1B,MAAM;QAACgB;MAAU,CAAC,GAAGD,IAAI;MACzB,IAAI,CAACb,KAAK,GAAG;QACXQ,IAAI,EAAEM,UAAU,CAACN,IAAI;QACrBC,cAAc,EAAEK,UAAU,CAACL,cAAc;QACzCC,iBAAiB,EAAEI,UAAU,CAACJ,iBAAiB;QAC/CC,KAAK,EAAEG,UAAU,CAACH;MACpB,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAI,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGrB,gBAAgB"}