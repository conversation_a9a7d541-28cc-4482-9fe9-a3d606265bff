{"version": 3, "file": "ext-lst-xform.js", "names": ["BaseXform", "require", "ExtLstXform", "tag", "render", "xmlStream", "openNode", "uri", "leafNode", "id", "closeNode", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/ext-lst-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass ExtLstXform extends BaseXform {\n  get tag() {\n    return 'a:extLst';\n  }\n\n  render(xmlStream) {\n    xmlStream.openNode(this.tag);\n    xmlStream.openNode('a:ext', {\n      uri: '{FF2B5EF4-FFF2-40B4-BE49-F238E27FC236}',\n    });\n    xmlStream.leafNode('a16:creationId', {\n      'xmlns:a16': 'http://schemas.microsoft.com/office/drawing/2014/main',\n      id: '{00000000-0008-0000-0000-000002000000}',\n    });\n    xmlStream.closeNode();\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        return true;\n      default:\n        return true;\n    }\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        // unprocessed internal nodes\n        return true;\n    }\n  }\n}\n\nmodule.exports = ExtLstXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,WAAW,SAASF,SAAS,CAAC;EAClC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,UAAU;EACnB;EAEAC,MAAMA,CAACC,SAAS,EAAE;IAChBA,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACH,GAAG,CAAC;IAC5BE,SAAS,CAACC,QAAQ,CAAC,OAAO,EAAE;MAC1BC,GAAG,EAAE;IACP,CAAC,CAAC;IACFF,SAAS,CAACG,QAAQ,CAAC,gBAAgB,EAAE;MACnC,WAAW,EAAE,uDAAuD;MACpEC,EAAE,EAAE;IACN,CAAC,CAAC;IACFJ,SAAS,CAACK,SAAS,CAAC,CAAC;IACrBL,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACV,GAAG;QACX,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAW,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACF,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,IAAI,CAACV,GAAG;QACX,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAa,MAAM,CAACC,OAAO,GAAGf,WAAW"}