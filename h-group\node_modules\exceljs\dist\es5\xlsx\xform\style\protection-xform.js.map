{"version": 3, "file": "protection-xform.js", "names": ["BaseXform", "require", "validation", "boolean", "value", "dflt", "undefined", "ProtectionXform", "tag", "render", "xmlStream", "model", "add<PERSON><PERSON><PERSON>", "openNode", "<PERSON><PERSON><PERSON><PERSON>", "add", "name", "addAttribute", "locked", "hidden", "closeNode", "commit", "rollback", "parseOpen", "node", "attributes", "isSignificant", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/protection-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nconst validation = {\n  boolean(value, dflt) {\n    if (value === undefined) {\n      return dflt;\n    }\n    return value;\n  },\n};\n\n// Protection encapsulates translation from style.protection model to/from xlsx\nclass ProtectionXform extends BaseXform {\n  get tag() {\n    return 'protection';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.addRollback();\n    xmlStream.openNode('protection');\n\n    let isValid = false;\n    function add(name, value) {\n      if (value !== undefined) {\n        xmlStream.addAttribute(name, value);\n        isValid = true;\n      }\n    }\n    add('locked', validation.boolean(model.locked, true) ? undefined : '0');\n    add('hidden', validation.boolean(model.hidden, false) ? '1' : undefined);\n\n    xmlStream.closeNode();\n\n    if (isValid) {\n      xmlStream.commit();\n    } else {\n      xmlStream.rollback();\n    }\n  }\n\n  parseOpen(node) {\n    const model = {\n      locked: !(node.attributes.locked === '0'),\n      hidden: node.attributes.hidden === '1',\n    };\n\n    // only want to record models that differ from defaults\n    const isSignificant = !model.locked || model.hidden;\n\n    this.model = isSignificant ? model : null;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = ProtectionXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,UAAU,GAAG;EACjBC,OAAOA,CAACC,KAAK,EAAEC,IAAI,EAAE;IACnB,IAAID,KAAK,KAAKE,SAAS,EAAE;MACvB,OAAOD,IAAI;IACb;IACA,OAAOD,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMG,eAAe,SAASP,SAAS,CAAC;EACtC,IAAIQ,GAAGA,CAAA,EAAG;IACR,OAAO,YAAY;EACrB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,WAAW,CAAC,CAAC;IACvBF,SAAS,CAACG,QAAQ,CAAC,YAAY,CAAC;IAEhC,IAAIC,OAAO,GAAG,KAAK;IACnB,SAASC,GAAGA,CAACC,IAAI,EAAEZ,KAAK,EAAE;MACxB,IAAIA,KAAK,KAAKE,SAAS,EAAE;QACvBI,SAAS,CAACO,YAAY,CAACD,IAAI,EAAEZ,KAAK,CAAC;QACnCU,OAAO,GAAG,IAAI;MAChB;IACF;IACAC,GAAG,CAAC,QAAQ,EAAEb,UAAU,CAACC,OAAO,CAACQ,KAAK,CAACO,MAAM,EAAE,IAAI,CAAC,GAAGZ,SAAS,GAAG,GAAG,CAAC;IACvES,GAAG,CAAC,QAAQ,EAAEb,UAAU,CAACC,OAAO,CAACQ,KAAK,CAACQ,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,GAAGb,SAAS,CAAC;IAExEI,SAAS,CAACU,SAAS,CAAC,CAAC;IAErB,IAAIN,OAAO,EAAE;MACXJ,SAAS,CAACW,MAAM,CAAC,CAAC;IACpB,CAAC,MAAM;MACLX,SAAS,CAACY,QAAQ,CAAC,CAAC;IACtB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,MAAMb,KAAK,GAAG;MACZO,MAAM,EAAE,EAAEM,IAAI,CAACC,UAAU,CAACP,MAAM,KAAK,GAAG,CAAC;MACzCC,MAAM,EAAEK,IAAI,CAACC,UAAU,CAACN,MAAM,KAAK;IACrC,CAAC;;IAED;IACA,MAAMO,aAAa,GAAG,CAACf,KAAK,CAACO,MAAM,IAAIP,KAAK,CAACQ,MAAM;IAEnD,IAAI,CAACR,KAAK,GAAGe,aAAa,GAAGf,KAAK,GAAG,IAAI;EAC3C;EAEAgB,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGvB,eAAe"}