/* تحسينات مدمجة لجميع صفحات التطبيق */

/* صفحة الطلبات */
.orders-page .card,
.orders-page .order-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 0.75rem;
}

.order-status {
  font-size: 0.6rem !important;
  padding: 0.15rem 0.4rem !important;
  border-radius: 4px !important;
}

.order-details {
  font-size: 0.7rem !important;
  line-height: 1.4 !important;
}

.order-amount {
  font-size: 1.1rem !important;
  font-weight: 700 !important;
}

/* صفحة العملاء */
.customers-page .card,
.customers-page .customer-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.customers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 0.75rem;
}

.customer-name {
  font-size: 0.8rem !important;
  font-weight: 600 !important;
}

.customer-info {
  font-size: 0.65rem !important;
  color: #6c757d !important;
}

.customer-stats {
  font-size: 0.7rem !important;
}

/* صفحة المواد الخام */
.materials-page .card,
.materials-page .material-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.material-name {
  font-size: 0.8rem !important;
  font-weight: 600 !important;
}

.material-quantity {
  font-size: 1rem !important;
  font-weight: 700 !important;
}

.material-unit {
  font-size: 0.65rem !important;
  color: #6c757d !important;
}

.material-price {
  font-size: 0.8rem !important;
  font-weight: 600 !important;
  color: #1e3c72 !important;
}

/* صفحة العمال */
.workers-page .card,
.workers-page .worker-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.workers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(190px, 1fr));
  gap: 0.75rem;
}

.worker-name {
  font-size: 0.8rem !important;
  font-weight: 600 !important;
}

.worker-role {
  font-size: 0.65rem !important;
  color: #6c757d !important;
}

.worker-salary {
  font-size: 0.9rem !important;
  font-weight: 700 !important;
  color: #27ae60 !important;
}

/* صفحة المخزون */
.inventory-page .card,
.inventory-page .inventory-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
}

.inventory-item {
  font-size: 0.75rem !important;
}

.inventory-level {
  font-size: 0.9rem !important;
  font-weight: 700 !important;
}

.inventory-status {
  font-size: 0.6rem !important;
  padding: 0.15rem 0.4rem !important;
}

/* صفحة التقارير */
.reports-page .card,
.reports-page .report-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.report-title {
  font-size: 0.8rem !important;
  font-weight: 600 !important;
}

.report-description {
  font-size: 0.65rem !important;
  color: #6c757d !important;
}

.report-date {
  font-size: 0.6rem !important;
  color: #6c757d !important;
}

/* صفحة الفواتير */
.invoices-page .card,
.invoices-page .invoice-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.invoices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 0.75rem;
}

.invoice-number {
  font-size: 0.8rem !important;
  font-weight: 600 !important;
}

.invoice-amount {
  font-size: 1.1rem !important;
  font-weight: 700 !important;
  color: #1e3c72 !important;
}

.invoice-status {
  font-size: 0.6rem !important;
  padding: 0.15rem 0.4rem !important;
}

/* صفحة المصروفات */
.expenses-page .card,
.expenses-page .expense-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.expenses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.expense-category {
  font-size: 0.7rem !important;
  color: #6c757d !important;
}

.expense-amount {
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: #e74c3c !important;
}

.expense-date {
  font-size: 0.65rem !important;
  color: #6c757d !important;
}

/* صفحة الإعدادات */
.settings-page .card,
.settings-page .setting-card {
  padding: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
}

.setting-title {
  font-size: 0.8rem !important;
  font-weight: 600 !important;
}

.setting-description {
  font-size: 0.65rem !important;
  color: #6c757d !important;
}

/* عناصر النماذج في جميع الصفحات */
.form-section {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.form-section-title {
  font-size: 0.9rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  color: #2c3e50 !important;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

/* أزرار الإجراءات */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.4rem 0.8rem !important;
  font-size: 0.7rem !important;
  border-radius: 6px !important;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
}

.action-btn i {
  font-size: 0.7rem !important;
}

/* تحسينات للشاشات المتوسطة */
@media (max-width: 992px) {
  .orders-grid,
  .customers-grid,
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .reports-grid,
  .invoices-grid,
  .expenses-grid,
  .settings-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.6rem;
  }
  
  .form-row {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.6rem;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .orders-grid,
  .customers-grid,
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .reports-grid,
  .invoices-grid,
  .expenses-grid,
  .settings-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .action-buttons {
    gap: 0.4rem;
  }
  
  .action-btn {
    padding: 0.3rem 0.6rem !important;
    font-size: 0.65rem !important;
  }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
  .orders-grid,
  .customers-grid,
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .reports-grid,
  .invoices-grid,
  .expenses-grid,
  .settings-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.4rem;
  }
  
  .action-btn {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.6rem !important;
  }
  
  .form-section {
    padding: 0.6rem;
  }
  
  .form-section-title {
    font-size: 0.8rem !important;
  }
}
