/* التصميم الاحترافي المتقدم لتطبيق H Group */

/* الألوان الأساسية المحدثة */
:root {
  --primary-color: #1e3c72;
  --primary-light: #2a5298;
  --primary-dark: #1a2f5c;
  --secondary-color: #ff6b35;
  --secondary-light: #ff8c5a;
  --secondary-dark: #e55a2b;
  --accent-color: #f7931e;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #3498db;
  --purple-color: #9b59b6;
  --light-color: #ecf0f1;
  --dark-color: #2c3e50;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;

  /* متغيرات التدرجات */
  --gradient-primary: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  --gradient-success: linear-gradient(135deg, #27ae60 0%, #229954 100%);
  --gradient-warning: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  --gradient-danger: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  --gradient-info: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  --gradient-purple: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);

  /* ظلال متقدمة */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
  --shadow-2xl: 0 16px 32px rgba(0, 0, 0, 0.2);

  /* انتقالات */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* نصوص */
  --font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
}

/* تخطيط التطبيق الجديد */
.app-container {
  min-height: 100vh;
  background: var(--gray-100);
  display: flex;
  flex-direction: column;
}

/* الشريط العلوي الجديد */
.navbar {
  background: var(--gradient-primary);
  color: white;
  padding: 0.75rem 0;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  text-decoration: none;
  font-size: var(--font-size-xl);
  font-weight: 700;
  transition: all var(--transition-normal);
}

.navbar-brand:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

.navbar-brand i {
  font-size: 1.5rem;
  color: var(--secondary-color);
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-menu {
  position: relative;
}

.user-menu-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all var(--transition-normal);
  font-size: var(--font-size-sm);
}

.user-menu-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow-xl);
  min-width: 200px;
  margin-top: 0.5rem;
  overflow: hidden;
  z-index: 1001;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--dark-color);
  text-decoration: none;
  transition: all var(--transition-fast);
  border: none;
  background: none;
  width: 100%;
  text-align: right;
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.user-menu-item:hover {
  background: var(--gray-100);
  color: var(--primary-color);
  text-decoration: none;
}

.logout-button {
  border-top: 1px solid var(--gray-200);
  color: var(--danger-color);
}

.logout-button:hover {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
}

/* شريط التنقل الرئيسي */
.main-navigation {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow-x: auto;
  padding: 0.5rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--gray-600);
  text-decoration: none;
  border-radius: 8px;
  transition: all var(--transition-normal);
  font-size: var(--font-size-sm);
  font-weight: 500;
  white-space: nowrap;
  position: relative;
}

.nav-item:hover {
  background: rgba(30, 60, 114, 0.1);
  color: var(--primary-color);
  text-decoration: none;
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: var(--secondary-color);
  border-radius: 50%;
}

.nav-item i {
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

/* منطقة المحتوى */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 0.75rem;
  }

  .navbar-brand {
    font-size: var(--font-size-lg);
  }

  .nav-container {
    padding: 0 0.75rem;
  }

  .nav-menu {
    gap: 0.25rem;
  }

  .nav-item {
    padding: 0.5rem 0.75rem;
    font-size: var(--font-size-xs);
  }

  .main-content {
    padding: 1rem;
  }
}

/* إعادة تعيين أساسية */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background: var(--gradient-primary);
  color: var(--dark-color);
  line-height: 1.6;
  direction: rtl;
  min-height: 100vh;
}

/* الأزرار المحسنة */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 12px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:active {
  transform: translateY(0);
}

/* أنواع الأزرار */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  box-shadow: 0 6px 20px rgba(30, 60, 114, 0.4);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.btn-success {
  background: var(--gradient-success);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-warning {
  background: var(--gradient-warning);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.btn-danger {
  background: var(--gradient-danger);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.btn-info {
  background: var(--gradient-info);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-info:hover {
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-purple {
  background: var(--gradient-purple);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-purple:hover {
  box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

/* أحجام الأزرار */
.btn-sm {
  padding: 6px 12px;
  font-size: var(--font-size-xs);
  border-radius: 8px;
}

.btn-lg {
  padding: 14px 28px;
  font-size: var(--font-size-lg);
  border-radius: 16px;
}

/* مجموعات الأزرار */
.btn-group {
  display: inline-flex;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.btn-group .btn {
  border-radius: 0;
  margin: 0;
}

.btn-group .btn:first-child {
  border-radius: 12px 0 0 12px;
}

.btn-group .btn:last-child {
  border-radius: 0 12px 12px 0;
}

.btn-group .btn:only-child {
  border-radius: 12px;
}

/* البطاقات المحسنة */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 16px 16px 0 0;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-2xl);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(229, 231, 235, 0.5);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
}

.card-title i {
  color: var(--secondary-color);
  font-size: 1.1em;
}

.card-body {
  padding: 0;
}

.card-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.25rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
}

/* الجداول المحسنة */
.table-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 1.5rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
  background: transparent;
}

.table thead {
  background: var(--gradient-primary);
  color: white;
}

.table th {
  padding: 1rem 0.875rem;
  text-align: right;
  font-weight: 600;
  font-size: var(--font-size-sm);
  border: none;
  letter-spacing: 0.5px;
}

.table td {
  padding: 0.875rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  transition: background-color var(--transition-fast);
}

.table tbody tr {
  transition: all var(--transition-fast);
}

.table tbody tr:hover {
  background-color: rgba(30, 60, 114, 0.05);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* تحسينات إضافية للجداول */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-striped tbody tr:nth-child(odd) {
  background-color: rgba(248, 250, 252, 0.5);
}

.table-bordered {
  border: 1px solid var(--gray-200);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--gray-200);
}

/* شارات الحالة */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.success {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(39, 174, 96, 0.3);
}

.status-badge.warning {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(243, 156, 18, 0.3);
}

.status-badge.danger {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.status-badge.info {
  background: rgba(52, 152, 219, 0.1);
  color: var(--info-color);
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.status-badge.secondary {
  background: rgba(108, 117, 125, 0.1);
  color: var(--gray-600);
  border: 1px solid rgba(108, 117, 125, 0.3);
}

/* النماذج المحسنة */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--dark-color);
  font-size: var(--font-size-sm);
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--gray-300);
  border-radius: 12px;
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: var(--primary-light);
}

/* النوافذ المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn var(--transition-normal);
}

.modal-content {
  background: white;
  border-radius: 20px;
  padding: 30px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-2xl);
  animation: slideUp var(--transition-normal);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* شاشة التحميل */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--gradient-primary);
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--secondary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .btn {
    padding: 8px 16px;
    font-size: var(--font-size-xs);
    border-radius: 10px;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0;
  }

  .btn-group .btn:first-child {
    border-radius: 10px 10px 0 0;
  }

  .btn-group .btn:last-child {
    border-radius: 0 0 10px 10px;
  }

  .card {
    padding: 1.25rem;
    border-radius: 12px;
    margin-bottom: 1.25rem;
  }

  .card-header {
    margin-bottom: 1rem;
    padding-bottom: 0.875rem;
  }

  .card-title {
    font-size: var(--font-size-lg);
  }

  .table-container {
    border-radius: 12px;
  }

  .table {
    font-size: var(--font-size-xs);
  }

  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }

  .modal-content {
    padding: 1.25rem;
    border-radius: 12px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-control {
    padding: 0.75rem;
    border-radius: 10px;
  }
}

@media (max-width: 480px) {
  .card {
    padding: 1rem;
    border-radius: 10px;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.375rem;
    font-size: 0.75rem;
  }

  .btn {
    padding: 6px 12px;
    font-size: 0.75rem;
  }

  .card-title {
    font-size: var(--font-size-base);
  }
}

/* أنماط التبويبات الجديدة للصفحات المدموجة */
.tabs-container {
  display: flex;
  gap: 8px;
  margin-bottom: 0;
  border-bottom: 2px solid var(--gray-200);
  padding-bottom: 0;
}

.tab-btn {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all var(--transition-normal);
  color: var(--gray-600);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.tab-btn:hover {
  background: rgba(30, 60, 114, 0.1);
  color: var(--primary-color);
}

.tab-btn.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-md);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
}

.tab-content {
  padding: 24px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--gray-200);
}

.section-header h3 {
  margin: 0;
  color: var(--dark-color);
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.section-actions {
  display: flex;
  gap: 12px;
}

/* أنماط الفونير */
.veneer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.veneer-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  padding: 20px;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.veneer-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.veneer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.veneer-header h4 {
  margin: 0;
  color: var(--dark-color);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.veneer-unit {
  background: rgba(30, 60, 114, 0.1);
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.veneer-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item .label {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.detail-item .value {
  color: var(--dark-color);
  font-weight: 500;
}

.veneer-actions {
  text-align: center;
}

/* أنماط التقارير المالية */
.financial-summary {
  margin-top: 24px;
}

.summary-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-sm);
}

.summary-card h4 {
  margin: 0 0 20px 0;
  color: var(--dark-color);
  font-size: var(--font-size-xl);
  font-weight: 600;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--gray-200);
}

.summary-card.revenue {
  border-left: 4px solid var(--success-color);
}

.summary-card.expenses {
  border-left: 4px solid var(--danger-color);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 0;
}

.summary-item .label {
  color: var(--gray-600);
  font-weight: 500;
}

.summary-item .value {
  color: var(--dark-color);
  font-weight: 600;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 2px solid var(--gray-200);
  font-size: var(--font-size-lg);
}

.summary-total .label {
  color: var(--dark-color);
  font-weight: 600;
}

.summary-total .value {
  color: var(--primary-color);
  font-weight: 700;
}

.profit-summary {
  margin-top: 24px;
  text-align: center;
}

.profit-card {
  background: var(--gradient-primary);
  color: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: var(--shadow-xl);
}

.profit-card h3 {
  margin: 0 0 16px 0;
  font-size: var(--font-size-2xl);
  font-weight: 600;
}

.profit-amount {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: 12px;
}

.profit-amount.positive {
  color: var(--success-color);
}

.profit-amount.negative {
  color: var(--danger-color);
}

.profit-details {
  font-size: var(--font-size-base);
  opacity: 0.9;
}

.quick-reports {
  margin-top: 32px;
}

.quick-reports h4 {
  margin-bottom: 20px;
  color: var(--dark-color);
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.report-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  text-decoration: none;
  color: var(--dark-color);
  transition: all var(--transition-normal);
  font-weight: 500;
  box-shadow: var(--shadow-sm);
}

.report-link:hover {
  background: rgba(30, 60, 114, 0.05);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

.report-link i {
  font-size: var(--font-size-xl);
  width: 24px;
  text-align: center;
}

/* أنماط معلومات العامل */
.worker-info {
  display: flex;
  flex-direction: column;
}

.worker-info strong {
  color: var(--dark-color);
  font-weight: 600;
}

.worker-info small {
  color: var(--gray-600);
  font-size: var(--font-size-xs);
  margin-top: 2px;
}
