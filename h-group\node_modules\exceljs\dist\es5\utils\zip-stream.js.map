{"version": 3, "file": "zip-stream.js", "names": ["events", "require", "JSZip", "StreamBuf", "string<PERSON>o<PERSON>uffer", "ZipWriter", "EventEmitter", "constructor", "options", "Object", "assign", "type", "compression", "zip", "stream", "append", "data", "hasOwnProperty", "base64", "file", "name", "process", "browser", "finalize", "content", "generateAsync", "end", "emit", "read", "size", "setEncoding", "encoding", "pause", "resume", "isPaused", "pipe", "destination", "unpipe", "unshift", "chunk", "wrap", "module", "exports"], "sources": ["../../../lib/utils/zip-stream.js"], "sourcesContent": ["const events = require('events');\nconst JSZip = require('jszip');\n\nconst StreamBuf = require('./stream-buf');\nconst {stringToBuffer} = require('./browser-buffer-encode');\n\n// =============================================================================\n// The ZipWriter class\n// Packs streamed data into an output zip stream\nclass ZipWriter extends events.EventEmitter {\n  constructor(options) {\n    super();\n    this.options = Object.assign(\n      {\n        type: 'nodebuffer',\n        compression: 'DEFLATE',\n      },\n      options\n    );\n\n    this.zip = new JSZip();\n    this.stream = new StreamBuf();\n  }\n\n  append(data, options) {\n    if (options.hasOwnProperty('base64') && options.base64) {\n      this.zip.file(options.name, data, {base64: true});\n    } else {\n      // https://www.npmjs.com/package/process\n      if (process.browser && typeof data === 'string') {\n        // use TextEncoder in browser\n        data = stringToBuffer(data);\n      }\n      this.zip.file(options.name, data);\n    }\n  }\n\n  async finalize() {\n    const content = await this.zip.generateAsync(this.options);\n    this.stream.end(content);\n    this.emit('finish');\n  }\n\n  // ==========================================================================\n  // Stream.Readable interface\n  read(size) {\n    return this.stream.read(size);\n  }\n\n  setEncoding(encoding) {\n    return this.stream.setEncoding(encoding);\n  }\n\n  pause() {\n    return this.stream.pause();\n  }\n\n  resume() {\n    return this.stream.resume();\n  }\n\n  isPaused() {\n    return this.stream.isPaused();\n  }\n\n  pipe(destination, options) {\n    return this.stream.pipe(destination, options);\n  }\n\n  unpipe(destination) {\n    return this.stream.unpipe(destination);\n  }\n\n  unshift(chunk) {\n    return this.stream.unshift(chunk);\n  }\n\n  wrap(stream) {\n    return this.stream.wrap(stream);\n  }\n}\n\n// =============================================================================\n\nmodule.exports = {\n  ZipWriter,\n};\n"], "mappings": ";;AAAA,MAAMA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAMC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAE9B,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;AACzC,MAAM;EAACG;AAAc,CAAC,GAAGH,OAAO,CAAC,yBAAyB,CAAC;;AAE3D;AACA;AACA;AACA,MAAMI,SAAS,SAASL,MAAM,CAACM,YAAY,CAAC;EAC1CC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGC,MAAM,CAACC,MAAM,CAC1B;MACEC,IAAI,EAAE,YAAY;MAClBC,WAAW,EAAE;IACf,CAAC,EACDJ,OACF,CAAC;IAED,IAAI,CAACK,GAAG,GAAG,IAAIX,KAAK,CAAC,CAAC;IACtB,IAAI,CAACY,MAAM,GAAG,IAAIX,SAAS,CAAC,CAAC;EAC/B;EAEAY,MAAMA,CAACC,IAAI,EAAER,OAAO,EAAE;IACpB,IAAIA,OAAO,CAACS,cAAc,CAAC,QAAQ,CAAC,IAAIT,OAAO,CAACU,MAAM,EAAE;MACtD,IAAI,CAACL,GAAG,CAACM,IAAI,CAACX,OAAO,CAACY,IAAI,EAAEJ,IAAI,EAAE;QAACE,MAAM,EAAE;MAAI,CAAC,CAAC;IACnD,CAAC,MAAM;MACL;MACA,IAAIG,OAAO,CAACC,OAAO,IAAI,OAAON,IAAI,KAAK,QAAQ,EAAE;QAC/C;QACAA,IAAI,GAAGZ,cAAc,CAACY,IAAI,CAAC;MAC7B;MACA,IAAI,CAACH,GAAG,CAACM,IAAI,CAACX,OAAO,CAACY,IAAI,EAAEJ,IAAI,CAAC;IACnC;EACF;EAEA,MAAMO,QAAQA,CAAA,EAAG;IACf,MAAMC,OAAO,GAAG,MAAM,IAAI,CAACX,GAAG,CAACY,aAAa,CAAC,IAAI,CAACjB,OAAO,CAAC;IAC1D,IAAI,CAACM,MAAM,CAACY,GAAG,CAACF,OAAO,CAAC;IACxB,IAAI,CAACG,IAAI,CAAC,QAAQ,CAAC;EACrB;;EAEA;EACA;EACAC,IAAIA,CAACC,IAAI,EAAE;IACT,OAAO,IAAI,CAACf,MAAM,CAACc,IAAI,CAACC,IAAI,CAAC;EAC/B;EAEAC,WAAWA,CAACC,QAAQ,EAAE;IACpB,OAAO,IAAI,CAACjB,MAAM,CAACgB,WAAW,CAACC,QAAQ,CAAC;EAC1C;EAEAC,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClB,MAAM,CAACkB,KAAK,CAAC,CAAC;EAC5B;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnB,MAAM,CAACmB,MAAM,CAAC,CAAC;EAC7B;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACpB,MAAM,CAACoB,QAAQ,CAAC,CAAC;EAC/B;EAEAC,IAAIA,CAACC,WAAW,EAAE5B,OAAO,EAAE;IACzB,OAAO,IAAI,CAACM,MAAM,CAACqB,IAAI,CAACC,WAAW,EAAE5B,OAAO,CAAC;EAC/C;EAEA6B,MAAMA,CAACD,WAAW,EAAE;IAClB,OAAO,IAAI,CAACtB,MAAM,CAACuB,MAAM,CAACD,WAAW,CAAC;EACxC;EAEAE,OAAOA,CAACC,KAAK,EAAE;IACb,OAAO,IAAI,CAACzB,MAAM,CAACwB,OAAO,CAACC,KAAK,CAAC;EACnC;EAEAC,IAAIA,CAAC1B,MAAM,EAAE;IACX,OAAO,IAAI,CAACA,MAAM,CAAC0B,IAAI,CAAC1B,MAAM,CAAC;EACjC;AACF;;AAEA;;AAEA2B,MAAM,CAACC,OAAO,GAAG;EACfrC;AACF,CAAC"}