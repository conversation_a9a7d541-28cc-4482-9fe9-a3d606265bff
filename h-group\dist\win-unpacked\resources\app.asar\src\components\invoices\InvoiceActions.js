const React = require('react');
const { useState } = React;
const { useAuth } = require('../../contexts/AuthContext');

const InvoiceActions = ({ invoice }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { hasPermission } = useAuth();

  // طباعة الفاتورة
  const handlePrintInvoice = async () => {
    try {
      if (!invoice || !invoice.id) {
        alert('لا توجد فاتورة للطباعة');
        return;
      }
      
      setLoading(true);
      setError(null);
      
      // توليد ملف PDF للفاتورة
      const result = await window.api.invoices.generatePDF(invoice.id);
      
      if (result.success) {
        // فتح ملف PDF
        window.open(`file://${result.path}`, '_blank');
        
        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'print_invoice',
          action_details: `طباعة الفاتورة رقم ${invoice.invoice_number}`
        });
      } else {
        throw new Error('فشل في إنشاء ملف PDF للفاتورة');
      }
    } catch (error) {
      console.error('خطأ في طباعة الفاتورة:', error);
      setError('حدث خطأ أثناء طباعة الفاتورة. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // إرسال الفاتورة بالبريد الإلكتروني
  const handleEmailInvoice = async () => {
    try {
      if (!invoice || !invoice.id) {
        alert('لا توجد فاتورة للإرسال');
        return;
      }
      
      // التحقق من وجود بريد إلكتروني للعميل
      if (!invoice.customer_email) {
        alert('لا يوجد بريد إلكتروني للعميل');
        return;
      }
      
      setLoading(true);
      setError(null);
      
      // توليد ملف PDF للفاتورة
      const result = await window.api.invoices.generatePDF(invoice.id);
      
      if (result.success) {
        // فتح تطبيق البريد الإلكتروني الافتراضي
        const subject = `فاتورة رقم ${invoice.invoice_number} - اتش قروب`;
        const body = `مرحباً ${invoice.customer_name}،\n\nمرفق فاتورة رقم ${invoice.invoice_number}.\n\nشكراً لتعاملكم معنا،\nاتش قروب`;
        
        window.open(`mailto:${invoice.customer_email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}&attachment=${encodeURIComponent(result.path)}`, '_blank');
        
        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'email_invoice',
          action_details: `إرسال الفاتورة رقم ${invoice.invoice_number} بالبريد الإلكتروني`
        });
      } else {
        throw new Error('فشل في إنشاء ملف PDF للفاتورة');
      }
    } catch (error) {
      console.error('خطأ في إرسال الفاتورة بالبريد الإلكتروني:', error);
      setError('حدث خطأ أثناء إرسال الفاتورة بالبريد الإلكتروني. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // مشاركة الفاتورة عبر WhatsApp
  const handleShareViaWhatsApp = async () => {
    try {
      if (!invoice || !invoice.id) {
        alert('لا توجد فاتورة للمشاركة');
        return;
      }
      
      // التحقق من وجود رقم هاتف للعميل
      if (!invoice.customer_phone) {
        alert('لا يوجد رقم هاتف للعميل');
        return;
      }
      
      setLoading(true);
      setError(null);
      
      // إعداد رسالة WhatsApp
      const message = `مرحباً ${invoice.customer_name}،\n\nفاتورة رقم ${invoice.invoice_number}\nالمبلغ: ${invoice.total_amount.toLocaleString()} ر.س\nالتاريخ: ${new Date(invoice.issue_date).toLocaleDateString('ar-SA')}\n\nشكراً لتعاملكم معنا،\nاتش قروب`;
      
      // تنسيق رقم الهاتف (إزالة الرمز + و00 في البداية)
      let phoneNumber = invoice.customer_phone.replace(/^\+|^00/, '');
      
      // فتح WhatsApp
      window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank');
      
      // إضافة سجل في سجل العمليات
      await window.api.activityLog.create({
        action_type: 'share_invoice',
        action_details: `مشاركة الفاتورة رقم ${invoice.invoice_number} عبر WhatsApp`
      });
    } catch (error) {
      console.error('خطأ في مشاركة الفاتورة عبر WhatsApp:', error);
      setError('حدث خطأ أثناء مشاركة الفاتورة عبر WhatsApp. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="invoice-actions">
      {error && <div className="alert alert-danger">{error}</div>}
      
      <button 
        className="invoice-print-btn"
        onClick={handlePrintInvoice}
        disabled={loading || !hasPermission('invoices_view')}
      >
        <i className="fas fa-print"></i>
        <span>طباعة الفاتورة</span>
      </button>
      
      <button 
        className="invoice-email-btn"
        onClick={handleEmailInvoice}
        disabled={loading || !invoice?.customer_email || !hasPermission('invoices_view')}
      >
        <i className="fas fa-envelope"></i>
        <span>إرسال بالبريد الإلكتروني</span>
      </button>
      
      <button 
        className="btn btn-success"
        onClick={handleShareViaWhatsApp}
        disabled={loading || !invoice?.customer_phone || !hasPermission('invoices_view')}
      >
        <i className="fab fa-whatsapp"></i>
        <span>مشاركة عبر WhatsApp</span>
      </button>
    </div>
  );
};

module.exports = InvoiceActions;
