{"version": 3, "file": "line-buffer.js", "names": ["EventEmitter", "require", "LineBuffer", "constructor", "options", "encoding", "buffer", "corked", "queue", "write", "chunk", "data", "lines", "split", "pop", "for<PERSON>ach", "line", "push", "emit", "cork", "uncork", "_flush", "setDefaultEncoding", "end", "module", "exports"], "sources": ["../../../lib/csv/line-buffer.js"], "sourcesContent": ["const {EventEmitter} = require('events');\n\nclass LineBuffer extends EventEmitter {\n  constructor(options) {\n    super();\n\n    this.encoding = options.encoding;\n\n    this.buffer = null;\n\n    // part of cork/uncork\n    this.corked = false;\n    this.queue = [];\n  }\n\n  // Events:\n  //  line: here is a line\n  //  done: all lines emitted\n\n  write(chunk) {\n    // find line or lines in chunk and emit them if not corked\n    // or queue them if corked\n    const data = this.buffer ? this.buffer + chunk : chunk;\n    const lines = data.split(/\\r?\\n/g);\n\n    // save the last line\n    this.buffer = lines.pop();\n\n    lines.forEach(function(line) {\n      if (this.corked) {\n        this.queue.push(line);\n      } else {\n        this.emit('line', line);\n      }\n    });\n\n    return !this.corked;\n  }\n\n  cork() {\n    this.corked = true;\n  }\n\n  uncork() {\n    this.corked = false;\n    this._flush();\n\n    // tell the source I'm ready again\n    this.emit('drain');\n  }\n\n  setDefaultEncoding() {\n    // ?\n  }\n\n  end() {\n    if (this.buffer) {\n      this.emit('line', this.buffer);\n      this.buffer = null;\n    }\n    this.emit('done');\n  }\n\n  _flush() {\n    if (!this.corked) {\n      this.queue.forEach(line => {\n        this.emit('line', line);\n      });\n      this.queue = [];\n    }\n  }\n}\n\nmodule.exports = LineBuffer;\n"], "mappings": ";;AAAA,MAAM;EAACA;AAAY,CAAC,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAExC,MAAMC,UAAU,SAASF,YAAY,CAAC;EACpCG,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,QAAQ,GAAGD,OAAO,CAACC,QAAQ;IAEhC,IAAI,CAACC,MAAM,GAAG,IAAI;;IAElB;IACA,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;EACjB;;EAEA;EACA;EACA;;EAEAC,KAAKA,CAACC,KAAK,EAAE;IACX;IACA;IACA,MAAMC,IAAI,GAAG,IAAI,CAACL,MAAM,GAAG,IAAI,CAACA,MAAM,GAAGI,KAAK,GAAGA,KAAK;IACtD,MAAME,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,QAAQ,CAAC;;IAElC;IACA,IAAI,CAACP,MAAM,GAAGM,KAAK,CAACE,GAAG,CAAC,CAAC;IAEzBF,KAAK,CAACG,OAAO,CAAC,UAASC,IAAI,EAAE;MAC3B,IAAI,IAAI,CAACT,MAAM,EAAE;QACf,IAAI,CAACC,KAAK,CAACS,IAAI,CAACD,IAAI,CAAC;MACvB,CAAC,MAAM;QACL,IAAI,CAACE,IAAI,CAAC,MAAM,EAAEF,IAAI,CAAC;MACzB;IACF,CAAC,CAAC;IAEF,OAAO,CAAC,IAAI,CAACT,MAAM;EACrB;EAEAY,IAAIA,CAAA,EAAG;IACL,IAAI,CAACZ,MAAM,GAAG,IAAI;EACpB;EAEAa,MAAMA,CAAA,EAAG;IACP,IAAI,CAACb,MAAM,GAAG,KAAK;IACnB,IAAI,CAACc,MAAM,CAAC,CAAC;;IAEb;IACA,IAAI,CAACH,IAAI,CAAC,OAAO,CAAC;EACpB;EAEAI,kBAAkBA,CAAA,EAAG;IACnB;EAAA;EAGFC,GAAGA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACjB,MAAM,EAAE;MACf,IAAI,CAACY,IAAI,CAAC,MAAM,EAAE,IAAI,CAACZ,MAAM,CAAC;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI;IACpB;IACA,IAAI,CAACY,IAAI,CAAC,MAAM,CAAC;EACnB;EAEAG,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACd,MAAM,EAAE;MAChB,IAAI,CAACC,KAAK,CAACO,OAAO,CAACC,IAAI,IAAI;QACzB,IAAI,CAACE,IAAI,CAAC,MAAM,EAAEF,IAAI,CAAC;MACzB,CAAC,CAAC;MACF,IAAI,CAACR,KAAK,GAAG,EAAE;IACjB;EACF;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGvB,UAAU"}