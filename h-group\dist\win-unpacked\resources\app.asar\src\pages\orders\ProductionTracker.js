const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { formatCurrency, formatDate } = require('../../utils/formatters');

// استيراد ملف CSS للتصميم الاحترافي
require('../../styles/ProductionTracker.css');

const ProductionTracker = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [order, setOrder] = useState(null);
  const [productionStages, setProductionStages] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [selectedStage, setSelectedStage] = useState(null);
  const [showStageModal, setShowStageModal] = useState(false);

  // تحميل البيانات الأولية
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        const [orderData, stagesData, workersData] = await Promise.all([
          window.api.orders.getById(orderId),
          window.api.productionStages.getByOrderId(orderId),
          window.api.workers.getAll()
        ]);

        setOrder(orderData);
        setProductionStages(stagesData);
        setWorkers(workersData);
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        setError('حدث خطأ أثناء تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    if (orderId) {
      fetchData();
    }
  }, [orderId]);

  // التحقق من الصلاحيات
  useEffect(() => {
    if (!hasPermission('orders_view')) {
      alert('ليس لديك صلاحية عرض الطلبات');
      navigate('/orders');
    }
  }, [hasPermission, navigate]);

  // تحديث حالة المرحلة
  const updateStageStatus = async (stageId, status, completionPercentage) => {
    try {
      setLoading(true);
      
      await window.api.productionStages.updateStatus(stageId, status, completionPercentage);
      
      // إعادة تحميل مراحل الإنتاج
      const updatedStages = await window.api.productionStages.getByOrderId(orderId);
      setProductionStages(updatedStages);
      
      // إغلاق النافذة المنبثقة
      setShowStageModal(false);
      setSelectedStage(null);
      
    } catch (error) {
      console.error('خطأ في تحديث حالة المرحلة:', error);
      setError('حدث خطأ أثناء تحديث حالة المرحلة');
    } finally {
      setLoading(false);
    }
  };

  // تحديث تفاصيل المرحلة
  const updateStageDetails = async (stageData) => {
    try {
      setLoading(true);
      
      await window.api.productionStages.update(selectedStage.id, stageData);
      
      // إعادة تحميل مراحل الإنتاج
      const updatedStages = await window.api.productionStages.getByOrderId(orderId);
      setProductionStages(updatedStages);
      
      // إغلاق النافذة المنبثقة
      setShowStageModal(false);
      setSelectedStage(null);
      
    } catch (error) {
      console.error('خطأ في تحديث تفاصيل المرحلة:', error);
      setError('حدث خطأ أثناء تحديث تفاصيل المرحلة');
    } finally {
      setLoading(false);
    }
  };

  // حساب نسبة الإنجاز الإجمالية
  const calculateOverallProgress = () => {
    if (productionStages.length === 0) return 0;
    
    const totalProgress = productionStages.reduce((sum, stage) => {
      return sum + (stage.completion_percentage || 0);
    }, 0);
    
    return Math.round(totalProgress / productionStages.length);
  };

  // الحصول على لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'لم تبدأ': return 'secondary';
      case 'قيد التنفيذ': return 'primary';
      case 'مكتملة': return 'success';
      case 'متأخرة': return 'danger';
      case 'معلقة': return 'warning';
      default: return 'secondary';
    }
  };

  // الحصول على أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'لم تبدأ': return 'fas fa-clock';
      case 'قيد التنفيذ': return 'fas fa-play';
      case 'مكتملة': return 'fas fa-check';
      case 'متأخرة': return 'fas fa-exclamation-triangle';
      case 'معلقة': return 'fas fa-pause';
      default: return 'fas fa-question';
    }
  };

  if (loading) {
    return (
      <div className="container-fluid">
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">جاري التحميل...</span>
            </div>
            <p className="mt-3">جاري تحميل بيانات الإنتاج...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container-fluid">
        <div className="alert alert-danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          لم يتم العثور على الطلب المطلوب
        </div>
      </div>
    );
  }

  const overallProgress = calculateOverallProgress();

  return (
    <div className="container-fluid production-tracker">
      {/* رأس الصفحة */}
      <div className="page-header">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h1 className="page-title">
              <i className="fas fa-tasks me-2"></i>
              تتبع مراحل الإنتاج
            </h1>
            <p className="page-subtitle">
              طلب رقم: {order.order_number} - {order.product_name}
            </p>
          </div>
          <div>
            <button
              className="btn btn-secondary me-2"
              onClick={() => navigate('/orders')}
            >
              <i className="fas fa-arrow-right me-2"></i>
              العودة للطلبات
            </button>
            <button
              className="btn btn-primary"
              onClick={() => navigate(`/orders/edit/${orderId}`)}
            >
              <i className="fas fa-edit me-2"></i>
              تعديل الطلب
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="alert alert-danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </div>
      )}

      {/* معلومات الطلب */}
      <div className="row mb-4">
        <div className="col-md-8">
          <div className="card order-info-card">
            <div className="card-header">
              <h5 className="card-title">
                <i className="fas fa-info-circle me-2"></i>
                معلومات الطلب
              </h5>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-6">
                  <p><strong>العميل:</strong> {order.customer_name}</p>
                  <p><strong>المنتج:</strong> {order.product_name}</p>
                  <p><strong>الفئة:</strong> {order.product_category || '-'}</p>
                  <p><strong>الأبعاد:</strong> {order.dimensions || '-'}</p>
                </div>
                <div className="col-md-6">
                  <p><strong>تاريخ الطلب:</strong> {formatDate(order.order_date)}</p>
                  <p><strong>تاريخ التسليم:</strong> {formatDate(order.delivery_date)}</p>
                  <p><strong>الأولوية:</strong> {order.priority_level}</p>
                  <p><strong>التعقيد:</strong> {order.complexity_level}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-4">
          <div className="card progress-card">
            <div className="card-header">
              <h5 className="card-title">
                <i className="fas fa-chart-pie me-2"></i>
                نسبة الإنجاز الإجمالية
              </h5>
            </div>
            <div className="card-body text-center">
              <div className="progress-circle">
                <div className="progress-value">{overallProgress}%</div>
              </div>
              <div className="progress-bar-container mt-3">
                <div className="progress">
                  <div 
                    className="progress-bar bg-success" 
                    style={{ width: `${overallProgress}%` }}
                  ></div>
                </div>
              </div>
              <p className="mt-2 text-muted">
                {productionStages.filter(s => s.status === 'مكتملة').length} من {productionStages.length} مراحل مكتملة
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* مراحل الإنتاج */}
      <div className="card stages-card">
        <div className="card-header">
          <h5 className="card-title">
            <i className="fas fa-list-ol me-2"></i>
            مراحل الإنتاج
          </h5>
        </div>
        <div className="card-body">
          {productionStages.length > 0 ? (
            <div className="stages-timeline">
              {productionStages.map((stage, index) => (
                <div key={stage.id} className={`stage-item ${stage.status}`}>
                  <div className="stage-connector">
                    {index < productionStages.length - 1 && <div className="connector-line"></div>}
                  </div>
                  
                  <div className="stage-icon">
                    <i className={getStatusIcon(stage.status)}></i>
                  </div>
                  
                  <div className="stage-content">
                    <div className="stage-header">
                      <h6 className="stage-title">{stage.stage_name}</h6>
                      <span className={`badge bg-${getStatusColor(stage.status)}`}>
                        {stage.status}
                      </span>
                    </div>
                    
                    <p className="stage-description">{stage.description}</p>
                    
                    <div className="stage-details">
                      <div className="row">
                        <div className="col-md-6">
                          <small className="text-muted">
                            <i className="fas fa-user me-1"></i>
                            العامل: {stage.worker_name || 'غير محدد'}
                          </small>
                        </div>
                        <div className="col-md-6">
                          <small className="text-muted">
                            <i className="fas fa-clock me-1"></i>
                            الساعات المقدرة: {stage.estimated_hours || 0}
                          </small>
                        </div>
                      </div>
                      
                      {stage.status !== 'لم تبدأ' && (
                        <div className="stage-progress mt-2">
                          <div className="d-flex justify-content-between align-items-center">
                            <small>نسبة الإنجاز:</small>
                            <small>{stage.completion_percentage || 0}%</small>
                          </div>
                          <div className="progress progress-sm">
                            <div 
                              className="progress-bar" 
                              style={{ width: `${stage.completion_percentage || 0}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="stage-actions mt-3">
                      <button
                        className="btn btn-sm btn-outline-primary me-2"
                        onClick={() => {
                          setSelectedStage(stage);
                          setShowStageModal(true);
                        }}
                      >
                        <i className="fas fa-edit me-1"></i>
                        تحديث
                      </button>
                      
                      {stage.status === 'لم تبدأ' && (
                        <button
                          className="btn btn-sm btn-success"
                          onClick={() => updateStageStatus(stage.id, 'قيد التنفيذ', 0)}
                        >
                          <i className="fas fa-play me-1"></i>
                          بدء المرحلة
                        </button>
                      )}
                      
                      {stage.status === 'قيد التنفيذ' && (
                        <button
                          className="btn btn-sm btn-success"
                          onClick={() => updateStageStatus(stage.id, 'مكتملة', 100)}
                        >
                          <i className="fas fa-check me-1"></i>
                          إنهاء المرحلة
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="alert alert-info">
              <i className="fas fa-info-circle me-2"></i>
              لا توجد مراحل إنتاج محددة لهذا الطلب بعد.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = ProductionTracker;
