/* البطاقات المحسنة المدمجة */

.enhanced-stat-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0;
  transform: translateY(10px);
}

.enhanced-stat-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.enhanced-stat-card.clickable {
  cursor: pointer;
}

.enhanced-stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.enhanced-stat-card.loading {
  pointer-events: none;
}

/* أنواع البطاقات */
.stat-card-primary {
  border-left: 4px solid #1e3c72;
}

.stat-card-primary .stat-icon {
  color: #1e3c72;
}

.stat-card-primary .progress-fill {
  background: linear-gradient(90deg, #1e3c72, #2a5298);
}

.stat-card-secondary {
  border-left: 4px solid #ff6b35;
}

.stat-card-secondary .stat-icon {
  color: #ff6b35;
}

.stat-card-secondary .progress-fill {
  background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.stat-card-success {
  border-left: 4px solid #27ae60;
}

.stat-card-success .stat-icon {
  color: #27ae60;
}

.stat-card-success .progress-fill {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.stat-card-warning {
  border-left: 4px solid #f39c12;
}

.stat-card-warning .stat-icon {
  color: #f39c12;
}

.stat-card-warning .progress-fill {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.stat-card-danger {
  border-left: 4px solid #e74c3c;
}

.stat-card-danger .stat-icon {
  color: #e74c3c;
}

.stat-card-danger .progress-fill {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.stat-card-info {
  border-left: 4px solid #3498db;
}

.stat-card-info .stat-icon {
  color: #3498db;
}

.stat-card-info .progress-fill {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

/* رأس البطاقة المدمج */
.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.stat-icon-container {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30, 60, 114, 0.1);
  transition: all 0.2s ease;
}

.stat-icon {
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.enhanced-stat-card:hover .stat-icon-container {
  transform: scale(1.05);
}

.enhanced-stat-card:hover .stat-icon {
  transform: rotate(5deg);
}

/* مؤشر الاتجاه المدمج */
.trend-indicator {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  font-size: 0.65rem;
  font-weight: 600;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
}

.trend-up {
  color: #27ae60;
}

.trend-down {
  color: #e74c3c;
}

.trend-neutral {
  color: #6c757d;
}

.trend-indicator .trend-up {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.trend-indicator .trend-down {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.trend-indicator .trend-neutral {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* جسم البطاقة المدمج */
.stat-card-body {
  text-align: center;
  margin-bottom: 0.5rem;
}

.stat-title {
  font-size: 0.7rem;
  font-weight: 600;
  color: #6c757d;
  margin: 0 0 0.3rem 0;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.stat-value-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.15rem;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  transition: all 0.2s ease;
}

.enhanced-stat-card:hover .stat-value {
  transform: scale(1.02);
}

.stat-subtitle {
  font-size: 0.65rem;
  color: #6c757d;
  font-weight: 500;
}

/* ذيل البطاقة */
.stat-card-footer {
  margin-top: 1rem;
}

.progress-indicator {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(108, 117, 125, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0%;
  transition: width 1s ease;
  border-radius: 2px;
}

/* تأثيرات إضافية */
.glow-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(30, 60, 114, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.enhanced-stat-card:hover .glow-effect {
  opacity: 1;
}

.wave-effect {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(30, 60, 114, 0.3), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.enhanced-stat-card:hover .wave-effect {
  transform: translateX(100%);
}

/* شاشة التحميل */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #1e3c72;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .enhanced-stat-card {
    padding: 1.25rem;
  }

  .stat-icon-container {
    width: 40px;
    height: 40px;
  }

  .stat-icon {
    font-size: 1.25rem;
  }

  .stat-value {
    font-size: 1.75rem;
  }

  .stat-title {
    font-size: 0.8rem;
  }

  .trend-indicator {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .enhanced-stat-card {
    padding: 1rem;
  }

  .stat-card-header {
    margin-bottom: 0.75rem;
  }

  .stat-card-body {
    margin-bottom: 0.75rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .stat-icon-container {
    width: 35px;
    height: 35px;
  }

  .stat-icon {
    font-size: 1rem;
  }
}
