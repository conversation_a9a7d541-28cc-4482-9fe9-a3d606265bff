const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const WorkerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [worker, setWorker] = useState(null);
  const [workerOrders, setWorkerOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // إحصائيات العامل
  const [stats, setStats] = useState({
    totalOrders: 0,
    completedOrders: 0,
    inProgressOrders: 0,
    totalEarnings: 0,
    averageEarningsPerOrder: 0
  });
  
  // تحميل بيانات العامل
  useEffect(() => {
    const fetchWorkerData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل بيانات العامل
        const workerData = await window.api.workers.getById(id);
        if (!workerData) {
          throw new Error('لم يتم العثور على العامل');
        }
        setWorker(workerData);
        
        // تحميل طلبات العامل
        const orders = await window.api.orderWorkers.getByWorkerId(id);
        setWorkerOrders(orders);
        
        // حساب الإحصائيات
        const completedOrders = orders.filter(order => order.status === 'مكتمل');
        const inProgressOrders = orders.filter(order => order.status === 'قيد التنفيذ');
        const totalEarnings = orders.reduce((sum, order) => sum + (order.fee || 0), 0);
        
        setStats({
          totalOrders: orders.length,
          completedOrders: completedOrders.length,
          inProgressOrders: inProgressOrders.length,
          totalEarnings: totalEarnings,
          averageEarningsPerOrder: orders.length > 0 ? totalEarnings / orders.length : 0
        });
        
      } catch (error) {
        console.error('خطأ في تحميل بيانات العامل:', error);
        setError('حدث خطأ أثناء تحميل بيانات العامل. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchWorkerData();
  }, [id]);
  
  // تعديل العامل
  const handleEditWorker = () => {
    if (!hasPermission('workers_edit')) {
      alert('ليس لديك صلاحية لتعديل العامل');
      return;
    }
    
    navigate(`/workers/edit/${id}`);
  };
  
  // حذف العامل
  const handleDeleteWorker = async () => {
    if (!hasPermission('workers_delete')) {
      alert('ليس لديك صلاحية لحذف العامل');
      return;
    }
    
    if (workerOrders.length > 0) {
      alert('لا يمكن حذف العامل لأنه مرتبط بطلبات. يجب إزالة العامل من الطلبات أولاً.');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف هذا العامل؟')) {
      try {
        const result = await window.api.workers.delete(id);
        if (result.success) {
          alert('تم حذف العامل بنجاح');
          navigate('/workers');
        } else {
          throw new Error('فشل في حذف العامل');
        }
      } catch (error) {
        console.error('خطأ في حذف العامل:', error);
        alert('حدث خطأ أثناء حذف العامل. يرجى المحاولة مرة أخرى.');
      }
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل بيانات العامل...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  if (!worker) {
    return <div className="alert alert-warning">لم يتم العثور على العامل</div>;
  }
  
  return (
    <div className="worker-details-page">
      <div className="page-header">
        <h2>تفاصيل العامل</h2>
        <div className="page-actions">
          {hasPermission('workers_edit') && (
            <button className="btn btn-primary" onClick={handleEditWorker}>
              <i className="fas fa-edit"></i> تعديل
            </button>
          )}
          
          {hasPermission('workers_delete') && (
            <button className="btn btn-danger" onClick={handleDeleteWorker}>
              <i className="fas fa-trash"></i> حذف
            </button>
          )}
          
          <Link to="/workers" className="btn btn-secondary">
            <i className="fas fa-arrow-right"></i> العودة للقائمة
          </Link>
        </div>
      </div>
      
      <div className="row">
        {/* بيانات العامل */}
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">معلومات العامل</div>
            <div className="card-body">
              <div className="worker-info-item">
                <span className="info-label">الاسم:</span>
                <span className="info-value">{worker.name}</span>
              </div>
              
              <div className="worker-info-item">
                <span className="info-label">الدور:</span>
                <span className="info-value">{worker.role || '-'}</span>
              </div>
              
              <div className="worker-info-item">
                <span className="info-label">رقم الهاتف:</span>
                <span className="info-value">{worker.phone || '-'}</span>
              </div>
              
              <div className="worker-info-item">
                <span className="info-label">العنوان:</span>
                <span className="info-value">{worker.address || '-'}</span>
              </div>
              
              <div className="worker-info-item">
                <span className="info-label">أجر الطلب:</span>
                <span className="info-value">
                  {worker.fee_per_order ? `${worker.fee_per_order.toLocaleString()} ر.س` : '-'}
                </span>
              </div>
              
              <div className="worker-info-item">
                <span className="info-label">الراتب الشهري:</span>
                <span className="info-value">
                  {worker.monthly_salary ? `${worker.monthly_salary.toLocaleString()} ر.س` : '-'}
                </span>
              </div>
              
              <div className="worker-info-item">
                <span className="info-label">ملاحظات:</span>
                <span className="info-value">{worker.notes || '-'}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* إحصائيات العامل */}
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">إحصائيات العامل</div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-6">
                  <div className="stat-card">
                    <div className="stat-title">إجمالي الطلبات</div>
                    <div className="stat-value">{stats.totalOrders}</div>
                  </div>
                </div>
                
                <div className="col-md-6">
                  <div className="stat-card">
                    <div className="stat-title">الطلبات المكتملة</div>
                    <div className="stat-value">{stats.completedOrders}</div>
                  </div>
                </div>
                
                <div className="col-md-6">
                  <div className="stat-card">
                    <div className="stat-title">الطلبات قيد التنفيذ</div>
                    <div className="stat-value">{stats.inProgressOrders}</div>
                  </div>
                </div>
                
                <div className="col-md-6">
                  <div className="stat-card">
                    <div className="stat-title">إجمالي الأرباح</div>
                    <div className="stat-value">{stats.totalEarnings.toLocaleString()} ر.س</div>
                  </div>
                </div>
                
                <div className="col-md-12">
                  <div className="stat-card">
                    <div className="stat-title">متوسط الربح لكل طلب</div>
                    <div className="stat-value">{stats.averageEarningsPerOrder.toLocaleString()} ر.س</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* طلبات العامل */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>طلبات العامل</h5>
            <span className="badge bg-primary">{workerOrders.length} طلب</span>
          </div>
        </div>
        <div className="card-body">
          {workerOrders.length === 0 ? (
            <div className="alert alert-info">لا توجد طلبات لهذا العامل</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>تاريخ التعيين</th>
                    <th>المواصفات</th>
                    <th>الحالة</th>
                    <th>الأجر</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {workerOrders.map(order => (
                    <tr key={order.id}>
                      <td>{order.order_number}</td>
                      <td>{new Date(order.assigned_date).toLocaleDateString('ar-SA')}</td>
                      <td>
                        <span className="specifications-preview">
                          {order.specifications ? (
                            order.specifications.length > 50 
                              ? order.specifications.substring(0, 50) + '...' 
                              : order.specifications
                          ) : '-'}
                        </span>
                      </td>
                      <td>
                        <span className={`status-badge ${order.status}`}>
                          {order.status}
                        </span>
                      </td>
                      <td>{order.fee ? `${order.fee.toLocaleString()} ر.س` : '-'}</td>
                      <td>
                        <Link to={`/orders/${order.order_id}`} className="btn btn-sm btn-info">
                          <i className="fas fa-eye"></i> عرض الطلب
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = WorkerDetails;
