const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const InstallmentsList = require('../../components/invoices/InstallmentsList');
const InvoiceActions = require('../../components/invoices/InvoiceActions');

const InvoiceDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [invoice, setInvoice] = useState(null);
  const [order, setOrder] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // تحميل بيانات الفاتورة
  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل بيانات الفاتورة
        const invoiceData = await window.api.invoices.getById(id);
        
        if (!invoiceData) {
          throw new Error('الفاتورة غير موجودة');
        }
        
        setInvoice(invoiceData);
        
        // تحميل بيانات الطلب
        const orderData = await window.api.orders.getById(invoiceData.order_id);
        setOrder(orderData);
        
        // تحميل بيانات العميل
        if (orderData && orderData.customer_id) {
          const customerData = await window.api.customers.getById(orderData.customer_id);
          setCustomer(customerData);
        }
        
        // تحميل المدفوعات
        const paymentsData = await window.api.payments.getByInvoiceId(id);
        setPayments(paymentsData);
      } catch (error) {
        console.error('خطأ في تحميل بيانات الفاتورة:', error);
        setError('حدث خطأ أثناء تحميل بيانات الفاتورة. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      fetchInvoiceDetails();
    }
  }, [id]);
  
  // إضافة دفعة جديدة
  const handleAddPayment = () => {
    if (!hasPermission('invoices_edit')) {
      alert('ليس لديك صلاحية لإضافة دفعة');
      return;
    }
    
    navigate(`/payments/create/${id}`);
  };
  
  // تعديل الفاتورة
  const handleEditInvoice = () => {
    if (!hasPermission('invoices_edit')) {
      alert('ليس لديك صلاحية لتعديل الفاتورة');
      return;
    }
    
    navigate(`/invoices/edit/${id}`);
  };
  
  // حذف الفاتورة
  const handleDeleteInvoice = async () => {
    if (!hasPermission('invoices_delete')) {
      alert('ليس لديك صلاحية لحذف الفاتورة');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
      try {
        await window.api.invoices.delete(id);
        
        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'delete_invoice',
          action_details: `حذف الفاتورة رقم ${invoice.invoice_number}`
        });
        
        alert('تم حذف الفاتورة بنجاح');
        navigate('/invoices');
      } catch (error) {
        console.error('خطأ في حذف الفاتورة:', error);
        alert('حدث خطأ أثناء حذف الفاتورة. يرجى المحاولة مرة أخرى.');
      }
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  if (!invoice) {
    return <div className="alert alert-warning">الفاتورة غير موجودة</div>;
  }
  
  return (
    <div className="invoice-details-page">
      <div className="page-header">
        <h2>تفاصيل الفاتورة {invoice.invoice_number}</h2>
        <div className="page-actions">
          {hasPermission('invoices_edit') && (
            <button className="btn btn-primary" onClick={handleEditInvoice}>
              <i className="fas fa-edit"></i> تعديل
            </button>
          )}
          
          {hasPermission('invoices_delete') && (
            <button className="btn btn-danger" onClick={handleDeleteInvoice}>
              <i className="fas fa-trash"></i> حذف
            </button>
          )}
          
          <button className="btn btn-secondary" onClick={() => navigate('/invoices')}>
            <i className="fas fa-arrow-right"></i> العودة
          </button>
        </div>
      </div>
      
      {/* أزرار الطباعة والمشاركة */}
      <InvoiceActions invoice={invoice} />
      
      <div className="row">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">معلومات الفاتورة</div>
            <div className="card-body">
              <div className="info-item">
                <span className="info-label">رقم الفاتورة:</span>
                <span className="info-value">{invoice.invoice_number}</span>
              </div>
              
              <div className="info-item">
                <span className="info-label">تاريخ الإصدار:</span>
                <span className="info-value">{new Date(invoice.issue_date).toLocaleDateString('ar-SA')}</span>
              </div>
              
              {invoice.due_date && (
                <div className="info-item">
                  <span className="info-label">تاريخ الاستحقاق:</span>
                  <span className="info-value">{new Date(invoice.due_date).toLocaleDateString('ar-SA')}</span>
                </div>
              )}
              
              <div className="info-item">
                <span className="info-label">المبلغ الإجمالي:</span>
                <span className="info-value">{invoice.total_amount.toLocaleString()} ر.س</span>
              </div>
              
              <div className="info-item">
                <span className="info-label">المبلغ المدفوع:</span>
                <span className="info-value">{invoice.paid_amount.toLocaleString()} ر.س</span>
              </div>
              
              <div className="info-item">
                <span className="info-label">المبلغ المتبقي:</span>
                <span className="info-value">{(invoice.total_amount - invoice.paid_amount).toLocaleString()} ر.س</span>
              </div>
              
              <div className="info-item">
                <span className="info-label">نوع الدفع:</span>
                <span className="info-value">{invoice.payment_type}</span>
              </div>
              
              <div className="info-item">
                <span className="info-label">حالة الفاتورة:</span>
                <span className={`info-value status-${invoice.status === 'مدفوعة' ? 'paid' : invoice.status === 'مدفوعة جزئياً' ? 'partial' : 'unpaid'}`}>
                  {invoice.status}
                </span>
              </div>
              
              {invoice.notes && (
                <div className="info-item">
                  <span className="info-label">ملاحظات:</span>
                  <span className="info-value">{invoice.notes}</span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">معلومات العميل والطلب</div>
            <div className="card-body">
              {customer && (
                <>
                  <div className="info-item">
                    <span className="info-label">اسم العميل:</span>
                    <span className="info-value">{customer.name}</span>
                  </div>
                  
                  {customer.phone && (
                    <div className="info-item">
                      <span className="info-label">رقم الهاتف:</span>
                      <span className="info-value">{customer.phone}</span>
                    </div>
                  )}
                  
                  {customer.email && (
                    <div className="info-item">
                      <span className="info-label">البريد الإلكتروني:</span>
                      <span className="info-value">{customer.email}</span>
                    </div>
                  )}
                  
                  {customer.address && (
                    <div className="info-item">
                      <span className="info-label">العنوان:</span>
                      <span className="info-value">{customer.address}</span>
                    </div>
                  )}
                </>
              )}
              
              {order && (
                <>
                  <div className="info-item">
                    <span className="info-label">رقم الطلب:</span>
                    <span className="info-value">
                      <a href={`/orders/${order.id}`}>{order.order_number}</a>
                    </span>
                  </div>
                  
                  <div className="info-item">
                    <span className="info-label">تاريخ الطلب:</span>
                    <span className="info-value">{new Date(order.order_date).toLocaleDateString('ar-SA')}</span>
                  </div>
                  
                  <div className="info-item">
                    <span className="info-label">حالة الطلب:</span>
                    <span className="info-value">{order.status}</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* قائمة المدفوعات */}
      <div className="card mt-4">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>المدفوعات</h5>
            {hasPermission('invoices_edit') && invoice.status !== 'مدفوعة' && (
              <button className="btn btn-success" onClick={handleAddPayment}>
                <i className="fas fa-plus"></i> إضافة دفعة
              </button>
            )}
          </div>
        </div>
        <div className="card-body">
          {payments.length === 0 ? (
            <div className="alert alert-info">لا توجد مدفوعات لهذه الفاتورة</div>
          ) : (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>رقم الدفعة</th>
                    <th>التاريخ</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                    <th>ملاحظات</th>
                  </tr>
                </thead>
                <tbody>
                  {payments.map(payment => (
                    <tr key={payment.id}>
                      <td>{payment.id}</td>
                      <td>{new Date(payment.payment_date).toLocaleDateString('ar-SA')}</td>
                      <td>{payment.amount.toLocaleString()} ر.س</td>
                      <td>{payment.payment_method}</td>
                      <td>{payment.notes}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
      
      {/* قائمة الأقساط - إذا كانت الفاتورة بالأقساط */}
      {invoice.payment_type === 'أقساط' && (
        <div className="mt-4">
          <InstallmentsList invoiceId={id} />
        </div>
      )}
    </div>
  );
};

module.exports = InvoiceDetails;
