const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const OrderDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [order, setOrder] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [product, setProduct] = useState(null);
  const [materials, setMaterials] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تحميل بيانات الطلب
  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل بيانات الطلب
        const orderData = await window.api.orders.getById(id);

        if (!orderData) {
          throw new Error('الطلب غير موجود');
        }

        setOrder(orderData);

        // تحميل بيانات العميل
        if (orderData.customer_id) {
          const customerData = await window.api.customers.getById(orderData.customer_id);
          setCustomer(customerData);
        }

        // تحميل بيانات المنتج
        if (orderData.product_id) {
          const productData = await window.api.products.getById(orderData.product_id);
          setProduct(productData);
        }

        // تحميل المواد المستخدمة في الطلب
        const materialsData = await window.api.orders.getMaterials(id);
        setMaterials(materialsData);

        // تحميل الفواتير المرتبطة بالطلب
        const invoicesData = await window.api.invoices.getByOrderId(id);
        setInvoices(invoicesData);
      } catch (error) {
        console.error('خطأ في تحميل بيانات الطلب:', error);
        setError('حدث خطأ أثناء تحميل بيانات الطلب. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchOrderDetails();
    }
  }, [id]);

  // تعديل الطلب
  const handleEditOrder = () => {
    if (!hasPermission('orders_edit')) {
      alert('ليس لديك صلاحية لتعديل الطلب');
      return;
    }

    navigate(`/orders/edit/${id}`);
  };

  // حذف الطلب
  const handleDeleteOrder = async () => {
    if (!hasPermission('orders_delete')) {
      alert('ليس لديك صلاحية لحذف الطلب');
      return;
    }

    // التحقق من وجود فواتير مرتبطة بالطلب
    if (invoices.length > 0) {
      alert('لا يمكن حذف الطلب لأنه مرتبط بفواتير');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
      try {
        await window.api.orders.delete(id);

        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'delete_order',
          action_details: `حذف الطلب رقم ${order.order_number}`
        });

        alert('تم حذف الطلب بنجاح');
        navigate('/orders');
      } catch (error) {
        console.error('خطأ في حذف الطلب:', error);
        alert('حدث خطأ أثناء حذف الطلب. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  // تغيير حالة الطلب
  const handleChangeStatus = async (newStatus) => {
    if (!hasPermission('orders_edit')) {
      alert('ليس لديك صلاحية لتغيير حالة الطلب');
      return;
    }

    try {
      await window.api.orders.updateStatus(id, newStatus);

      // تحديث حالة الطلب في الواجهة
      setOrder(prev => ({
        ...prev,
        status: newStatus
      }));

      // إضافة سجل في سجل العمليات
      await window.api.activityLog.create({
        action_type: 'update_order_status',
        action_details: `تغيير حالة الطلب رقم ${order.order_number} إلى ${newStatus}`
      });

      alert('تم تغيير حالة الطلب بنجاح');
    } catch (error) {
      console.error('خطأ في تغيير حالة الطلب:', error);
      alert('حدث خطأ أثناء تغيير حالة الطلب. يرجى المحاولة مرة أخرى.');
    }
  };

  // إنشاء فاتورة للطلب
  const handleCreateInvoice = () => {
    if (!hasPermission('invoices_create')) {
      alert('ليس لديك صلاحية لإنشاء فاتورة');
      return;
    }

    navigate(`/invoices/create/${id}`);
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل البيانات...');
  }

  if (error) {
    return React.createElement('div', { className: 'alert alert-danger' }, error);
  }

  if (!order) {
    return React.createElement('div', { className: 'alert alert-warning' }, 'الطلب غير موجود');
  }

  return (
    <div className="order-details-page">
      <div className="page-header">
        <h2>تفاصيل الطلب {order.order_number}</h2>
        <div className="page-actions">
          {hasPermission('orders_edit') && (
            <button className="btn btn-primary" onClick={handleEditOrder}>
              <i className="fas fa-edit"></i> تعديل
            </button>
          )}

          {hasPermission('orders_delete') && invoices.length === 0 && (
            <button className="btn btn-danger" onClick={handleDeleteOrder}>
              <i className="fas fa-trash"></i> حذف
            </button>
          )}

          {hasPermission('invoices_create') && order.status === 'مكتمل' && (
            <button className="btn btn-success" onClick={handleCreateInvoice}>
              <i className="fas fa-file-invoice-dollar"></i> إنشاء فاتورة
            </button>
          )}

          <button className="btn btn-secondary" onClick={() => navigate('/orders')}>
            <i className="fas fa-arrow-right"></i> العودة
          </button>
        </div>
      </div>

      {/* حالة الطلب */}
      <div className="order-status-bar">
        <div className="status-label">حالة الطلب:</div>
        <div className="status-buttons">
          <button
            className={`status-btn ${order.status === 'جديد' ? 'active' : ''}`}
            onClick={() => handleChangeStatus('جديد')}
            disabled={!hasPermission('orders_edit')}
          >
            جديد
          </button>
          <button
            className={`status-btn ${order.status === 'قيد التنفيذ' ? 'active' : ''}`}
            onClick={() => handleChangeStatus('قيد التنفيذ')}
            disabled={!hasPermission('orders_edit')}
          >
            قيد التنفيذ
          </button>
          <button
            className={`status-btn ${order.status === 'مكتمل' ? 'active' : ''}`}
            onClick={() => handleChangeStatus('مكتمل')}
            disabled={!hasPermission('orders_edit')}
          >
            مكتمل
          </button>
          <button
            className={`status-btn ${order.status === 'ملغي' ? 'active' : ''}`}
            onClick={() => handleChangeStatus('ملغي')}
            disabled={!hasPermission('orders_edit')}
          >
            ملغي
          </button>
        </div>
      </div>

      <div className="row">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">معلومات الطلب</div>
            <div className="card-body">
              <div className="info-item">
                <span className="info-label">رقم الطلب:</span>
                <span className="info-value">{order.order_number}</span>
              </div>

              <div className="info-item">
                <span className="info-label">تاريخ الطلب:</span>
                <span className="info-value">{new Date(order.order_date).toLocaleDateString('ar-SA')}</span>
              </div>

              {order.delivery_date && (
                <div className="info-item">
                  <span className="info-label">تاريخ التسليم المتوقع:</span>
                  <span className="info-value">{new Date(order.delivery_date).toLocaleDateString('ar-SA')}</span>
                </div>
              )}

              {product && (
                <div className="info-item">
                  <span className="info-label">المنتج:</span>
                  <span className="info-value">{product.name}</span>
                </div>
              )}

              {order.specifications && (
                <div className="info-item">
                  <span className="info-label">المواصفات:</span>
                  <span className="info-value">{order.specifications}</span>
                </div>
              )}

              {order.notes && (
                <div className="info-item">
                  <span className="info-label">ملاحظات:</span>
                  <span className="info-value">{order.notes}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card">
            <div className="card-header">معلومات العميل</div>
            <div className="card-body">
              {customer ? (
                <>
                  <div className="info-item">
                    <span className="info-label">اسم العميل:</span>
                    <span className="info-value">{customer.name}</span>
                  </div>

                  {customer.phone && (
                    <div className="info-item">
                      <span className="info-label">رقم الهاتف:</span>
                      <span className="info-value">{customer.phone}</span>
                    </div>
                  )}

                  {customer.email && (
                    <div className="info-item">
                      <span className="info-label">البريد الإلكتروني:</span>
                      <span className="info-value">{customer.email}</span>
                    </div>
                  )}

                  {customer.address && (
                    <div className="info-item">
                      <span className="info-label">العنوان:</span>
                      <span className="info-value">{customer.address}</span>
                    </div>
                  )}
                </>
              ) : (
                <div className="alert alert-warning">لا توجد معلومات للعميل</div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* المواد المستخدمة */}
      <div className="card mt-4">
        <div className="card-header">المواد المستخدمة</div>
        <div className="card-body">
          {materials.length === 0 ? (
            <div className="alert alert-info">لا توجد مواد مستخدمة في هذا الطلب</div>
          ) : (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>المادة</th>
                    <th>الوحدة</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                  </tr>
                </thead>
                <tbody>
                  {materials.map(material => (
                    <tr key={material.id}>
                      <td>{material.material_name}</td>
                      <td>{material.unit}</td>
                      <td>{material.quantity}</td>
                      <td>{material.cost_per_unit.toLocaleString()} ر.س</td>
                      <td>{material.total_cost.toLocaleString()} ر.س</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="4" className="text-left">إجمالي تكلفة المواد:</td>
                    <td>{materials.reduce((sum, material) => sum + material.total_cost, 0).toLocaleString()} ر.س</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* التكاليف والأسعار */}
      <div className="card mt-4">
        <div className="card-header">التكاليف والأسعار</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-3">
              <div className="cost-item">
                <div className="cost-label">تكلفة المواد</div>
                <div className="cost-value">{materials.reduce((sum, material) => sum + material.total_cost, 0).toLocaleString()} ر.س</div>
              </div>
            </div>

            <div className="col-md-3">
              <div className="cost-item">
                <div className="cost-label">أجور العمال</div>
                <div className="cost-value">{order.worker_fee.toLocaleString()} ر.س</div>
              </div>
            </div>

            <div className="col-md-3">
              <div className="cost-item">
                <div className="cost-label">تكاليف المصنع</div>
                <div className="cost-value">{order.factory_fee.toLocaleString()} ر.س</div>
              </div>
            </div>

            <div className="col-md-3">
              <div className="cost-item">
                <div className="cost-label">أجور التصميم</div>
                <div className="cost-value">{order.designer_fee.toLocaleString()} ر.س</div>
              </div>
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-4">
              <div className="cost-item total">
                <div className="cost-label">التكلفة الإجمالية</div>
                <div className="cost-value">{order.total_cost.toLocaleString()} ر.س</div>
              </div>
            </div>

            <div className="col-md-4">
              <div className="cost-item">
                <div className="cost-label">هامش الربح</div>
                <div className="cost-value">{order.owner_margin.toLocaleString()} ر.س</div>
              </div>
            </div>

            <div className="col-md-4">
              <div className="cost-item final">
                <div className="cost-label">السعر النهائي</div>
                <div className="cost-value">{order.final_price.toLocaleString()} ر.س</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* الفواتير المرتبطة */}
      <div className="card mt-4">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>الفواتير المرتبطة</h5>
            {hasPermission('invoices_create') && order.status === 'مكتمل' && (
              <button className="btn btn-success" onClick={handleCreateInvoice}>
                <i className="fas fa-plus"></i> إنشاء فاتورة
              </button>
            )}
          </div>
        </div>
        <div className="card-body">
          {invoices.length === 0 ? (
            <div className="alert alert-info">لا توجد فواتير مرتبطة بهذا الطلب</div>
          ) : (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>رقم الفاتورة</th>
                    <th>تاريخ الإصدار</th>
                    <th>المبلغ الإجمالي</th>
                    <th>المبلغ المدفوع</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {invoices.map(invoice => (
                    <tr key={invoice.id}>
                      <td>{invoice.invoice_number}</td>
                      <td>{new Date(invoice.issue_date).toLocaleDateString('ar-SA')}</td>
                      <td>{invoice.total_amount.toLocaleString()} ر.س</td>
                      <td>{invoice.paid_amount.toLocaleString()} ر.س</td>
                      <td>
                        <span className={`status-badge ${invoice.status === 'مدفوعة' ? 'paid' : invoice.status === 'مدفوعة جزئياً' ? 'partial' : 'unpaid'}`}>
                          {invoice.status}
                        </span>
                      </td>
                      <td>
                        <button
                          className="btn btn-sm btn-info"
                          onClick={() => navigate(`/invoices/${invoice.id}`)}
                        >
                          <i className="fas fa-eye"></i> عرض
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = OrderDetails;
