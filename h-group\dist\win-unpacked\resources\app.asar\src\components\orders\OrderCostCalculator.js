const React = require('react');
const { useState, useEffect } = React;
const CostPrediction = require('./CostPrediction');

const OrderCostCalculator = ({ order, onChange }) => {
  const [materials, setMaterials] = useState([]);
  const [workerFee, setWorkerFee] = useState(0);
  const [factoryFee, setFactoryFee] = useState(0);
  const [designerFee, setDesignerFee] = useState(0);
  const [ownerMargin, setOwnerMargin] = useState(0);
  const [totalCost, setTotalCost] = useState(0);
  const [finalPrice, setFinalPrice] = useState(0);
  const [showPrediction, setShowPrediction] = useState(false);

  // تحميل البيانات الأولية
  useEffect(() => {
    if (order) {
      // تحميل المواد
      if (order.materials && order.materials.length > 0) {
        setMaterials(order.materials);
      }

      // تحميل التكاليف
      setWorkerFee(order.worker_fee || 0);
      setFactoryFee(order.factory_fee || 0);
      setDesignerFee(order.designer_fee || 0);
      setOwnerMargin(order.owner_margin || 0);
    }
  }, [order]);

  // حساب التكلفة الإجمالية والسعر النهائي
  useEffect(() => {
    // حساب تكلفة المواد
    const materialsCost = materials.reduce((sum, material) => sum + (material.total_cost || 0), 0);

    // حساب التكلفة الإجمالية
    const newTotalCost = materialsCost + workerFee + factoryFee + designerFee;
    setTotalCost(newTotalCost);

    // حساب السعر النهائي
    const newFinalPrice = newTotalCost + ownerMargin;
    setFinalPrice(newFinalPrice);

    // إرسال البيانات المحدثة
    if (onChange) {
      onChange({
        materials,
        worker_fee: workerFee,
        factory_fee: factoryFee,
        designer_fee: designerFee,
        owner_margin: ownerMargin,
        total_cost: newTotalCost,
        final_price: newFinalPrice
      });
    }
  }, [materials, workerFee, factoryFee, designerFee, ownerMargin, onChange]);

  // إضافة مادة جديدة
  const handleAddMaterial = () => {
    setMaterials([
      ...materials,
      {
        material_id: '',
        material_name: '',
        unit: '',
        quantity: 0,
        cost_per_unit: 0,
        total_cost: 0
      }
    ]);
  };

  // حذف مادة
  const handleRemoveMaterial = (index) => {
    const newMaterials = [...materials];
    newMaterials.splice(index, 1);
    setMaterials(newMaterials);
  };

  // تحديث بيانات المادة
  const handleMaterialChange = (index, field, value) => {
    const newMaterials = [...materials];
    newMaterials[index][field] = value;

    // إذا تم تغيير الكمية أو السعر، حساب التكلفة الإجمالية
    if (field === 'quantity' || field === 'cost_per_unit') {
      newMaterials[index].total_cost = newMaterials[index].quantity * newMaterials[index].cost_per_unit;
    }

    setMaterials(newMaterials);
  };

  // تطبيق التنبؤ
  const handleApplyPrediction = (prediction) => {
    // تحديث المواد
    if (prediction.materials && prediction.materials.length > 0) {
      setMaterials(prediction.materials);
    }

    // تحديث التكاليف
    setWorkerFee(prediction.worker_fee || 0);
    setFactoryFee(prediction.factory_fee || 0);
    setDesignerFee(prediction.designer_fee || 0);
    setOwnerMargin(prediction.owner_margin || 0);

    // إخفاء التنبؤ
    setShowPrediction(false);
  };

  return (
    <div className="order-cost-calculator">
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>حساب التكاليف والأسعار</h5>
            <button
              className="btn btn-sm btn-outline-primary"
              onClick={() => setShowPrediction(!showPrediction)}
            >
              <i className="fas fa-magic"></i> {showPrediction ? 'إخفاء التنبؤ' : 'التنبؤ بالتكاليف'}
            </button>
          </div>
        </div>

        <div className="card-body">
          {showPrediction && (
            <CostPrediction
              productId={order?.product_id}
              specifications={order?.specifications}
              onApplyPrediction={handleApplyPrediction}
            />
          )}

          <h6>المواد المستخدمة</h6>
          <div className="table-responsive">
            <table className="table">
              <thead>
                <tr>
                  <th>المادة</th>
                  <th>الوحدة</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>الإجمالي</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {materials.map((material, index) => (
                  <tr key={index}>
                    <td>
                      <input
                        type="text"
                        className="form-control form-control-sm"
                        value={material.material_name}
                        onChange={(e) => handleMaterialChange(index, 'material_name', e.target.value)}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        className="form-control form-control-sm"
                        value={material.unit}
                        onChange={(e) => handleMaterialChange(index, 'unit', e.target.value)}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        className="form-control form-control-sm"
                        value={material.quantity}
                        onChange={(e) => handleMaterialChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.01"
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        className="form-control form-control-sm"
                        value={material.cost_per_unit}
                        onChange={(e) => handleMaterialChange(index, 'cost_per_unit', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.01"
                      />
                    </td>
                    <td>
                      {material.total_cost.toLocaleString()} ر.س
                    </td>
                    <td>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => handleRemoveMaterial(index)}
                      >
                        <i className="fas fa-trash"></i>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr>
                  <td colSpan="6">
                    <button
                      className="btn btn-sm btn-success"
                      onClick={handleAddMaterial}
                    >
                      <i className="fas fa-plus"></i> إضافة مادة
                    </button>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          <div className="row mt-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">أجور العمال</label>
                <input
                  type="number"
                  className="form-control"
                  value={workerFee}
                  onChange={(e) => setWorkerFee(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">تكاليف المصنع</label>
                <input
                  type="number"
                  className="form-control"
                  value={factoryFee}
                  onChange={(e) => setFactoryFee(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
          </div>

          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">أجور التصميم</label>
                <input
                  type="number"
                  className="form-control"
                  value={designerFee}
                  onChange={(e) => setDesignerFee(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">هامش الربح</label>
                <input
                  type="number"
                  className="form-control"
                  value={ownerMargin}
                  onChange={(e) => setOwnerMargin(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-6">
              <div className="alert alert-info">
                <strong>التكلفة الإجمالية:</strong> {totalCost.toLocaleString()} ر.س
              </div>
            </div>

            <div className="col-md-6">
              <div className="alert alert-success">
                <strong>السعر النهائي:</strong> {finalPrice.toLocaleString()} ر.س
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

module.exports = OrderCostCalculator;
