const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');

const MaterialReservations = () => {
  const navigate = useNavigate();
  const [reservations, setReservations] = useState([]);
  const [materials, setMaterials] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState(null);
  const [filter, setFilter] = useState('all'); // all, active, used, cancelled

  const [newReservation, setNewReservation] = useState({
    material_id: '',
    order_id: '',
    reserved_quantity: '',
    expires_at: '',
    notes: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // تحميل جميع الحجوزات
      const allReservations = [];
      const materialsData = await window.electronAPI.materials.getAll();
      
      for (const material of materialsData) {
        try {
          const materialReservations = await window.electronAPI.materialReservations.getByMaterialId(material.id);
          allReservations.push(...materialReservations);
        } catch (error) {
          console.warn(`خطأ في تحميل حجوزات المادة ${material.name}:`, error);
        }
      }

      const ordersData = await window.electronAPI.orders.getAll();
      
      setReservations(allReservations);
      setMaterials(materialsData);
      setOrders(ordersData);
    } catch (error) {
      console.error('خطأ في تحميل بيانات الحجوزات:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReservation = async (e) => {
    e.preventDefault();
    
    try {
      // تحديد تاريخ انتهاء الحجز (7 أيام من الآن إذا لم يتم تحديده)
      const expiresAt = newReservation.expires_at || 
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();

      await window.electronAPI.materialReservations.create({
        ...newReservation,
        reserved_quantity: parseFloat(newReservation.reserved_quantity),
        expires_at: expiresAt
      });

      setShowCreateModal(false);
      setNewReservation({
        material_id: '',
        order_id: '',
        reserved_quantity: '',
        expires_at: '',
        notes: ''
      });
      
      await loadData();
      alert('تم إنشاء الحجز بنجاح');
    } catch (error) {
      console.error('خطأ في إنشاء الحجز:', error);
      alert('خطأ في إنشاء الحجز: ' + error.message);
    }
  };

  const handleUseReservation = async (reservationId, usedQuantity) => {
    try {
      await window.electronAPI.materialReservations.use(reservationId, usedQuantity);
      await loadData();
      alert('تم استخدام الحجز بنجاح');
    } catch (error) {
      console.error('خطأ في استخدام الحجز:', error);
      alert('خطأ في استخدام الحجز: ' + error.message);
    }
  };

  const handleCancelReservation = async (reservationId) => {
    if (!confirm('هل أنت متأكد من إلغاء هذا الحجز؟')) return;
    
    try {
      await window.electronAPI.materialReservations.cancel(reservationId);
      await loadData();
      alert('تم إلغاء الحجز بنجاح');
    } catch (error) {
      console.error('خطأ في إلغاء الحجز:', error);
      alert('خطأ في إلغاء الحجز: ' + error.message);
    }
  };

  const filteredReservations = reservations.filter(reservation => {
    if (filter === 'all') return true;
    if (filter === 'active') return reservation.status === 'نشط';
    if (filter === 'used') return reservation.status === 'مستخدم';
    if (filter === 'cancelled') return reservation.status === 'ملغي';
    return true;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'نشط': return 'success';
      case 'مستخدم': return 'info';
      case 'ملغي': return 'danger';
      case 'منتهي الصلاحية': return 'warning';
      default: return 'secondary';
    }
  };

  const isExpired = (expiresAt) => {
    return new Date(expiresAt) < new Date();
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل حجوزات المواد...')
    );
  }

  return React.createElement('div', { className: 'material-reservations-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-bookmark' }),
          ' إدارة حجوزات المواد'
        ),
        React.createElement('div', { className: 'header-actions' },
          React.createElement('button', {
            className: 'btn btn-primary',
            onClick: () => setShowCreateModal(true)
          },
            React.createElement('i', { className: 'fas fa-plus' }),
            ' حجز جديد'
          )
        )
      )
    ),

    // فلاتر الحجوزات
    React.createElement('div', { className: 'reservations-filters' },
      React.createElement('div', { className: 'filter-buttons' },
        ['all', 'active', 'used', 'cancelled'].map(filterType =>
          React.createElement('button', {
            key: filterType,
            className: `filter-btn ${filter === filterType ? 'active' : ''}`,
            onClick: () => setFilter(filterType)
          },
            filterType === 'all' ? 'الكل' :
            filterType === 'active' ? 'نشط' :
            filterType === 'used' ? 'مستخدم' : 'ملغي'
          )
        )
      ),
      React.createElement('div', { className: 'reservations-summary' },
        React.createElement('div', { className: 'summary-item' },
          React.createElement('span', { className: 'label' }, 'إجمالي الحجوزات:'),
          React.createElement('span', { className: 'value' }, reservations.length)
        ),
        React.createElement('div', { className: 'summary-item' },
          React.createElement('span', { className: 'label' }, 'الحجوزات النشطة:'),
          React.createElement('span', { className: 'value active' }, 
            reservations.filter(r => r.status === 'نشط').length
          )
        ),
        React.createElement('div', { className: 'summary-item' },
          React.createElement('span', { className: 'label' }, 'منتهية الصلاحية:'),
          React.createElement('span', { className: 'value expired' }, 
            reservations.filter(r => r.status === 'نشط' && isExpired(r.expires_at)).length
          )
        )
      )
    ),

    // جدول الحجوزات
    React.createElement('div', { className: 'reservations-table-container' },
      React.createElement('table', { className: 'reservations-table' },
        React.createElement('thead', null,
          React.createElement('tr', null,
            React.createElement('th', null, 'المادة'),
            React.createElement('th', null, 'الطلب'),
            React.createElement('th', null, 'الكمية المحجوزة'),
            React.createElement('th', null, 'تاريخ الحجز'),
            React.createElement('th', null, 'تاريخ الانتهاء'),
            React.createElement('th', null, 'الحالة'),
            React.createElement('th', null, 'الإجراءات')
          )
        ),
        React.createElement('tbody', null,
          filteredReservations.length > 0
            ? filteredReservations.map(reservation =>
                React.createElement('tr', { 
                  key: reservation.id,
                  className: isExpired(reservation.expires_at) && reservation.status === 'نشط' ? 'expired-row' : ''
                },
                  React.createElement('td', null,
                    React.createElement('div', { className: 'material-info' },
                      React.createElement('strong', null, reservation.material_name),
                      reservation.notes && React.createElement('div', { className: 'reservation-notes' }, reservation.notes)
                    )
                  ),
                  React.createElement('td', null,
                    React.createElement('div', { className: 'order-info' },
                      React.createElement('span', { className: 'order-number' }, reservation.order_number),
                      React.createElement('div', { className: 'customer-name' }, reservation.customer_name)
                    )
                  ),
                  React.createElement('td', null,
                    React.createElement('span', { className: 'quantity' }, 
                      `${reservation.reserved_quantity} ${reservation.unit || ''}`
                    )
                  ),
                  React.createElement('td', null,
                    new Date(reservation.reserved_at).toLocaleDateString('ar-SA')
                  ),
                  React.createElement('td', null,
                    React.createElement('span', { 
                      className: `expiry-date ${isExpired(reservation.expires_at) ? 'expired' : ''}` 
                    },
                      new Date(reservation.expires_at).toLocaleDateString('ar-SA')
                    )
                  ),
                  React.createElement('td', null,
                    React.createElement('span', { 
                      className: `status-badge ${getStatusColor(reservation.status)}` 
                    }, reservation.status)
                  ),
                  React.createElement('td', null,
                    React.createElement('div', { className: 'action-buttons' },
                      reservation.status === 'نشط' && [
                        React.createElement('button', {
                          key: 'use',
                          className: 'btn btn-sm btn-success',
                          onClick: () => {
                            const quantity = prompt('كم الكمية المستخدمة؟', reservation.reserved_quantity);
                            if (quantity && !isNaN(quantity)) {
                              handleUseReservation(reservation.id, parseFloat(quantity));
                            }
                          },
                          title: 'استخدام الحجز'
                        },
                          React.createElement('i', { className: 'fas fa-check' })
                        ),
                        React.createElement('button', {
                          key: 'cancel',
                          className: 'btn btn-sm btn-danger',
                          onClick: () => handleCancelReservation(reservation.id),
                          title: 'إلغاء الحجز'
                        },
                          React.createElement('i', { className: 'fas fa-times' })
                        )
                      ]
                    )
                  )
                )
              )
            : React.createElement('tr', null,
                React.createElement('td', { colSpan: 7, className: 'no-data' },
                  'لا توجد حجوزات'
                )
              )
        )
      )
    ),

    // نافذة إنشاء حجز جديد
    showCreateModal && React.createElement('div', { className: 'modal-overlay' },
      React.createElement('div', { className: 'modal-content' },
        React.createElement('div', { className: 'modal-header' },
          React.createElement('h3', null, 'حجز مادة جديد'),
          React.createElement('button', {
            className: 'close-btn',
            onClick: () => setShowCreateModal(false)
          },
            React.createElement('i', { className: 'fas fa-times' })
          )
        ),
        React.createElement('form', { onSubmit: handleCreateReservation },
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'المادة'),
            React.createElement('select', {
              value: newReservation.material_id,
              onChange: (e) => setNewReservation({...newReservation, material_id: e.target.value}),
              required: true
            },
              React.createElement('option', { value: '' }, 'اختر المادة'),
              materials.map(material =>
                React.createElement('option', { key: material.id, value: material.id }, material.name)
              )
            )
          ),
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'الطلب'),
            React.createElement('select', {
              value: newReservation.order_id,
              onChange: (e) => setNewReservation({...newReservation, order_id: e.target.value}),
              required: true
            },
              React.createElement('option', { value: '' }, 'اختر الطلب'),
              orders.filter(order => order.status !== 'مكتمل' && order.status !== 'ملغي').map(order =>
                React.createElement('option', { key: order.id, value: order.id }, 
                  `${order.order_number} - ${order.customer_name}`
                )
              )
            )
          ),
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'الكمية المحجوزة'),
            React.createElement('input', {
              type: 'number',
              step: '0.01',
              value: newReservation.reserved_quantity,
              onChange: (e) => setNewReservation({...newReservation, reserved_quantity: e.target.value}),
              required: true
            })
          ),
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'تاريخ انتهاء الحجز (اختياري)'),
            React.createElement('input', {
              type: 'datetime-local',
              value: newReservation.expires_at,
              onChange: (e) => setNewReservation({...newReservation, expires_at: e.target.value})
            })
          ),
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, 'ملاحظات'),
            React.createElement('textarea', {
              value: newReservation.notes,
              onChange: (e) => setNewReservation({...newReservation, notes: e.target.value}),
              rows: 3
            })
          ),
          React.createElement('div', { className: 'modal-actions' },
            React.createElement('button', { type: 'submit', className: 'btn btn-primary' }, 'إنشاء الحجز'),
            React.createElement('button', { 
              type: 'button', 
              className: 'btn btn-secondary',
              onClick: () => setShowCreateModal(false)
            }, 'إلغاء')
          )
        )
      )
    )
  );
};

module.exports = MaterialReservations;
