{"version": 3, "file": "formula-xform.js", "names": ["BaseXform", "require", "FormulaXform", "tag", "render", "xmlStream", "model", "leafNode", "parseOpen", "parseText", "text", "parseClose", "name", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/formula-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\n\nclass FormulaXform extends BaseXform {\n  get tag() {\n    return 'formula';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, null, model);\n  }\n\n  parseOpen() {\n    this.model = '';\n  }\n\n  parseText(text) {\n    this.model += text;\n  }\n\n  parseClose(name) {\n    return name !== this.tag;\n  }\n}\n\nmodule.exports = FormulaXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE7C,MAAMC,YAAY,SAASF,SAAS,CAAC;EACnC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE,IAAI,EAAEG,KAAK,CAAC;EAC3C;EAEAE,SAASA,CAAA,EAAG;IACV,IAAI,CAACF,KAAK,GAAG,EAAE;EACjB;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,CAACJ,KAAK,IAAII,IAAI;EACpB;EAEAC,UAAUA,CAACC,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,IAAI,CAACT,GAAG;EAC1B;AACF;AAEAU,MAAM,CAACC,OAAO,GAAGZ,YAAY"}