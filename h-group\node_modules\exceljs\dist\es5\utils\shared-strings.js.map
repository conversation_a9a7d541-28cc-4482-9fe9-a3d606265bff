{"version": 3, "file": "shared-strings.js", "names": ["SharedStrings", "constructor", "_values", "_totalRefs", "_hash", "Object", "create", "count", "length", "values", "totalRefs", "getString", "index", "add", "value", "undefined", "push", "module", "exports"], "sources": ["../../../lib/utils/shared-strings.js"], "sourcesContent": ["class SharedStrings {\n  constructor() {\n    this._values = [];\n    this._totalRefs = 0;\n    this._hash = Object.create(null);\n  }\n\n  get count() {\n    return this._values.length;\n  }\n\n  get values() {\n    return this._values;\n  }\n\n  get totalRefs() {\n    return this._totalRefs;\n  }\n\n  getString(index) {\n    return this._values[index];\n  }\n\n  add(value) {\n    let index = this._hash[value];\n    if (index === undefined) {\n      index = this._hash[value] = this._values.length;\n      this._values.push(value);\n    }\n    this._totalRefs++;\n    return index;\n  }\n}\n\nmodule.exports = SharedStrings;\n"], "mappings": ";;AAAA,MAAMA,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAClC;EAEA,IAAIC,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACL,OAAO,CAACM,MAAM;EAC5B;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACP,OAAO;EACrB;EAEA,IAAIQ,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACP,UAAU;EACxB;EAEAQ,SAASA,CAACC,KAAK,EAAE;IACf,OAAO,IAAI,CAACV,OAAO,CAACU,KAAK,CAAC;EAC5B;EAEAC,GAAGA,CAACC,KAAK,EAAE;IACT,IAAIF,KAAK,GAAG,IAAI,CAACR,KAAK,CAACU,KAAK,CAAC;IAC7B,IAAIF,KAAK,KAAKG,SAAS,EAAE;MACvBH,KAAK,GAAG,IAAI,CAACR,KAAK,CAACU,KAAK,CAAC,GAAG,IAAI,CAACZ,OAAO,CAACM,MAAM;MAC/C,IAAI,CAACN,OAAO,CAACc,IAAI,CAACF,KAAK,CAAC;IAC1B;IACA,IAAI,CAACX,UAAU,EAAE;IACjB,OAAOS,KAAK;EACd;AACF;AAEAK,MAAM,CAACC,OAAO,GAAGlB,aAAa"}