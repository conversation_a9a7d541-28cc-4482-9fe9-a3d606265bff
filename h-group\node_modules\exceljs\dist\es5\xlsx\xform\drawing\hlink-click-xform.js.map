{"version": 3, "file": "hlink-click-xform.js", "names": ["BaseXform", "require", "HLinkClickXform", "tag", "render", "xmlStream", "model", "hyperlinks", "rId", "leafNode", "tooltip", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/hlink-click-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass HLinkClickXform extends BaseXform {\n  get tag() {\n    return 'a:hlinkClick';\n  }\n\n  render(xmlStream, model) {\n    if (!(model.hyperlinks && model.hyperlinks.rId)) {\n      return;\n    }\n    xmlStream.leafNode(this.tag, {\n      'xmlns:r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',\n      'r:id': model.hyperlinks.rId,\n      tooltip: model.hyperlinks.tooltip,\n    });\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          hyperlinks: {\n            rId: node.attributes['r:id'],\n            tooltip: node.attributes.tooltip,\n          },\n        };\n        return true;\n      default:\n        return true;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = HLinkClickXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,eAAe,SAASF,SAAS,CAAC;EACtC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAI,EAAEA,KAAK,CAACC,UAAU,IAAID,KAAK,CAACC,UAAU,CAACC,GAAG,CAAC,EAAE;MAC/C;IACF;IACAH,SAAS,CAACI,QAAQ,CAAC,IAAI,CAACN,GAAG,EAAE;MAC3B,SAAS,EAAE,qEAAqE;MAChF,MAAM,EAAEG,KAAK,CAACC,UAAU,CAACC,GAAG;MAC5BE,OAAO,EAAEJ,KAAK,CAACC,UAAU,CAACG;IAC5B,CAAC,CAAC;EACJ;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACV,GAAG;QACX,IAAI,CAACG,KAAK,GAAG;UACXC,UAAU,EAAE;YACVC,GAAG,EAAEI,IAAI,CAACE,UAAU,CAAC,MAAM,CAAC;YAC5BJ,OAAO,EAAEE,IAAI,CAACE,UAAU,CAACJ;UAC3B;QACF,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAK,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGhB,eAAe"}