/* تحسينات خاصة بلوحة التحكم المدمجة */

/* شبكة لوحة التحكم الرئيسية */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
  width: 100%;
}

/* بطاقات الإحصائيات المدمجة */
.dashboard-stat-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.dashboard-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* رأس بطاقة الإحصائيات */
.dashboard-stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.dashboard-stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  font-size: 0.9rem;
}

.dashboard-stat-title {
  font-size: 0.7rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin: 0;
}

/* قيم الإحصائيات */
.dashboard-stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0.3rem 0;
  line-height: 1;
}

.dashboard-stat-subtitle {
  font-size: 0.65rem;
  color: #6c757d;
  margin: 0;
}

/* مؤشرات الاتجاه */
.dashboard-trend {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-size: 0.6rem;
  font-weight: 600;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
  margin-top: 0.3rem;
}

.dashboard-trend.positive {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.dashboard-trend.negative {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.dashboard-trend.neutral {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* بطاقات الأنشطة الحديثة */
.recent-activities-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 0.75rem;
}

.recent-activities-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.4rem;
  border-bottom: 1px solid #f8f9fa;
}

.recent-activities-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: white;
  background: #6c757d;
}

.activity-icon.order {
  background: #27ae60;
}

.activity-icon.customer {
  background: #3498db;
}

.activity-icon.material {
  background: #f39c12;
}

.activity-icon.worker {
  background: #9b59b6;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 0.7rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.1rem 0;
}

.activity-subtitle {
  font-size: 0.6rem;
  color: #6c757d;
  margin: 0;
}

.activity-time {
  font-size: 0.6rem;
  color: #6c757d;
}

/* بطاقات التنبيهات */
.alerts-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 0.75rem;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.6rem;
  border-radius: 6px;
  margin-bottom: 0.4rem;
  font-size: 0.7rem;
}

.alert-item:last-child {
  margin-bottom: 0;
}

.alert-item.warning {
  background: rgba(243, 156, 18, 0.1);
  border: 1px solid rgba(243, 156, 18, 0.2);
  color: #f39c12;
}

.alert-item.danger {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.alert-item.info {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.alert-icon {
  font-size: 0.8rem;
}

/* بطاقات الرسوم البيانية المدمجة */
.chart-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 0.75rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.chart-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.chart-container {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 6px;
  color: #6c757d;
  font-size: 0.7rem;
}

/* أزرار سريعة */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  width: 100%;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  padding: 0.6rem;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-decoration: none;
  color: #2c3e50;
  transition: all 0.2s ease;
  font-size: 0.7rem;
  text-align: center;
}

.quick-action-btn:hover {
  background: #1e3c72;
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.quick-action-icon {
  font-size: 1.2rem;
  color: #1e3c72;
  transition: color 0.2s ease;
}

.quick-action-btn:hover .quick-action-icon {
  color: white;
}

.quick-action-text {
  font-weight: 600;
  margin: 0;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
  }

  .dashboard-stat-card {
    padding: 0.6rem;
  }

  .dashboard-stat-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .dashboard-stat-value {
    font-size: 1.2rem;
  }

  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.4rem;
  }

  .quick-action-btn {
    padding: 0.5rem;
    font-size: 0.65rem;
  }

  .quick-action-icon {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 0.4rem;
  }

  .dashboard-stat-card {
    padding: 0.5rem;
  }

  .dashboard-stat-icon {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .dashboard-stat-value {
    font-size: 1rem;
  }

  .dashboard-stat-title {
    font-size: 0.65rem;
  }

  .dashboard-stat-subtitle {
    font-size: 0.6rem;
  }

  .activity-item {
    padding: 0.3rem 0;
  }

  .activity-icon {
    width: 20px;
    height: 20px;
    font-size: 0.6rem;
  }

  .activity-title {
    font-size: 0.65rem;
  }

  .activity-subtitle,
  .activity-time {
    font-size: 0.55rem;
  }
}
