const React = require('react');
const { useState, useEffect } = React;

const CostPrediction = ({ productId, specifications, onApplyPrediction }) => {
  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedMaterials, setSelectedMaterials] = useState([]);

  // تحميل التنبؤ بالتكاليف عند تغيير المنتج أو المواصفات
  useEffect(() => {
    const fetchPrediction = async () => {
      if (!productId && !specifications) return;

      try {
        setLoading(true);
        setError(null);

        const data = await window.api.orders.predictCost(productId, specifications);
        setPrediction(data);

        // إعداد المواد المقترحة
        if (data.suggested_materials && data.suggested_materials.length > 0) {
          const materials = data.suggested_materials.map(material => ({
            ...material,
            selected: false,
            quantity: Math.round(material.avg_quantity * 100) / 100
          }));

          setSelectedMaterials(materials);
        }
      } catch (error) {
        console.error('خطأ في التنبؤ بالتكاليف:', error);
        setError('حدث خطأ أثناء التنبؤ بالتكاليف. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchPrediction();
  }, [productId, specifications]);

  // تحديد/إلغاء تحديد مادة
  const handleToggleMaterial = (index) => {
    const newMaterials = [...selectedMaterials];
    newMaterials[index].selected = !newMaterials[index].selected;
    setSelectedMaterials(newMaterials);
  };

  // تغيير كمية مادة
  const handleQuantityChange = (index, value) => {
    const quantity = parseFloat(value);

    if (isNaN(quantity) || quantity <= 0) return;

    const newMaterials = [...selectedMaterials];
    newMaterials[index].quantity = quantity;
    setSelectedMaterials(newMaterials);
  };

  // تطبيق التنبؤ
  const handleApplyPrediction = () => {
    if (!prediction) return;

    // إعداد المواد المحددة
    const materials = selectedMaterials
      .filter(material => material.selected)
      .map(material => ({
        material_id: material.id,
        material_name: material.name,
        unit: material.unit,
        quantity: material.quantity,
        cost_per_unit: material.cost_per_unit,
        total_cost: material.quantity * material.cost_per_unit
      }));

    // إعداد بيانات التكاليف
    const costs = {
      materials,
      worker_fee: prediction.predicted_costs.worker_fee,
      factory_fee: prediction.predicted_costs.factory_fee,
      designer_fee: prediction.predicted_costs.designer_fee,
      owner_margin: prediction.predicted_costs.owner_margin
    };

    // استدعاء دالة التطبيق
    onApplyPrediction(costs);
  };

  if (loading) {
    return <div className="loading">جاري التنبؤ بالتكاليف...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (!prediction) {
    return null;
  }

  return (
    <div className="cost-prediction-container">
      <div className="cost-prediction-header">
        <div className="cost-prediction-title">التنبؤ بالتكاليف</div>
        <div className="cost-prediction-accuracy">
          {prediction.similar_orders_count > 0 ? (
            <span>بناءً على {prediction.similar_orders_count} طلبات مشابهة</span>
          ) : (
            <span>بناءً على الإعدادات الافتراضية</span>
          )}
        </div>
      </div>

      <div className="cost-prediction-details">
        <div className="cost-prediction-item">
          <div className="cost-prediction-item-label">تكلفة المواد</div>
          <div className="cost-prediction-item-value">
            {prediction.predicted_costs.materials_cost.toLocaleString()} ر.س
          </div>
        </div>

        <div className="cost-prediction-item">
          <div className="cost-prediction-item-label">أجور العمال</div>
          <div className="cost-prediction-item-value">
            {prediction.predicted_costs.worker_fee.toLocaleString()} ر.س
          </div>
        </div>

        <div className="cost-prediction-item">
          <div className="cost-prediction-item-label">تكاليف المصنع</div>
          <div className="cost-prediction-item-value">
            {prediction.predicted_costs.factory_fee.toLocaleString()} ر.س
          </div>
        </div>

        <div className="cost-prediction-item">
          <div className="cost-prediction-item-label">أجور التصميم</div>
          <div className="cost-prediction-item-value">
            {prediction.predicted_costs.designer_fee.toLocaleString()} ر.س
          </div>
        </div>

        <div className="cost-prediction-item">
          <div className="cost-prediction-item-label">هامش الربح</div>
          <div className="cost-prediction-item-value">
            {prediction.predicted_costs.owner_margin.toLocaleString()} ر.س
          </div>
        </div>

        <div className="cost-prediction-item">
          <div className="cost-prediction-item-label">التكلفة الإجمالية</div>
          <div className="cost-prediction-item-value">
            {prediction.predicted_costs.total_cost.toLocaleString()} ر.س
          </div>
        </div>

        <div className="cost-prediction-item">
          <div className="cost-prediction-item-label">السعر النهائي</div>
          <div className="cost-prediction-item-value">
            {prediction.predicted_costs.final_price.toLocaleString()} ر.س
          </div>
        </div>
      </div>

      {selectedMaterials.length > 0 && (
        <div className="suggested-materials">
          <div className="suggested-materials-title">المواد المقترحة</div>

          <div className="table-responsive">
            <table className="table">
              <thead>
                <tr>
                  <th>اختيار</th>
                  <th>المادة</th>
                  <th>الوحدة</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>الإجمالي</th>
                </tr>
              </thead>
              <tbody>
                {selectedMaterials.map((material, index) => (
                  <tr key={material.id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={material.selected}
                        onChange={() => handleToggleMaterial(index)}
                      />
                    </td>
                    <td>{material.name}</td>
                    <td>{material.unit}</td>
                    <td>
                      <input
                        type="number"
                        className="form-control form-control-sm"
                        value={material.quantity}
                        onChange={(e) => handleQuantityChange(index, e.target.value)}
                        min="0.01"
                        step="0.01"
                      />
                    </td>
                    <td>{material.cost_per_unit.toLocaleString()} ر.س</td>
                    <td>{(material.quantity * material.cost_per_unit).toLocaleString()} ر.س</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      <div className="mt-3">
        <button
          className="btn btn-primary"
          onClick={handleApplyPrediction}
        >
          <i className="fas fa-check"></i> تطبيق التنبؤ
        </button>
      </div>
    </div>
  );
};

module.exports = CostPrediction;
