{"version": 3, "file": "vml-position-xform.js", "names": ["BaseXform", "require", "VmlPositionXform", "constructor", "model", "_model", "tag", "render", "xmlStream", "type", "leafNode", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/comment/style/vml-position-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\n\nclass VmlPositionXform extends BaseXform {\n  constructor(model) {\n    super();\n    this._model = model;\n  }\n\n  get tag() {\n    return this._model && this._model.tag;\n  }\n\n  render(xmlStream, model, type) {\n    if (model === type[2]) {\n      xmlStream.leafNode(this.tag);\n    } else if (this.tag === 'x:SizeWithCells' && model === type[1]) {\n      xmlStream.leafNode(this.tag);\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {};\n        this.model[this.tag] = true;\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = VmlPositionXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE7C,MAAMC,gBAAgB,SAASF,SAAS,CAAC;EACvCG,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,MAAM,GAAGD,KAAK;EACrB;EAEA,IAAIE,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,GAAG;EACvC;EAEAC,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAEK,IAAI,EAAE;IAC7B,IAAIL,KAAK,KAAKK,IAAI,CAAC,CAAC,CAAC,EAAE;MACrBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,CAAC;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,iBAAiB,IAAIF,KAAK,KAAKK,IAAI,CAAC,CAAC,CAAC,EAAE;MAC9DD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,CAAC;IAC9B;EACF;EAEAK,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACP,GAAG;QACX,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAACA,KAAK,CAAC,IAAI,CAACE,GAAG,CAAC,GAAG,IAAI;QAC3B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAQ,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGf,gBAAgB"}